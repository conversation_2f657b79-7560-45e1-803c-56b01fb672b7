from PyQt6.QtWidgets import QProgressBar
from PyQt6.QtGui import QPainter, QColor, QPainterPath, QPen, QLinearGradient, QFont
from PyQt6.QtCore import Qt, QRectF, pyqtProperty

class BarraTotal(QProgressBar):
    """
    Barra de progreso personalizada simple con bordes redondeados y efecto 3D
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTextVisible(False)
        self.setStyleSheet("""
            QProgressBar {
                background-color: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        # Color por defecto
        self.base_color = QColor("#0078d7")  # Color base azul Microsoft
        self.border_radius = 10            # Radio de borde exactamente igual al fondo
        self.use_3d_effect = True          # Habilitar efecto 3D por defecto
        self.show_percentage = True        # Nueva propiedad para controlar la visibilidad del porcentaje

    def setBaseColor(self, color):
        """Establece el color base de la barra de progreso"""
        try:
            if isinstance(color, str):
                self.base_color = QColor(color)
            elif isinstance(color, QColor):
                self.base_color = color
            else:
                self.base_color = QColor("#0078d7")  # Color fallback
            self.update()
        except Exception as e:
            print(f"Error al establecer color: {e}")
            self.base_color = QColor("#0078d7")  # Color fallback

    def setBorderRadius(self, radius):
        """Establece el radio de borde redondeado"""
        self.border_radius = radius
        self.update()
    
    def set3DEffect(self, enabled):
        """Habilita o deshabilita el efecto 3D"""
        self.use_3d_effect = enabled
        self.update()
    
    def setShowPercentage(self, show):
        """Controla si se muestra o no el porcentaje"""
        self.show_percentage = show
        self.update()
    
    # Propiedad para animación si se requiere
    @pyqtProperty(QColor)
    def color(self):
        return self.base_color
        
    @color.setter
    def color(self, color):
        self.setBaseColor(color)

    def paintEvent(self, event):
        """Dibuja la barra de progreso personalizada con efecto 3D"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        width = self.width()
        height = self.height()
        
        # Primero dibujamos el contenedor con bordes redondeados
        container_path = QPainterPath()
        container_path.addRoundedRect(QRectF(0, 0, width, height), self.border_radius, self.border_radius)
        
        # Color base con opacidad 80%
        base_color = QColor(self.base_color)
        base_color.setAlphaF(0.8)
        
        # Calcular el ancho de la barra según el progreso
        progress = self.value() / (self.maximum() - self.minimum()) if (self.maximum() - self.minimum()) > 0 else 0
        progress_width = int(width * progress)
        
        # Si hay progreso, dibujar el rectángulo de progreso
        if progress_width > 0:
            # Crear y dibujar el rectángulo de progreso (cuadrado, no redondeado)
            progress_path = QPainterPath()
            progress_path.addRect(0, 0, progress_width, height)
            
            # Intersectar el rectángulo de progreso con el contenedor redondeado
            progress_path = progress_path.intersected(container_path)
            
            if self.use_3d_effect:
                # Crear gradiente para efecto 3D
                gradient = QLinearGradient(0, 0, 0, height)
                
                # Versión más brillante del color base para la parte superior
                bright_color = QColor(self.base_color)
                h, s, v, a = bright_color.getHsv()
                bright_color.setHsv(h, s, min(v + 30, 255), a)
                
                # Versión más oscura del color base para la parte inferior
                dark_color = QColor(self.base_color)
                h, s, v, a = dark_color.getHsv()
                dark_color.setHsv(h, s, max(v - 20, 0), a)
                
                gradient.setColorAt(0, bright_color)
                gradient.setColorAt(1, dark_color)
                
                # Dibujar el progreso con gradiente
                painter.fillPath(progress_path, gradient)
                
                # Añadir efecto de brillo en la parte superior (cuadrado, no redondeado)
                shine_gradient = QLinearGradient(0, 0, 0, height * 0.5)
                shine_gradient.setColorAt(0, QColor(255, 255, 255, 160))  # Brillo superior
                shine_gradient.setColorAt(0.3, QColor(255, 255, 255, 80))  # Transición media
                shine_gradient.setColorAt(0.5, QColor(255, 255, 255, 0))   # Desvanecimiento
                
                # Crear path para el brillo (cuadrado, siguiendo el progreso)
                highlight_path = QPainterPath()
                highlight_path.addRect(QRectF(0, 0, progress_width, height * 0.5))
                
                # Intersectar con el progreso para mantener los bordes correctos
                highlight_path = highlight_path.intersected(progress_path)
                
                # Dibujar el brillo
                painter.fillPath(highlight_path, shine_gradient)
                
                # Añadir sombra sutil en la parte inferior (cuadrada, siguiendo el progreso)
                shadow_gradient = QLinearGradient(0, height * 0.7, 0, height)
                shadow_gradient.setColorAt(0, QColor(0, 0, 0, 0))
                shadow_gradient.setColorAt(1, QColor(0, 0, 0, 40))
                
                shadow_path = QPainterPath()
                shadow_path.addRect(QRectF(0, height * 0.7, progress_width, height * 0.3))
                
                # Intersectar con el progreso para mantener los bordes correctos
                shadow_path = shadow_path.intersected(progress_path)
                
                painter.fillPath(shadow_path, shadow_gradient)
            else:
                # Dibujar el progreso con color plano (modo original)
                painter.fillPath(progress_path, base_color)
        
        # Mostrar siempre '0%' si show_percentage está activado, incluso cuando value == 0
        if self.show_percentage:
            text = f"{self.value()}%"
            painter.setPen(QColor(255, 255, 255))  # Color blanco para el texto
            font = QFont()
            font.setBold(True)
            font.setPointSize(10)  # TAMAÑO DEL PORCIENTO
            painter.setFont(font)

            font_metrics = painter.fontMetrics()
            text_width = font_metrics.horizontalAdvance(text)
            text_height = font_metrics.height()

            text_x = (width - text_width) / 2
            text_y = (height + text_height) / 2 - font_metrics.descent()

            painter.drawText(int(text_x), int(text_y), text)

        painter.end()

    def resizeEvent(self, event):
        """Mantener la posición relativa de las barras durante el redimensionamiento"""
        super().resizeEvent(event)

