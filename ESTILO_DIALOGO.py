from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QGraphicsDropShadowEffect, QWidget
)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QPoint
from PyQt6.QtGui import QColor, QFont
from APARIENCIA import apply_acrylic_and_rounded

class MacLikeDialog(QDialog):
    def __init__(self, parent=None, title="", message="", icon_path=None):
        super().__init__(parent)
        
        # Configuración básica de la ventana
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setModal(True)
        
        # Aplicar efecto acrílico y bordes redondeados
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd)
        
        # Layout principal
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Contenedor principal con fondo semi-transparente
        self.container = QWidget()
        self.container.setObjectName("container")
        container_layout = QVBoxLayout(self.container)
        container_layout.setContentsMargins(20, 20, 20, 20)
        container_layout.setSpacing(15)
        
        # Título
        title_label = QLabel(title)
        title_label.setObjectName("title")
        title_font = QFont()
        title_font.setPointSize(13)
        title_font.setBold(True)
        title_label.setFont(title_font)
        container_layout.addWidget(title_label, 0, Qt.AlignmentFlag.AlignCenter)
        
        # Mensaje
        message_label = QLabel(message)
        message_label.setObjectName("message")
        message_label.setWordWrap(True)
        message_font = QFont()
        message_font.setPointSize(11)
        message_label.setFont(message_font)
        container_layout.addWidget(message_label, 0, Qt.AlignmentFlag.AlignCenter)
        
        # Botones
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.ok_button = QPushButton("Aceptar")
        self.ok_button.setObjectName("okButton")
        self.ok_button.clicked.connect(self.accept)
        self.ok_button.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.ok_button)
        
        container_layout.addLayout(button_layout)
        layout.addWidget(self.container)
        
        # Aplicar sombra
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 50))
        self.container.setGraphicsEffect(shadow)
        
        # Estilos
        self.setStyleSheet("""
            #container {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
            }
            #title {
                color: white;
                font-weight: bold;
            }
            #message {
                color: rgba(255, 255, 255, 0.8);
            }
            #okButton {
                background-color: #007AFF;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
                font-weight: bold;
                min-width: 80px;
            }
            #okButton:hover {
                background-color: #0069D9;
            }
            #okButton:pressed {
                background-color: #0051A8;
            }
        """)

    def showEvent(self, event):
        super().showEvent(event)
        # Animación de entrada
        self.container.setGraphicsEffect(None)  # Temporalmente removemos la sombra para la animación
        self.container.setGeometry(self.container.x(), self.container.y() - 50, 
                                 self.container.width(), self.container.height())
        
        # Animación de posición
        self.anim = QPropertyAnimation(self.container, b"pos")
        self.anim.setDuration(200)
        self.anim.setEndValue(QPoint(self.container.x(), self.container.y() + 50))
        self.anim.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # Animación de opacidad
        self.fade = QPropertyAnimation(self, b"windowOpacity")
        self.fade.setDuration(200)
        self.fade.setStartValue(0)
        self.fade.setEndValue(1)
        self.fade.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # Iniciar animaciones
        self.anim.start()
        self.fade.start()
        
        # Restaurar sombra después de la animación
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 50))
        self.container.setGraphicsEffect(shadow) 