from PyQt6.QtWidgets import QLabel, QToolTip
from PyQt6.QtGui import Q<PERSON><PERSON>ter, QColor, QLinearGradient, QPen, QPainterPath
from PyQt6.QtCore import Qt, QPropertyAnimation, QPoint, QTimer, QRectF, QEasingCurve

# Variable global para almacenar referencia al tooltip activo
_active_tooltip = None
_last_tooltip_text = ""
_last_tooltip_pos = None
_tooltip_update_timer = None

class CustomToolTip(QLabel):
    """
    Un tooltip personalizado con bordes redondeados y apariencia moderna.
    Similar al estilo de Windows en modo oscuro con fondo gris semitransparente.
    """
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
        self.setStyleSheet("""
            QLabel {
                color: white;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: normal;
                font-family: Segoe UI, Arial, sans-serif;
            }
        """)
        
        # Animación de opacidad
        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(150)
        self.opacity_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # Animación de escala
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(150)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def paintEvent(self, event):
        if self.isVisible():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
            
            # Crear path con bordes redondeados
            path = QPainterPath()
            rect = QRectF(self.rect()).adjusted(1, 1, -1, -1)
            radius = 8.0  # Radio fijo para las esquinas
            path.addRoundedRect(rect, radius, radius)
            
            # Dibujar sombra sutil
            painter.setPen(Qt.PenStyle.NoPen)
            for i in range(3):
                shadow_path = QPainterPath()
                shadow_rect = rect.adjusted(-i, -i, i, i)
                shadow_path.addRoundedRect(shadow_rect, radius, radius)
                painter.setBrush(QColor(0, 0, 0, 10 - i * 2))
                painter.drawPath(shadow_path)
            
            # Dibujar fondo con gradiente (estilo Windows modo oscuro)
            gradient = QLinearGradient(0, 0, 0, self.height())
            gradient.setColorAt(0.0, QColor(44, 44, 44, 245))  # Gris oscuro semi-transparente
            gradient.setColorAt(1.0, QColor(32, 32, 32, 245))  # Ligeramente más oscuro en la parte inferior
            painter.setBrush(gradient)
            painter.drawPath(path)
            
            # Dibujar borde sutil
            pen = QPen()
            pen.setColor(QColor(255, 255, 255, 20))  # Borde blanco muy sutil
            pen.setWidth(1)
            painter.setPen(pen)
            painter.drawPath(path)
            
            # Renderizar el texto
            super().paintEvent(event)
    
    def hideEvent(self, event):
        self.opacity_animation.stop()
        self.scale_animation.stop()
        super().hideEvent(event)
        
        # Limpiar referencia global cuando se cierra
        global _active_tooltip
        if _active_tooltip == self:
            _active_tooltip = None
    
    def show_tooltip(self, pos):
        self.adjustSize()
        final_pos = pos + QPoint(-self.width() // 2, 35)
        self.move(final_pos)
        self.show()
    
    def update_position(self, pos):
        """Actualiza la posición del tooltip sin recrearlo"""
        final_pos = pos + QPoint(-self.width() // 2, 35)
        self.move(final_pos)

def showCustomTooltip(text, pos, parent=None, duration=3000):
    """
    Muestra un tooltip personalizado en la posición indicada.
    
    Args:
        text (str): Texto a mostrar en el tooltip
        pos (QPoint): Posición donde mostrar el tooltip
        parent (QWidget): Widget padre (opcional)
        duration (int): Duración en milisegundos (por defecto 3000ms)
    """
    global _active_tooltip, _last_tooltip_text, _last_tooltip_pos, _tooltip_update_timer
    
    # Si el tooltip ya está visible y es el mismo texto, solo actualizar la posición
    if _active_tooltip and _active_tooltip.isVisible() and _last_tooltip_text == text:
        _active_tooltip.update_position(pos)
        _last_tooltip_pos = pos
        return
    
    # Ocultar cualquier tooltip activo
    if _active_tooltip and _active_tooltip.isVisible():
        _active_tooltip.hide()
        _active_tooltip = None
    
    # Detener timer anterior si existe
    if _tooltip_update_timer:
        _tooltip_update_timer.stop()
    
    # Ocultar tooltip estándar de Qt
    QToolTip.hideText()
    
    # Crear y mostrar el nuevo tooltip
    _active_tooltip = CustomToolTip(text, parent)
    _active_tooltip.show_tooltip(pos)
    _last_tooltip_text = text
    _last_tooltip_pos = pos
    
    # Configurar temporizador para ocultarlo después de duration ms
    _tooltip_update_timer = QTimer()
    _tooltip_update_timer.setSingleShot(True)
    _tooltip_update_timer.timeout.connect(lambda: _hide_active_tooltip())
    _tooltip_update_timer.start(duration)

def _hide_active_tooltip():
    """Función interna para ocultar el tooltip activo"""
    global _active_tooltip, _last_tooltip_text, _last_tooltip_pos
    if _active_tooltip:
        _active_tooltip.hide()
        _active_tooltip = None
        _last_tooltip_text = ""
        _last_tooltip_pos = None

def showTooltipAtCursor(text, duration=3000, parent=None):
    """
    Muestra un tooltip personalizado en la posición actual del cursor.
    
    Args:
        text (str): Texto a mostrar en el tooltip
        duration (int): Duración en milisegundos (por defecto 3000ms)
        parent (QWidget): Widget padre (opcional)
    """
    from PyQt6.QtGui import QCursor
    showCustomTooltip(text, QCursor.pos(), parent, duration)

def showTooltipAtWidget(text, widget, duration=3000):
    """
    Muestra un tooltip personalizado centrado en un widget.
    
    Args:
        text (str): Texto a mostrar en el tooltip
        widget (QWidget): Widget sobre el que centrar el tooltip
        duration (int): Duración en milisegundos (por defecto 3000ms)
    """
    center_pos = widget.mapToGlobal(widget.rect().center())
    showCustomTooltip(text, center_pos, widget, duration)
