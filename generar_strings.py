from cryptography.fernet import <PERSON><PERSON><PERSON>

def generar_strings():
    key = Fernet.generate_key()
    f = Fernet(key)
    
    strings = {
        'APP_NAME': 'ZETACOPY',
        'LICENSE_KEY': 'licencia-123',
        'ERROR_MSG': 'Error iniciando aplicacion'
    }
    
    print(f"_k = {key}")
    print("\n_STRINGS = {")
    for name, value in strings.items():
        encrypted = f.encrypt(value.encode())
        print(f"    '{name}': {encrypted},")
    print("}")

if __name__ == '__main__':
    generar_strings() 