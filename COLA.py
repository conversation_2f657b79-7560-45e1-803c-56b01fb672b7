from PyQt6.QtWidgets import QApplication, QAbstractItemView, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem, QWidget, QSizeGrip, QSpacerItem, QSizePolicy, QLabel, QPushButton, QGraphicsDropShadowEffect, QTableWidget, QTableWidgetItem, QHeaderView
from PyQt6.QtCore import Qt, QSize, QPropertyAnimation, pyqtProperty, QPoint, pyqtSignal, QEvent
from PyQt6.QtGui import QPixmap, QIcon, QKeyEvent, QColor
import sys
import os
import ctypes
from threading import Lock
from REDIMENSIONAR import WindowResizer
from CREAR import CustomCloseButton, CustomMaximizeButton, CustomMinimizeButton

class AnimatedButton(QPushButton):
    def __init__(self, icon_path, parent=None, is_window_control=False):
        super().__init__(parent)
        self.setIcon(QIcon(icon_path))
        self.is_window_control = is_window_control
        self._icon_size = 25
        self.setIconSize(QSize(25, 25))
        self.setFixedSize(35, 35)
        self.setStyleSheet("""
            QPushButton {
                border: none;
                background: transparent;
                width: 35px;
                height: 35px;
                min-width: 35px;
                min-height: 35px;
                max-width: 35px;
                max-height: 35px;
            }
        """)
        AnimatedButton.add_shadow_effect(self, Qt.GlobalColor.black)
        self.setMouseTracking(True)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.update()

    @staticmethod
    def add_shadow_effect(widget, color):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

    @pyqtProperty(int)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.setIconSize(QSize(size, size))

    def enterEvent(self, event):
        self.animate_icon_size(30)

    def leaveEvent(self, event):
        self.animate_icon_size(25)

    def animate_icon_size(self, target_size):
        self.animation = QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(50)
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

    def reset_icon_size(self):
        self._icon_size = 25
        self.setIconSize(QSize(25, 25))
        self.update()

class ColaWindow(QWidget):
    delete_files_signal = pyqtSignal(list, str)
    def __init__(self, files_in_queue, selected_drive, main_window):
        super().__init__()
        self.windll = ctypes.windll
        self.main_window = main_window
        self.selected_drive = selected_drive
        self.files_in_queue = files_in_queue
        self.setWindowTitle(f"Archivos en Cola - Disco {selected_drive}")
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.Tool |
            Qt.WindowType.NoDropShadowWindowHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.setAttribute(Qt.WidgetAttribute.WA_AlwaysStackOnTop, False)
        if main_window:
            main_geo = main_window.geometry()
            pos = self.calculate_window_position()
            self.setGeometry(
                pos.x(),
                pos.y(),
                800,
                main_geo.height()
            )
        else:
            self.setGeometry(200, 200, 800, 600)
        self.window_resizer = WindowResizer(self)
        hwnd = int(self.winId())
        self.set_rounded_corners(hwnd)
        self.dock_widget = QWidget(self)
        self.table_widget = QTableWidget(self)
        self.background_widget = QWidget(self)
        self.background_widget.setStyleSheet("""
            QWidget {
                background-color: #202020;
                border-radius: 10px;
            }
        """)
        self.background_widget.setGeometry(self.rect())
        self.background_widget.lower()
        self.setStyleSheet("""
            QWidget {
                background: #202020;
                color: white;
            }
        """)
        self.dock_widget.setStyleSheet("""
            QWidget {
                background: #202020;
                border: none;
                border-radius: 10px;
            }
        """)
        self.table_widget.setStyleSheet("""
            QTableWidget {
                background: #202020;
                border: none;
                color: white;
                gridline-color: rgba(255, 255, 255, 30);
            }
            QTableWidget::item {
                padding: 5px;
                background: #202020;
            }
            QTableWidget::item:selected {
                background: #404040;
            }
            QHeaderView::section {
                background-color: #303030;
                color: white;
                padding: 5px;
                border: none;
                font-weight: bold;
            }
            QScrollBar:vertical {
                border: none;
                background: #303030;
                width: 8px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #505050;
                min-height: 20px;
                border-radius: 4px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        self.is_maximized = False
        self.dragging = False
        dock_layout = QHBoxLayout(self.dock_widget)
        dock_layout.setContentsMargins(5, 5, 5, 5)
        dock_layout.setSpacing(2)
        dock_layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        self.minimize_button = CustomMinimizeButton(self.dock_widget)
        self.minimize_button.clicked.connect(self.showMinimized)
        self.minimize_button.setToolTip("Minimizar")
        self.maximize_button = CustomMaximizeButton(self.dock_widget)
        self.maximize_button.clicked.connect(self.toggle_maximize_restore)
        self.maximize_button.setToolTip("Maximizar/Restaurar")
        self.close_button = CustomCloseButton(self.dock_widget)
        self.close_button.clicked.connect(self.close)
        self.close_button.setToolTip("Cerrar")
        dock_layout.addWidget(self.minimize_button, 0, Qt.AlignmentFlag.AlignRight)
        dock_layout.addWidget(self.maximize_button, 0, Qt.AlignmentFlag.AlignRight)
        dock_layout.addWidget(self.close_button, 0, Qt.AlignmentFlag.AlignRight)
        self.dock_widget.adjustSize()
        self.transparent_widget = QWidget(self)
        self.transparent_widget.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
            }
        """)
        transparent_layout = QVBoxLayout(self.transparent_widget)
        transparent_layout.setContentsMargins(10, 0, 10, 10)
        self.table_widget = QTableWidget(self.transparent_widget)
        self.table_widget.setColumnCount(3)
        self.table_widget.setHorizontalHeaderLabels(["Tamaño", "Origen", "Destino"])
        self.table_widget.setStyleSheet("""
            QTableWidget {
                background: transparent;
                border: none;
                color: white;
                gridline-color: rgba(255, 255, 255, 30);
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background: rgba(255, 255, 255, 20);
            }
            QHeaderView::section {
                background-color: #303030;
                color: white;
                padding: 5px;
                border: none;
                font-weight: bold;
            }
            QScrollBar:vertical {
                border: none;
                background: #303030;
                width: 8px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #505050;
                min-height: 20px;
                border-radius: 4px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        self.table_widget.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table_widget.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.table_widget.verticalHeader().setVisible(False)
        self.table_widget.setShowGrid(True)
        header = self.table_widget.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        self.table_widget.setColumnWidth(0, 100)
        self.update_queue_view()
        transparent_layout.addWidget(self.table_widget)
        layout.addWidget(self.dock_widget)
        layout.addWidget(self.transparent_widget)
        self.setLayout(layout)
        self.table_widget.keyPressEvent = self.key_press_event
        self.installEventFilter(self)
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 5, 0, 0)  # Margen superior pequeño
        self.delete_button = QPushButton("BORRAR")
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                border: none;
                border-radius: 16px;
                padding: 5px 15px;
                font-weight: bold;
                min-width: 60px;
                height: 24px;
            }
            QPushButton:hover {
                background-color: #ef5350;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
            QPushButton:disabled {
                background-color: #666666;
            }
        """)
        self.delete_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.delete_button.clicked.connect(self.delete_selected_files)
        shadow = QGraphicsDropShadowEffect(self.delete_button)
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 160))
        self.delete_button.setGraphicsEffect(shadow)
        button_layout.addWidget(self.delete_button, alignment=Qt.AlignmentFlag.AlignRight)
        transparent_layout.addLayout(button_layout)
        self.table_widget.itemSelectionChanged.connect(self.update_delete_button_state)
        self.delete_button.setEnabled(False)

    def update_queue_view(self):
        self.table_widget.setRowCount(0)
        if self.selected_drive in self.main_window.queues:
            queue_items = list(self.main_window.queues[self.selected_drive].queue)
            self.table_widget.setRowCount(len(queue_items))
            for row, (file_path, destination_path) in enumerate(queue_items):
                try:
                    if os.path.isfile(file_path):
                        size = os.path.getsize(file_path)
                    elif os.path.isdir(file_path):
                        size = sum(
                            os.path.getsize(os.path.join(root, file))
                            for root, _, files in os.walk(file_path)
                            for file in files
                        )
                    else:
                        size = 0
                    size_item = QTableWidgetItem(self.format_size(size))
                    source_item = QTableWidgetItem(file_path)
                    dest_item = QTableWidgetItem(destination_path)
                    size_item.setData(Qt.ItemDataRole.UserRole, (file_path, destination_path))
                    self.table_widget.setItem(row, 0, size_item)
                    self.table_widget.setItem(row, 1, source_item)
                    self.table_widget.setItem(row, 2, dest_item)
                except Exception as e:
                    print(f"Error al procesar archivo {file_path}: {e}")

    def key_press_event(self, event):
        if event.key() == Qt.Key.Key_Delete:
            self.delete_selected_files()
        else:
            super(QTableWidget, self.table_widget).keyPressEvent(event)

    def format_size(self, size):
        """
        Formatea el tamaño usando la misma lógica que Windows Explorer con redondeo preciso
        """
        KB = 1024
        MB = KB * 1024
        GB = MB * 1024
        TB = GB * 1024
        if size < KB:
            return f"{size} B"
        elif size < MB:
            value = size / KB
            return f"{value:.2f} KB"  # Aumentamos precisión a 2 decimales
        elif size < GB:
            value = size / MB
            return f"{value:.2f} MB"  # Aumentamos precisión a 2 decimales
        elif size < TB:
            value = size / GB
            return f"{value:.3f} GB"  # Aumentamos precisión a 3 decimales para GB
        else:
            value = size / TB
            return f"{value:.2f} TB"  # 2 decimales para TB

    def delete_selected_files(self):
        selected_rows = self.table_widget.selectedItems()
        if not selected_rows:
            print("No hay archivos seleccionados para eliminar")
            return
            
        files_to_delete = []
        processed_rows = set()
        
        for item in selected_rows:
            row = item.row()
            if row in processed_rows:
                continue
            processed_rows.add(row)
            
            file_data = self.table_widget.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if isinstance(file_data, tuple) and len(file_data) >= 2:
                source, dest = file_data
                try:
                    if os.path.isfile(source):
                        file_size = os.path.getsize(source)
                    elif os.path.isdir(source):
                        file_size = sum(
                            os.path.getsize(os.path.join(root, file))
                            for root, _, files in os.walk(source)
                            for file in files
                        )
                    else:
                        file_size = 0
                    files_to_delete.append((source, dest, file_size))
                except OSError:
                    print(f"No se pudo obtener el tamaño del archivo: {source}")
                    
        if files_to_delete:
            # Emitir señal para que MainWindow maneje el borrado
            self.delete_files_signal.emit(files_to_delete, self.selected_drive)
            self.update_queue_view()

    def toggle_maximize_restore(self):
        if self.is_maximized:
            self.showNormal()
        else:
            self.showMaximized()
        self.is_maximized = not self.is_maximized

    def mousePressEvent(self, event):
        if not self.window_resizer.handle_mouse_press(event):
            if event.button() == Qt.MouseButton.LeftButton and event.position().toPoint().y() <= 50:
                self.dragging = True
                self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()

    def mouseMoveEvent(self, event):
        if not self.window_resizer.handle_mouse_move(event):
            if self.dragging:
                self.move(event.globalPosition().toPoint() - self.drag_position)

    def mouseReleaseEvent(self, event):
        if not self.window_resizer.handle_mouse_release(event):
            if event.button() == Qt.MouseButton.LeftButton:
                self.dragging = False

    def set_rounded_corners(self, hwnd):
        DWMWA_WINDOW_CORNER_PREFERENCE = 33
        DWMWCP_ROUND = 2
        self.windll.dwmapi.DwmSetWindowAttribute(
            hwnd,
            DWMWA_WINDOW_CORNER_PREFERENCE,
            ctypes.byref(ctypes.c_int(DWMWCP_ROUND)),
            ctypes.sizeof(ctypes.c_int)
        )

    def resizeEvent(self, event):
        if event.size() == event.oldSize():
            return
        super().resizeEvent(event)
        self.setUpdatesEnabled(False)
        try:
            new_width = self.width()
            new_height = self.height()
            if hasattr(self, 'background_widget'):
                self.background_widget.setGeometry(0, 0, new_width, new_height)
            if hasattr(self, 'transparent_widget'):
                content_width = new_width - 20
                content_height = new_height - 60
                if content_width > 0 and content_height > 0:
                    self.transparent_widget.setGeometry(10, 50, content_width, content_height)
            self.window_resizer.update_positions()
        finally:
            self.setUpdatesEnabled(True)
        if hasattr(self, 'table_widget'):
            visible_rows = self.table_widget.viewport().height() // max(1, self.table_widget.rowHeight(0))
            needs_scrollbar = self.table_widget.rowCount() > visible_rows
            current_policy = self.table_widget.verticalScrollBarPolicy()
            new_policy = Qt.ScrollBarPolicy.ScrollBarAsNeeded if needs_scrollbar else Qt.ScrollBarPolicy.ScrollBarAlwaysOff
            if current_policy != new_policy:
                self.table_widget.setVerticalScrollBarPolicy(new_policy)

    def keyPressEvent(self, event: QKeyEvent):
        if event.key() == Qt.Key.Key_Control or event.key() == Qt.Key.Key_Shift:
            self.table_widget.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        super().keyPressEvent(event)

    def keyReleaseEvent(self, event: QKeyEvent):
        if event.key() == Qt.Key.Key_Control or event.key() == Qt.Key.Key_Shift:
            self.table_widget.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        super().keyReleaseEvent(event)

    def closeEvent(self, event):
        """Resetear los botones antes de cerrar la ventana"""
        # Ocultar el tooltip del botón CLOSE antes de cerrar
        if hasattr(self, 'close_button') and hasattr(self.close_button, 'tooltip'):
            self.close_button.tooltip.hide()  # Ocultar el tooltip
            
        # Resetear los botones personalizados
        if hasattr(self, 'minimize_button'):
            self.minimize_button.scale = 1.0
            self.minimize_button.update()
            
        if hasattr(self, 'maximize_button'):
            self.maximize_button.scale = 1.0
            self.maximize_button.update()
            
        if hasattr(self, 'close_button'):
            self.close_button.scale = 1.0
            self.close_button.update()
            
        super().closeEvent(event)

    def eventFilter(self, obj, event):
        return super().eventFilter(obj, event)

    def showEvent(self, event):
        super().showEvent(event)
        if self.main_window:
            main_geo = self.main_window.geometry()
            pos = self.calculate_window_position()
            self.setGeometry(
                pos.x(),
                pos.y(),
                self.width(),
                main_geo.height()
            )
        hwnd = int(self.winId())
        self.set_rounded_corners(hwnd)
        for grip in self.window_resizer.size_grips.values():
            grip.raise_()
            grip.show()

    def focusInEvent(self, event):
        super().focusInEvent(event)

    def update_delete_button_state(self):
        """Habilitar el botón solo si hay elementos seleccionados"""
        self.delete_button.setEnabled(len(self.table_widget.selectedItems()) > 0)

    def calculate_window_position(self):
        """Calcula la mejor posición para la ventana Cola basada en la posición de la ventana principal"""
        if not self.main_window:
            return QPoint(200, 200)
        screen = QApplication.primaryScreen().geometry()
        main_geo = self.main_window.geometry()
        cola_width = 800  # Ancho deseado de la ventana Cola
        spacing = 10      # Espacio entre ventanas
        right_pos = main_geo.right() + spacing
        left_pos = main_geo.left() - cola_width - spacing
        if (right_pos + cola_width) <= screen.right():
            return QPoint(right_pos, main_geo.y())
        elif left_pos >= screen.left():
            return QPoint(left_pos, main_geo.y())
        space_right = screen.right() - main_geo.right()
        space_left = main_geo.left() - screen.left()
        if space_right >= space_left:
            return QPoint(
                screen.right() - cola_width - spacing,
                main_geo.y()
            )
        else:
            return QPoint(
                screen.left() + spacing,
                main_geo.y()
            )

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ColaWindow([], "", None)
    window.show()
    sys.exit(app.exec())
