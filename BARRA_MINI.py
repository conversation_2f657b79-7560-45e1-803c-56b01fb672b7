from PyQt6.QtWidgets import QProgressBar
from PyQt6.QtGui import QPainter, QColor, QPainterPath, QPen, QLinearGradient
from PyQt6.QtCore import Qt, QRectF

class RoundedProgressBar(QProgressBar):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setTextVisible(False)
        self.file_progress = 0  # Inicializa file_progress aquí
        self.set_color("#0078d7")  # Color por defecto

    def set_color(self, color):
        print(f"RoundedProgressBar: Estableciendo color a {color}")
        self.progress_color = QColor(color)  # Guarda el color como un QColor
        self.setStyleSheet(f"""
            QProgressBar {{
                background-color: transparent;
                border: none;
            }}
            QProgressBar::chunk {{
                background-color: {color};
                border-radius: 7px;
            }}
        """)
        self.update()  # Fuerza una actualización del widget

    def setFileProgress(self, value):
        self.file_progress = value
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Dibuja el fondo semi-transparente
        bg_color = QColor(255, 255, 255, 25)
        painter.setBrush(bg_color)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(self.rect(), 7, 7)
        
        # Calcula el ancho de la barra de progreso principal
        progress = self.value() / (self.maximum() - self.minimum())
        width = int(self.width() * progress)
        
        # Dibuja la barra de progreso principal
        if width > 0:
            # Crear gradiente para la barra de progreso
            gradient = QLinearGradient(0, 0, width, 0)
            base_color = self.progress_color
            lighter_color = QColor(base_color)
            lighter_color.setBlue(min(255, lighter_color.blue() + 40))
            gradient.setColorAt(0, base_color)
            gradient.setColorAt(1, lighter_color)
            
            painter.setBrush(gradient)
            if width < 14:  # Si el ancho es menor que el diámetro del círculo
                painter.drawEllipse(0, 0, 14, self.height())
            else:
                path = QPainterPath()
                path.addRoundedRect(QRectF(0, 0, width, self.height()), 7, 7)
                painter.drawPath(path)
                
                # Efecto de brillo más pronunciado para efecto 3D
                shine_gradient = QLinearGradient(0, 0, 0, self.height())
                shine_gradient.setColorAt(0, QColor(255, 255, 255, 160))  # Brillo superior
                shine_gradient.setColorAt(0.3, QColor(255, 255, 255, 80))  # Transición media
                shine_gradient.setColorAt(0.5, QColor(255, 255, 255, 0))   # Desvanecimiento
                shine_gradient.setColorAt(0.7, QColor(0, 0, 0, 20))        # Sombra sutil abajo
                
                # Dibujar el brillo con ajuste más preciso al ancho de la barra
                highlight = QPainterPath()
                highlight_width = width - 2  # Ajustar el ancho del brillo
                highlight.addRoundedRect(
                    QRectF(1, 1, highlight_width, self.height() - 2),
                    6, 6  # Radio de bordes ajustado
                )
                painter.fillPath(highlight, shine_gradient)
        
        # Dibuja el porcentaje de progreso
        if self.value() > 0:
            percentage = f"{self.value()}%"
            painter.setPen(QColor(255, 255, 255))  # Color del texto
            painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, percentage)
        
        # Dibuja la barra de progreso de archivos
        if self.file_progress > 0:
            file_progress_color = QColor(0, 255, 0)  # Color verde para la barra de archivos
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(file_progress_color)

            file_progress_path = QPainterPath()
            file_progress_path.addRoundedRect(QRectF(1, 1, self.width() - 2, self.height() - 2), 6, 6)
            file_progress_path.addRoundedRect(QRectF(3, 3, self.width() - 6, self.height() - 6), 4, 4)
            painter.drawPath(file_progress_path)

            angle = int(360 * self.file_progress / 100)
            painter.setPen(QPen(file_progress_color, 2))
            painter.drawArc(QRectF(1, 1, self.width() - 2, self.height() - 2), 90 * 16, -angle * 16)

        painter.end()