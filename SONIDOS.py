import os
import pygame

class SoundManager:
    """
    Clase para manejar la reproducción de sonidos en la aplicación.
    """
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SoundManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        # Inicializar pygame mixer
        try:
            pygame.mixer.init()
            self._initialized = True
            self.sounds_path = os.path.join(os.path.dirname(__file__), 'SONIDOS')
        except Exception as e:
            print(f"Error al inicializar el sistema de sonido: {e}")
            self._initialized = False
    
    def play_sound(self, sound_type):
        """
        Reproduce un sonido específico.
        Args:
            sound_type (str): Tipo de sonido a reproducir ('button', 'drag', 'txt', 'finish', etc.)
        """
        if not self._initialized:
            return
        try:
            # Mapeo de tipos de sonido a archivos
            sound_files = {
                'button': 'BOTONES.wav',
                'drag': 'SONIDO DE ARRASTRE.mp3',
                'txt': 'TXT.mp3',
                'error': 'SONIDO DE BORRADO.mp3',
                'finish': 'COPIA FINALIZADA.wav'  # Agregamos el nuevo sonido
            }
            
            # Obtener el archivo de sonido correspondiente
            sound_file = sound_files.get(sound_type)
            if not sound_file:
                print(f"Tipo de sonido desconocido: {sound_type}")
                return
            
            # Ruta completa al archivo de sonido
            sound_path = os.path.join(self.sounds_path, sound_file)
            
            # Verificar si el archivo existe
            if not os.path.exists(sound_path):
                print(f"Archivo de sonido no encontrado: {sound_path}")
                return
            
            # Reproducir el sonido
            pygame.mixer.music.load(sound_path)
            pygame.mixer.music.play()
        except Exception as e:
            print(f"Error al reproducir sonido {sound_type}: {e}")

# Crear una instancia global para usar en toda la aplicación
sound_manager = SoundManager()