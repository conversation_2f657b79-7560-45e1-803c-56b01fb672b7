# BORRADO.py
from PyQt6 import <PERSON>t<PERSON><PERSON>, QtWidgets, QtGui
import os
import shutil
import AJUSTES
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame, QLineEdit,
    QWidget, QProgressBar, QGraphicsDropShadowEffect, QToolTip
)
from PyQt6.QtCore import (
    Qt, pyqtSignal, QObject, QThread, QRunnable, QThreadPool, QPropertyAnimation, 
    pyqtProperty, QSize, QTimer, QMetaObject, Q_ARG
)
from PyQt6.QtGui import QPainter, QColor, QPainterPath, QIcon, QPixmap
import ctypes
from APARIENCIA import (
    ACCENT_POLICY, WINDOWCOMPOSITIONATTRIBDATA, 
    ACCENT_ENABLE_ACRYLICBLURBEHIND, WCA_ACCENT_POLICY, 
    apply_acrylic_and_rounded
)
from collections import deque
import time
import threading
from BARRA_ARRASTRE import DragProgressBar
BATCH_SIZE = 100 # Tamaño del lote para procesar archivos
def get_drive_space(drive):
    """Obtiene el espacio libre y total de un disco"""
    free_bytes = ctypes.c_ulonglong(0)
    total_bytes = ctypes.c_ulonglong(0)
    total_free_bytes = ctypes.c_ulonglong(0)
    ctypes.windll.kernel32.GetDiskFreeSpaceExW(
        ctypes.c_wchar_p(drive), 
        ctypes.byref(free_bytes), 
        ctypes.byref(total_bytes), 
        ctypes.byref(total_free_bytes)
    )
    return free_bytes.value, total_bytes.value

class AnimatedButton(QtWidgets.QPushButton):
    def __init__(self, icon_path, parent=None):
        super().__init__(parent)
        self.setIcon(QtGui.QIcon(icon_path))
        self.setIconSize(QtCore.QSize(25, 25))  # Initial icon size
        self.setStyleSheet("border: none; background: transparent;")
        self.setMinimumSize(35, 35)
        self.setMaximumSize(35, 35)
        AnimatedButton.add_shadow_effect(self, QtCore.Qt.GlobalColor.white)
        self.setMouseTracking(True)
        self._icon_size = 25
        self.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)

    @staticmethod
    def add_shadow_effect(widget, color):
        shadow = QtWidgets.QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

    @QtCore.pyqtProperty(int)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.setIconSize(QtCore.QSize(size, size))

    def enterEvent(self, event):
        self.animate_icon_size(30)  # Enlarged icon size

    def leaveEvent(self, event):
        self.animate_icon_size(25)  # Original icon size

    def animate_icon_size(self, target_size):
        self.animation = QtCore.QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(50)  # Duration of the animation in milliseconds
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

class ConfirmDeleteDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.Popup | 
            Qt.WindowType.NoDropShadowWindowHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)

        # Layout principal con margen reducido para un look más moderno
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # Mensaje con estilo mejorado
        message = QLabel("¿ELIMINAR?")
        message.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                padding: 5px;
            }
        """)
        message.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Añadir sombra al texto
        message_shadow = QGraphicsDropShadowEffect()
        message_shadow.setBlurRadius(10)
        message_shadow.setXOffset(0)
        message_shadow.setYOffset(0)
        message_shadow.setColor(QColor(0, 0, 0, 160))
        message.setGraphicsEffect(message_shadow)
        
        layout.addWidget(message)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        ok_button = QPushButton("OK")
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 120, 215, 0.7);
                color: white;
                border: none;
                border-radius: 16px;
                padding: 5px 15px;
                font-weight: bold;
                min-width: 60px;
                height: 24px;
            }
            QPushButton:hover {
                background-color: rgba(30, 144, 255, 0.8);
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: rgba(0, 95, 179, 0.9);
                transform: scale(0.95);
            }
        """)

        cancel_button = QPushButton("CANCELAR")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(211, 47, 47, 0.7);
                color: white;
                border: none;
                border-radius: 16px;
                padding: 5px 15px;
                font-weight: bold;
                min-width: 60px;
                height: 24px;
            }
            QPushButton:hover {
                background-color: rgba(239, 83, 80, 0.8);
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: rgba(183, 28, 28, 0.9);
                transform: scale(0.95);
            }
        """)

        # Agregar sombras mejoradas a los botones
        for button in [ok_button, cancel_button]:
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(15)  # Aumentado para más profundidad
            shadow.setXOffset(0)
            shadow.setYOffset(2)  # Ligero offset vertical
            shadow.setColor(QColor(0, 0, 0, 100))
            button.setGraphicsEffect(shadow)
            button.setCursor(Qt.CursorShape.PointingHandCursor)

        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

    def showEvent(self, event):
        super().showEvent(event)
        # Aplicar efecto acrílico
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd)
        
        # Animación de aparición mejorada
        self.final_pos = self.pos()
        start_pos = self.final_pos + QtCore.QPoint(0, 50)
        self.move(start_pos)
        
        self.pos_anim = QPropertyAnimation(self, b"pos")
        self.pos_anim.setDuration(300)  # Reducido para más rapidez
        self.pos_anim.setStartValue(start_pos)
        self.pos_anim.setKeyValueAt(0.6, self.final_pos - QtCore.QPoint(0, 8))
        self.pos_anim.setKeyValueAt(0.8, self.final_pos + QtCore.QPoint(0, 4))
        self.pos_anim.setEndValue(self.final_pos)
        self.pos_anim.setEasingCurve(QtCore.QEasingCurve.Type.OutBounce)
        
        # Animación de opacidad más suave
        self.fade_anim = QPropertyAnimation(self, b"windowOpacity")
        self.fade_anim.setDuration(200)  # Más rápido
        self.fade_anim.setStartValue(0.0)
        self.fade_anim.setEndValue(1.0)
        self.fade_anim.setEasingCurve(QtCore.QEasingCurve.Type.OutCubic)
        
        self.pos_anim.start()
        self.fade_anim.start()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        rect = self.rect()
        path = QPainterPath()
        path.addRoundedRect(
            float(rect.x()), 
            float(rect.y()), 
            float(rect.width()), 
            float(rect.height()), 
            10.0,  # Radio de esquinas aumentado
            10.0
        )
        # Fondo más sutil
        painter.fillPath(path, QColor(0, 0, 0, 10))

class DeleteProgressBar(DragProgressBar):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.container = QWidget(parent)
        self.layout = QHBoxLayout(self.container)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)
        # Botón de cancelar
        from CREAR import create_no_icon
        self.cancel_button = AnimatedButton(None, self.container)
        self.cancel_button.setIcon(create_no_icon(size=30))
        self.cancel_button.setIconSize(QtCore.QSize(25, 25))
        self.cancel_button._icon_size = 25
        self.cancel_button.setToolTip("Cancelar Borrado")
        self.cancel_button.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
        
        # Sombra para el botón de cancelar
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 160))
        self.cancel_button.setGraphicsEffect(shadow)
        self.layout.addWidget(self.cancel_button)
        self.layout.addWidget(self)
        self.hide()
        self.container.hide()
        self.cancel_button.clicked.connect(
            lambda: cancel_delete_operation(parent, parent.current_delete_worker)
        )
    def show(self):
        self.container.show()
        super().show()
    def hide(self):
        self.container.hide()
        super().hide()

class DeleteTask(QRunnable):
    def __init__(self, files_batch, callback):
        super().__init__()
        self.files_batch = files_batch
        self.callback = callback
    def run(self):
        deleted_files = []
        for file_path in self.files_batch:
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                deleted_files.append(file_path)
            except Exception as e:
                print(f"Error deleting {file_path}: {e}")
        self.callback(deleted_files)

class DeleteWorker(QThread):
    progress = pyqtSignal(int)
    item_deleted = pyqtSignal(str)
    finished = pyqtSignal()
    cleanup_requested = pyqtSignal()
    space_freed = pyqtSignal(str, int)  # Señal para el espacio liberado
    def __init__(self, files_to_delete, main_window):
        super().__init__()
        self.files_to_delete = files_to_delete
        self.main_window = main_window
        self.is_running = True
        self.drive_letter = main_window.current_explorer_drive
        self.total_freed = 0
    def run(self):
        try:
            total_files = len(self.files_to_delete)
            for index, file_path in enumerate(self.files_to_delete, 1):
                if not self.is_running:
                    break
                try:
                    if os.path.exists(file_path):
                        if os.path.isfile(file_path):
                            # Borrar archivo y sumar espacio liberado
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            self.total_freed += file_size
                            self.space_freed.emit(self.drive_letter, file_size)
                        elif os.path.isdir(file_path):
                            # Recorrer la carpeta recursivamente y sumar el espacio de cada archivo
                            for root, _, files in os.walk(file_path):
                                for file in files:
                                    file_path_in_dir = os.path.join(root, file)
                                    file_size = os.path.getsize(file_path_in_dir)
                                    os.remove(file_path_in_dir)
                                    self.total_freed += file_size
                                    self.space_freed.emit(self.drive_letter, file_size)
                            # Borrar la carpeta vacía
                            shutil.rmtree(file_path)
                    progress = int((index / total_files) * 100)
                    self.progress.emit(progress)
                    self.item_deleted.emit(file_path)
                except Exception as e:
                    print(f"Error al borrar {file_path}: {e}")
        finally:
            self.cleanup_requested.emit()
            self.finished.emit()

    def stop(self):
        self.is_running = False
def calculate_folder_size(folder_path):
    """Calcula el tamaño total de una carpeta recursivamente."""
    total_size = 0
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                total_size += os.path.getsize(file_path)
            except OSError:
                continue
    return total_size
def confirm_delete_selected_items(main_window):
    """Confirma el borrado de los elementos seleccionados y muestra el cálculo en consola."""
    try:
        selected_items = main_window.file_tree_widget.selectedItems()
        if not selected_items:
            return

        # Calcular el tamaño total de los elementos seleccionados
        total_size_to_delete = 0
        valid_files_to_delete = []
        
        for item in selected_items:
            try:
                # Verificar que el item siga siendo válido
                file_name = item.text(0)
                file_path = os.path.join(main_window.current_drive, file_name)
                
                # Verificar que el archivo/carpeta exista
                if not os.path.exists(file_path):
                    continue
                    
                valid_files_to_delete.append((file_path, item))
                
                if os.path.isfile(file_path):
                    total_size_to_delete += os.path.getsize(file_path)
                elif os.path.isdir(file_path):
                    total_size_to_delete += calculate_folder_size(file_path)
            except Exception as e:
                print(f"Error procesando item para cálculo de tamaño: {e}")
                continue
                
        # Si no hay archivos válidos para borrar, salir
        if not valid_files_to_delete:
            return

        # Obtener el espacio final actual
        drive_letter = main_window.current_explorer_drive
        if drive_letter in main_window.final_space_text:
            current_final = main_window.final_space_text[drive_letter]
            value, unit = current_final.split()
            value = float(value)
            unit_multipliers = {
                'B': 1,
                'KB': 1024,
                'MB': 1024**2,
                'GB': 1024**3,
                'TB': 1024**4
            }
            current_final_bytes = value * unit_multipliers[unit]
        else:
            current_final_bytes = 0

        # Calcular el nuevo espacio final
        new_final_bytes = current_final_bytes + total_size_to_delete
        new_final_str = main_window.format_size(new_final_bytes)

        # Mostrar en consola
        print("\nCálculo del espacio final:")
        print("----------------------------------------")
        print(f"    {main_window.format_size(current_final_bytes)}  (Espacio final actual)")
        print(f"+   {main_window.format_size(total_size_to_delete)}  (Espacio a liberar)")
        print("----------------------------------------")
        print(f"=   {new_final_str}  (Nuevo espacio final)")
        print("----------------------------------------")

        # Mostrar diálogo de confirmación
        dialog = ConfirmDeleteDialog(main_window)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Pausar la copia si está en curso
            if drive_letter in main_window.threads:
                thread = main_window.threads[drive_letter]
                if thread.is_alive():
                    thread.paused = True
                    main_window.pause_times[drive_letter] = time.time()
                    print(f"Copia en {drive_letter} pausada para borrado.")

            # Procesar el borrado
            files_to_delete = [file_path for file_path, _ in valid_files_to_delete]

            # Crear y configurar el worker para el borrado
            worker = DeleteWorker(files_to_delete, main_window)
            main_window.current_delete_worker = worker
            if not hasattr(main_window, 'delete_progress_bar'):
                main_window.delete_progress_bar = DeleteProgressBar(main_window)
                layout = main_window.back_new_folder_layout
                layout.insertWidget(layout.indexOf(main_window.cola_button) + 1, 
                                  main_window.delete_progress_bar.container)
            worker.progress.connect(main_window.delete_progress_bar.setValue)
            worker.cleanup_requested.connect(lambda: cleanup_after_delete(main_window, worker))
            worker.item_deleted.connect(
                lambda path: remove_item_from_tree(main_window, path),
                Qt.ConnectionType.QueuedConnection
            )
            worker.space_freed.connect(
                lambda drive, size: update_final_space(main_window, drive, size)
            )
            main_window.delete_progress_bar.setValue(0)
            main_window.delete_progress_bar.show()
            worker.start()
    except Exception as e:
        print(f"Error en confirm_delete_selected_items: {e}")
        import traceback
        traceback.print_exc()

def remove_item_from_tree(main_window, file_path):
    """Función auxiliar para eliminar items del árbol de manera segura"""
    try:
        for i in range(main_window.file_tree_widget.topLevelItemCount()):
            item = main_window.file_tree_widget.topLevelItem(i)
            if os.path.join(main_window.current_drive, item.text(0)) == file_path:
                main_window.file_tree_widget.takeTopLevelItem(i)
                break
        main_window.file_tree_widget.update()
    except Exception as e:
        print(f"Error al eliminar item del árbol: {e}")

def cleanup_after_delete(main_window, worker):
    """Limpia después de completar el borrado"""
    try:
        if worker:
            worker.stop()
        if hasattr(main_window, 'current_delete_worker'):
            delattr(main_window, 'current_delete_worker')
        if hasattr(main_window, 'delete_progress_bar'):
            main_window.delete_progress_bar.hide()
        drive_letter = main_window.current_explorer_drive
        if drive_letter in main_window.threads:
            thread = main_window.threads[drive_letter]
            if thread.is_alive():
                thread.paused = False
                if drive_letter in main_window.pause_times:
                    pause_duration = time.time() - main_window.pause_times.pop(drive_letter, time.time())
                    main_window.copy_start_time[drive_letter] += pause_duration
                    
                # Quitar el texto PAUSADO del header
                header_item = main_window.file_tree_widget.headerItem()
                current_text = header_item.text(0)
                if "(PAUSADO)" in current_text:
                    new_text = current_text.replace(" (PAUSADO)", "")
                    header_item.setText(0, new_text)
                print(f"Copia en {drive_letter} reanudada después del borrado.")
        main_window.show_drive_contents(main_window.current_drive, refresh=True)
    except Exception as e:
        print(f"Error en cleanup: {e}")

def remove_single_item(main_window, item):
    if item:
        try:
            parent = item.parent()
            if parent:
                parent.removeChild(item)
            else:
                index = main_window.file_tree_widget.indexOfTopLevelItem(item)
                if index >= 0:
                    main_window.file_tree_widget.takeTopLevelItem(index)
        except Exception as e:
            print(f"Error removiendo item del árbol: {e}")

def cancel_delete_operation(main_window, worker):
    """Cancela la operación de borrado en curso"""
    try:
        if worker and worker.is_running:
            worker.stop()
            print("Operación de borrado cancelada")
        cleanup_after_delete(main_window, worker)
    except Exception as e:
        print(f"Error al cancelar el borrado: {e}")

# La clase CreateFolderDialog se ha movido a CREAR_CARPETA.py

def update_final_space(main_window, drive_letter, freed_space):
    """Actualiza el espacio disponible después de un borrado"""
    try:
        if drive_letter in main_window.threads and main_window.is_copying(drive_letter):
            # Si el disco está copiando, sumar al espacio final
            if drive_letter in main_window.final_space_text:
                current_final = main_window.final_space_text[drive_letter]
                value, unit = current_final.split()
                value = float(value)
                unit_multipliers = {
                    'B': 1,
                    'KB': 1024,
                    'MB': 1024**2,
                    'GB': 1024**3,
                    'TB': 1024**4
                }
                current_final_bytes = value * unit_multipliers[unit]
                new_final_bytes = current_final_bytes + freed_space
                new_final_str = main_window.format_size(new_final_bytes)
                main_window.final_space_text[drive_letter] = new_final_str

                # Actualizar el label de información del disco
                volume_name = main_window.get_volume_name(drive_letter)
                total_size = main_window.total_sizes.get(drive_letter, 0)
                total_size_str = main_window.format_size(total_size)
                total_size_gb = total_size / (1024 ** 3)
                precio = main_window.calcular_y_mostrar_precio(drive_letter, total_size_gb)
                pause_text = ""
                thread = main_window.threads[drive_letter]
                if getattr(thread, 'paused', False):
                    pause_text = " (Pausado)"
                if precio is not None:
                    drive_info_text = f"{volume_name} ({drive_letter}) | [ 𝐓: {total_size_str}] (${precio:.2f}) [ 𝐅: {new_final_str}]{pause_text}"
                else:
                    drive_info_text = f"{volume_name} ({drive_letter}) | [ 𝐓: {total_size_str}] [ 𝐅: {new_final_str}]{pause_text}"
                main_window.drive_info_label.setText(drive_info_text)

                # Actualizar la vista de la lista de discos (si aplica)
                for i in range(main_window.list_widget.count()):
                    item = main_window.list_widget.item(i)
                    if drive_letter in item.text():
                        widget = main_window.list_widget.itemWidget(item)
                        if widget:
                            speed_label = widget.findChildren(QLabel)[2]
                            if precio is not None:
                                speed_text = f"[ 𝐓: {total_size_str}] (${precio:.2f}) [ 𝐅: {new_final_str}]{pause_text}"
                            else:
                                speed_text = f"[ 𝐓: {total_size_str}] [ 𝐅: {new_final_str}]{pause_text}"
                            speed_label.setText(speed_text)
                        break
                print(f"Espacio final actualizado en ambas vistas para {drive_letter}: {new_final_str}")
        else:
            # Si el disco no está copiando, sumar al espacio libre
            free_space, _ = get_drive_space(drive_letter)
            new_free_space = free_space + freed_space
            volume_name = main_window.get_volume_name(drive_letter)
            free_space_str = main_window.format_size(new_free_space)
            drive_info_text = f"{volume_name} ({drive_letter}) | {free_space_str}"
            main_window.drive_info_label.setText(drive_info_text)
            print(f"Espacio disponible actualizado en {drive_letter}: {free_space_str}")
    except Exception as e:
        print(f"Error actualizando espacio final: {e}")