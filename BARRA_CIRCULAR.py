from PyQt6.QtWidgets import QWidget
from PyQt6.QtGui import QPainter, QPen, QColor, QBrush, QPainterPath, QConicalGradient
from PyQt6.QtCore import Qt, QRectF, QPointF

class BarraCircular(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.progress = 0
        self.color = QColor("#0078d7")  # Color por defecto
        self.setFixedSize(32, 32)  # Tamaño por defecto
        
    def setValue(self, value):
        """Establece el valor del progreso (0-100)"""
        self.progress = max(0, min(value, 100))  # Asegurar que esté entre 0 y 100
        self.update()
        
    def value(self):
        """Obtiene el valor actual del progreso"""
        return self.progress
        
    def setColor(self, color):
        """Establece el color de la barra de progreso"""
        self.color = QColor(color)
        self.update()
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Calcular dimensiones
        width = self.width()
        height = self.height()
        margin = 2
        rect = QRectF(margin, margin, width - 2*margin, height - 2*margin)
        
        # Dibujar círculo base (fondo)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QBrush(QColor(255, 255, 255, 30)))
        painter.drawEllipse(rect)
        
        if self.progress > 0:
            # Crear gradiente cónico para el progreso
            gradient = QConicalGradient()
            gradient.setCenter(rect.center())
            gradient.setAngle(90)  # Comenzar desde arriba
            
            # Color principal con diferentes niveles de opacidad
            gradient.setColorAt(0, self.color)
            gradient.setColorAt(self.progress/100, self.color)
            gradient.setColorAt(self.progress/100 + 0.001, QColor(0, 0, 0, 0))
            
            # Configurar el pincel con el gradiente
            painter.setBrush(QBrush(gradient))
            
            # Dibujar el arco de progreso
            pen = QPen(self.color, 3)
            pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            painter.setPen(pen)
            
            # Calcular el ángulo del arco basado en el progreso
            span_angle = -360 * self.progress / 100
            
            # Dibujar el arco de progreso
            painter.drawArc(rect, 90 * 16, span_angle * 16)
            
            # Dibujar círculo interior
            inner_margin = margin + 3
            inner_rect = QRectF(
                inner_margin, 
                inner_margin, 
                width - 2*inner_margin, 
                height - 2*inner_margin
            )
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(QBrush(QColor(0, 0, 0, 100)))
            painter.drawEllipse(inner_rect)
