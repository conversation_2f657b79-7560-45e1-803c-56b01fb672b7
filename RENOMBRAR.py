from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLineEdit, 
    QGraphicsOpacityEffect, QLabel, QWidget, 
    QSpacerItem, QSizePolicy, QGraphicsDropShadowEffect,
    QPushButton
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QPoint, QParallelAnimationGroup, pyqtProperty, QSize
from PyQt6.QtGui import QKeyEvent, QIcon, QPixmap, QColor, QPainter, QPen, QPainterPath
from APARIENCIA import apply_acrylic_and_rounded
from CREAR import renombrar, create_yes_icon
import os
class AnimatedButton(QPushButton):
    def __init__(self, icon_path, parent=None):
        super().__init__(parent)
        self.setIcon(QIcon(icon_path))
        self.setIconSize(QSize(25, 25))
        self.setStyleSheet("border: none; background: transparent;")
        self.setMinimumSize(35, 35)
        self.setMaximumSize(35, 35)
        self.setMouseTracking(True)
        self._icon_size = 25
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 160))
        self.setGraphicsEffect(shadow)

    @pyqtProperty(int)
    def icon_size(self):
        return self._icon_size
    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.setIconSize(QSize(size, size))
    def enterEvent(self, event):
        self.animate_icon_size(30)
    def leaveEvent(self, event):
        self.animate_icon_size(25)
    def animate_icon_size(self, target_size):
        self.animation = QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(50)
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

class CustomRenameDialog(QDialog):
    def __init__(self, parent=None, current_name="", icon_path=None, is_volume=False, is_alias=False):
        super().__init__(parent)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.Popup | 
            Qt.WindowType.NoDropShadowWindowHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd)
        self._border_color = QColor(66, 133, 244, 255)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        input_layout = QHBoxLayout()
        input_layout.setContentsMargins(0, 0, 0, 0)
        input_layout.setSpacing(5)
        if isinstance(icon_path, QIcon):
            icon = icon_path
            item_icon = AnimatedButton("", self)
            item_icon.setIcon(icon)
        elif isinstance(icon_path, str) and os.path.exists(icon_path):
            item_icon = AnimatedButton(icon_path, self)
        else:
            icon = renombrar(size=30)  # Ajustar tamaño según AnimatedButton (30x30)
            item_icon = AnimatedButton("", self)
            item_icon.setIcon(icon)
        item_icon.setFixedSize(30, 30)
        input_layout.addWidget(item_icon)
        self.line_edit = QLineEdit()
        self.line_edit.setText(current_name)
        self.line_edit.setMinimumWidth(200)  # Establecer ancho mínimo
        self.line_edit.setStyleSheet("""
            QLineEdit {
                background-color: #2B2B2B;
                color: white;
                border: none;
                border-radius: 15px;
                padding: 5px 15px;
                font-size: 13px;
                height: 20px;
            }
        """)
        line_edit_shadow = QGraphicsDropShadowEffect()
        line_edit_shadow.setBlurRadius(10)
        line_edit_shadow.setXOffset(0)
        line_edit_shadow.setYOffset(0)
        line_edit_shadow.setColor(self._border_color)
        self.line_edit.setGraphicsEffect(line_edit_shadow)
        input_layout.addWidget(self.line_edit)
        
        self.confirm_button = AnimatedButton(None, self)
        self.confirm_button.setIcon(create_yes_icon(size=30))
        self.confirm_button.setFixedSize(30, 30)
        self.confirm_button.clicked.connect(self.accept)
        self.confirm_button.setCursor(Qt.CursorShape.PointingHandCursor)
        
        input_layout.addWidget(self.confirm_button)
        layout.addLayout(input_layout)
        self.adjustSize()
        self.line_edit.setFocus()
        self.line_edit.selectAll()
        self.color_animation = QPropertyAnimation(self, b"border_color")
        self.color_animation.setDuration(2000)
        self.color_animation.setLoopCount(-1)
        colors = [
            QColor(66, 133, 244, 255),   # Google Blue
            QColor(234, 67, 53, 255),    # Google Red
            QColor(251, 188, 5, 255),    # Google Yellow
            QColor(52, 168, 83, 255),    # Google Green
            QColor(66, 133, 244, 255)    # Volver al azul
        ]
        for i, color in enumerate(colors):
            self.color_animation.setKeyValueAt(i/(len(colors)-1), color)
        self.color_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        self.color_animation.start()
        self.setFixedHeight(40)  # Ajustar el tamaño del diálogo

        # Deshabilitar el Tab focus para los botones
        item_icon.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.confirm_button.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        
        # Deshabilitar el Tab focus para el QLineEdit
        self.line_edit.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        
        # Guardar referencia al parent y al índice actual
        self.parent = parent
        
        # Guardar las banderas de tipo
        self.is_volume = is_volume
        self.is_alias = is_alias
        
        # Solo configurar tree_widget si no es un volumen
        if not is_volume and hasattr(parent, 'file_tree_widget'):
            self.tree_widget = parent.file_tree_widget
            self.current_item_index = None
            
            # Encontrar el índice del item actual
            for i in range(self.tree_widget.topLevelItemCount()):
                item_text = self.tree_widget.topLevelItem(i).text(0)
                if os.path.splitext(item_text)[0] == current_name:
                    self.current_item_index = i
                    break
        else:
            self.tree_widget = None
            self.current_item_index = None

    @pyqtProperty(QColor)
    def border_color(self):
        return self._border_color

    @border_color.setter
    def border_color(self, color):
        self._border_color = color
        for widget in self.findChildren(QWidget):
            if effect := widget.graphicsEffect():
                if isinstance(effect, QGraphicsDropShadowEffect):
                    effect.setColor(color)

    def get_new_name(self):
        return self.line_edit.text()

    def rename_file(self, current_item, new_name_without_ext):
        """Función auxiliar para manejar el renombrado de archivos"""
        try:
            if not current_item:
                return False

            old_name = current_item.text(0)
            extension = os.path.splitext(old_name)[1]
            new_name = new_name_without_ext + extension
            
            # Construir las rutas completas
            old_path = os.path.join(self.parent.current_drive, old_name)
            new_path = os.path.join(self.parent.current_drive, new_name)
            
            # Verificar si el archivo existe y si el nuevo nombre es diferente
            if os.path.exists(old_path) and old_path != new_path:
                # Verificar si el archivo destino ya existe
                if os.path.exists(new_path):
                    print(f"El archivo {new_path} ya existe")
                    return False
                    
                try:
                    # Desactivar temporalmente el ordenamiento
                    self.tree_widget.setSortingEnabled(False)
                    
                    # Primero renombrar el archivo físico
                    os.rename(old_path, new_path)
                    
                    # Luego actualizar el texto en el item
                    current_item.setText(0, new_name)
                    return True
                except Exception as e:
                    print(f"Error al renombrar el archivo: {e}")
                    return False
            return False
        except Exception as e:
            print(f"Error en rename_file: {e}")
            return False

    def keyPressEvent(self, event: QKeyEvent):
        if event.key() in (Qt.Key.Key_Enter, Qt.Key.Key_Return):
            self.accept()
        elif event.key() == Qt.Key.Key_Escape:
            self.reject()
        elif event.key() == Qt.Key.Key_Tab and not self.is_volume:
            # Solo manejar Tab para archivos, no para volúmenes
            try:
                event.accept()
                if self.current_item_index is not None and self.tree_widget:
                    current_item = self.tree_widget.topLevelItem(self.current_item_index)
                    
                    # Intentar renombrar el archivo actual
                    self.rename_file(current_item, self.line_edit.text())
                    
                    # Pasar al siguiente elemento
                    next_index = (self.current_item_index + 1) % self.tree_widget.topLevelItemCount()
                    next_item = self.tree_widget.topLevelItem(next_index)
                    
                    if next_item:
                        self.current_item_index = next_index
                        new_name = os.path.splitext(next_item.text(0))[0]
                        
                        self.line_edit.setText(new_name)
                        self.line_edit.selectAll()
                        
                        self.tree_widget.clearSelection()
                        next_item.setSelected(True)
                        self.tree_widget.scrollToItem(next_item)
            except Exception as e:
                print(f"Error en keyPressEvent: {e}")
        else:
            super().keyPressEvent(event)

    def accept(self):
        try:
            if self.is_volume:
                # Simplemente cerrar el diálogo y dejar que el parent maneje el renombrado
                super().accept()
            else:
                # Comportamiento existente para archivos
                if self.current_item_index is not None and self.tree_widget:
                    current_item = self.tree_widget.topLevelItem(self.current_item_index)
                    self.rename_file(current_item, self.line_edit.text())
                
                # Reactivar el ordenamiento después de cerrar el diálogo
                if self.tree_widget:
                    QTimer.singleShot(100, lambda: self.tree_widget.setSortingEnabled(True))
                super().accept()
        except Exception as e:
            print(f"Error en accept: {e}")
            super().accept()

    def reject(self):
        # Reactivar el ordenamiento después de cerrar el diálogo
        if self.tree_widget:
            QTimer.singleShot(100, lambda: self.tree_widget.setSortingEnabled(True))
        super().reject()

    def showEvent(self, event):
        super().showEvent(event)
        self.final_pos = self.pos()
        start_pos = self.final_pos + QPoint(0, 50)
        self.move(start_pos)
        self.pos_anim = QPropertyAnimation(self, b"pos")
        self.pos_anim.setDuration(400)
        self.pos_anim.setStartValue(start_pos)
        self.pos_anim.setKeyValueAt(0.7, self.final_pos - QPoint(0, 10))
        self.pos_anim.setKeyValueAt(0.85, self.final_pos + QPoint(0, 5))
        self.pos_anim.setEndValue(self.final_pos)
        self.pos_anim.setEasingCurve(QEasingCurve.Type.OutBounce)
        self.fade_anim = QPropertyAnimation(self, b"windowOpacity")
        self.fade_anim.setDuration(300)
        self.fade_anim.setStartValue(0.0)
        self.fade_anim.setEndValue(1.0)
        self.fade_anim.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.pos_anim.start()
        self.fade_anim.start()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        rect = self.rect()
        path = QPainterPath()
        path.addRoundedRect(
            float(rect.x()), 
            float(rect.y()), 
            float(rect.width()), 
            float(rect.height()), 
            8.0, 
            8.0
        )
        painter.fillPath(path, QColor(0, 0, 0, 50))