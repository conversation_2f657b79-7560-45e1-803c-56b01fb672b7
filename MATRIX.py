from PyQt6.QtWidgets import <PERSON><PERSON>ialog, QVBoxLayout, QLabel, QGraphicsDropShadowEffect, QPushButton, QHBoxLayout, QWidget, QFrame, QApplication, QListWidget, QListWidgetItem, QRadioButton, QButtonGroup
from PyQt6.QtGui import QColor, QDragEnterEvent, QDropEvent, QPainter, QPen, QBrush, QLinearGradient, QPainterPath
from PyQt6.QtCore import Qt, QMimeData, QSize, QRectF, pyqtProperty, QPoint, QTimer
import ctypes
import json
import os
import sys
import win32api
import time

from APARIENCIA import (
    ACCENT_POLICY, 
    WINDOWCOMPOSITIONATTRIBDATA, 
    ACCENT_ENABLE_ACRYLICBLURBEHIND, 
    WCA_ACCENT_POLICY,
    DWMWA_WINDOW_CORNER_PREFERENCE,
    DWMWCP_ROUND,
    ACCENT_ENABLE_FLUENT,
    is_windows_11_or_greater
)

# Importar las clases necesarias de CREAR.py
from CREAR import (
    CustomCloseButton, 
    WindowControlButton, 
    ButtonColors, 
    CustomToolTip, 
    DeleteButton,
    icono_MATRIX # Añadir esta importación
)

class CloseButton(QPushButton):
    """Botón de cierre circular rojo con X blanca"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(20, 20)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setStyleSheet("""
            QPushButton {
                background-color: #E81123;
                border-radius: 10px;
                border: none;
            }
            QPushButton:hover {
                background-color: #F1707A;
            }
            QPushButton:pressed {
                background-color: #D10F1C;
            }
        """)
    
    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Dibujar la X blanca
        painter.setPen(QPen(QColor(255, 255, 255), 1.5))
        painter.drawLine(7, 7, 13, 13)
        painter.drawLine(13, 7, 7, 13)

class DropFrame(QFrame):
    """Frame que acepta arrastrar y soltar"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.parent_dialog = parent
        
        # Estilo para el cajón gris oscuro semitransparente
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(50, 50, 50, 100);
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 30);
            }
        """)
    
    def dragEnterEvent(self, event):
        if self.parent_dialog:
            self.parent_dialog.dragEnterEvent(event)
    
    def dragLeaveEvent(self, event):
        if self.parent_dialog:
            self.parent_dialog.dragLeaveEvent(event)
    
    def dropEvent(self, event: QDropEvent):
        """Maneja el evento cuando se suelta algo en el diálogo"""
        if self.parent_dialog:
            self.parent_dialog.dropEvent(event)
            # Forzar actualización de la interfaz
            QApplication.processEvents()

class CustomListItem(QWidget):
    """Widget personalizado para elementos de la lista con radio botón y botón de eliminación"""
    def __init__(self, text, path, parent=None, on_delete=None):
        super().__init__(parent)
        self.path = path
        self.on_delete = on_delete
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)  # Reducir los márgenes
        layout.setSpacing(5)  # Reducir el espacio entre elementos
        
        # Añadir radio button con estilo cuadrado
        self.radio_button = QRadioButton()
        self.radio_button.setStyleSheet("""
            QRadioButton {
                background: transparent;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid gray;
                border-radius: 3px;  /* Reducido para hacerlo más cuadrado */
                background: transparent;
            }
            QRadioButton::indicator:checked {
                background: #0078D7;
                border: 1px solid #0078D7;
                image: none;
            }
            QRadioButton::indicator:hover {
                border: 1px solid white;
            }
        """)
        
        self.label = QLabel(text)
        self.label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 11px;
                background: transparent;
            }
        """)
        self.label.setWordWrap(True)  # Permitir que el texto se envuelva
        
        # Usar el botón de eliminación de CREAR.py
        self.delete_button = DeleteButton(self)  # Pasar self como parent
        self.delete_button.clicked.connect(self.delete_item)
        
        layout.addWidget(self.radio_button)
        layout.addWidget(self.label, 1)  # El 1 hace que el label ocupe todo el espacio disponible
        layout.addWidget(self.delete_button)
        
        # Aplicar estilo general
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(50, 50, 50, 150);  /* Fondo gris semitransparente */
                border-radius: 5px;  /* Bordes redondeados */
            }
        """)

    def sizeHint(self):
        # Asegurar que todos los elementos tengan el mismo tamaño mínimo
        return QSize(200, 40)  # Ajusta el tamaño según sea necesario

    def delete_item(self):
        """Elimina este elemento de la lista"""
        if self.on_delete:
            # Ocultar el tooltip antes de eliminar
            if hasattr(self.delete_button, 'hideTooltip'):
                self.delete_button.hideTooltip()
            self.on_delete(self.path)

    def hideEvent(self, event):
        """Maneja el evento de ocultar el widget"""
        # Ocultar el tooltip cuando se oculta el widget
        if hasattr(self.delete_button, 'hideTooltip'):
            self.delete_button.hideTooltip()
        super().hideEvent(event)

class MatrixDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Window)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        
        # Evitar que se cierre al hacer clic fuera
        self.setModal(True)
        
        # Habilitar arrastrar y soltar
        self.setAcceptDrops(True)
        
        # Tamaño más grande y cuadrado
        self.setFixedSize(350, 450)
        
        # Aplicar el efecto acrílico
        self.apply_acrylic_effect()
        
        # Aplicar bordes redondeados
        hwnd = int(self.winId())
        ctypes.windll.dwmapi.DwmSetWindowAttribute(
            hwnd,
            DWMWA_WINDOW_CORNER_PREFERENCE,
            ctypes.byref(ctypes.c_int(DWMWCP_ROUND)),
            ctypes.sizeof(ctypes.c_int)
        )

        # Layout principal
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Barra de título personalizada
        title_bar = QWidget()
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(10, 5, 10, 5)
        
        # Crear el icono de MATRIX
        matrix_icon = icono_MATRIX(24)  # Tamaño 24x24 para el icono
        icon_label = QLabel()
        icon_label.setPixmap(matrix_icon.pixmap(24, 24))
        title_layout.addWidget(icon_label)
        
        # Título con el texto "MATRIX"
        title_label = QLabel("MATRIX")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_layout.addWidget(title_label)
        
        # Espaciador para empujar el botón de cierre a la derecha
        title_layout.addStretch()
        
        # Botón de cierre
        self.close_button = CustomCloseButton(self)
        self.close_button.clicked.connect(self.request_close)
        title_layout.addWidget(self.close_button)
        
        layout.addWidget(title_bar)
        
        # Crear el cajón gris oscuro
        self.drop_frame = DropFrame(self)
        layout.addWidget(self.drop_frame)
        
        # Layout interno para el cajón
        drop_layout = QVBoxLayout(self.drop_frame)
        drop_layout.setContentsMargins(15, 15, 15, 15)
        drop_layout.setSpacing(10)
        
        # Lista de directorios guardados
        self.list_widget = QListWidget()
        self.list_widget.setStyleSheet("""
            QListWidget {
                background-color: transparent;
                border: none;
                color: white;
                font-size: 11px;
                outline: none;
            }
            QListWidget::item {
                padding: 2px;
                border-bottom: 1px solid rgba(255, 255, 255, 30);
                min-height: 22px;  /* Altura mínima para cada elemento */
                border-radius: 5px;  /* Bordes redondeados */
            }
            QListWidget::item:hover {
                background-color: rgba(255, 255, 255, 20);
            }
            QListWidget::item:selected {
                background-color: rgba(100, 100, 255, 40);
                border: none;
                outline: none;
            }
        """)
        drop_layout.addWidget(self.list_widget)
        
        # Mensaje con efecto de sombra
        self.message = QLabel("Arrastra un directorio o disco aquí")
        self.message.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                background: transparent;
                border: none;
            }
        """)
        self.message.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Efecto de sombra para el texto del mensaje
        message_shadow = QGraphicsDropShadowEffect()
        message_shadow.setBlurRadius(8)
        message_shadow.setXOffset(0)
        message_shadow.setYOffset(0)
        message_shadow.setColor(QColor(0, 0, 0, 160))
        self.message.setGraphicsEffect(message_shadow)
        
        drop_layout.addStretch()
        drop_layout.addWidget(self.message)
        drop_layout.addStretch()
        
        # Crear un grupo de botones para los radio buttons
        self.button_group = QButtonGroup(self)
        self.button_group.setExclusive(False)  # Permitir deselección
        
        # Obtener los discos seleccionados
        self.selected_drives = []
        self.get_selected_drives(parent)
        
        # Variable para almacenar el directorio seleccionado
        self.selected_directory = None
        
        # Cargar directorios guardados DESPUÉS de crear button_group
        self.load_saved_directories()

    def get_selected_drives(self, parent):
        """Obtiene los discos seleccionados de la ventana principal"""
        self.selected_drives = []
        if parent and hasattr(parent, 'list_widget'):
            for i in range(parent.list_widget.count()):
                item = parent.list_widget.item(i)
                if item.isSelected():
                    text = item.text()
                    drive_letter = text.split('(')[-1].strip(')')
                    try:
                        volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                        self.selected_drives.append({
                            'letter': drive_letter,
                            'name': volume_name
                        })
                    except Exception as e:
                        print(f"Error obteniendo información del volumen: {e}")
                        self.selected_drives.append({
                            'letter': drive_letter,
                            'name': f"Disco {drive_letter}"
                        })
        
        # Si no hay discos seleccionados, usar todos los discos disponibles
        if not self.selected_drives and parent and hasattr(parent, 'list_widget'):
            for i in range(parent.list_widget.count()):
                item = parent.list_widget.item(i)
                if not item.isHidden():
                    text = item.text()
                    drive_letter = text.split('(')[-1].strip(')')
                    try:
                        volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                        self.selected_drives.append({
                            'letter': drive_letter,
                            'name': volume_name
                        })
                    except Exception as e:
                        print(f"Error obteniendo información del volumen: {e}")
                        self.selected_drives.append({
                            'letter': drive_letter,
                            'name': f"Disco {drive_letter}"
                        })

    def delete_matrix_entry(self, path):
        """Elimina una entrada de la matriz en config.json"""
        try:
            # Determinar la ruta del ejecutable
            if getattr(sys, 'frozen', False):
                app_path = os.path.dirname(sys.executable)
            else:
                app_path = os.path.dirname(os.path.abspath(__file__))
            
            config_path = os.path.join(app_path, 'config.json')
            
            # Cargar la configuración existente
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # Verificar si existe la sección matrix
                    if 'matrix' in config:
                        # Buscar y eliminar la entrada
                        for i, entry in enumerate(config['matrix']):
                            if entry.get('directory', '') == path:
                                del config['matrix'][i]
                                break
                        
                        # Guardar el archivo actualizado
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=4, ensure_ascii=False)
                        
                        print(f"Entrada eliminada de config.json: {path}")
                        
                        # Recargar la lista
                        self.load_saved_directories()
                except Exception as e:
                    print(f"Error al eliminar entrada de config.json: {e}")
                    import traceback
                    traceback.print_exc()
        except Exception as e:
            print(f"Error en delete_matrix_entry: {e}")

    def load_saved_directories(self):
        """Carga los directorios guardados en config.json"""
        try:
            # Determinar la ruta del ejecutable
            if getattr(sys, 'frozen', False):
                app_path = os.path.dirname(sys.executable)
            else:
                app_path = os.path.dirname(os.path.abspath(__file__))
            
            config_path = os.path.join(app_path, 'config.json')
            
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # Limpiar la lista y el grupo de botones
                    self.list_widget.clear()
                    for button in self.button_group.buttons():
                        self.button_group.removeButton(button)
                    
                    # Obtener la sección de matriz
                    matrix_data = config.get('matrix', [])
                    
                    if matrix_data:
                        for entry in matrix_data:
                            directory = entry.get('directory', '')
                            is_selected = entry.get('selected', False)  # Obtener el estado selected
                            
                            # Determinar el texto a mostrar
                            if directory.endswith(':/') or directory.endswith(':\\'):
                                drive_letter = directory[0]
                                drive_name = entry.get('drive_name', f"Disco {drive_letter}")
                                item_text = f"{drive_name} ({drive_letter}:)"
                            else:
                                dir_name = entry.get('directory_name', os.path.basename(directory))
                                drive_letter = entry.get('drive_letter', '').strip(':\\')
                                if drive_letter:
                                    item_text = f"{dir_name} ({drive_letter}:)"
                                else:
                                    item_text = f"{dir_name}"
                            
                            # Crear un elemento personalizado con radio botón
                            item = QListWidgetItem()
                            item.setData(Qt.ItemDataRole.UserRole, directory)
                            self.list_widget.addItem(item)
                            
                            custom_widget = CustomListItem(item_text, directory, on_delete=self.delete_matrix_entry)
                            
                            # Añadir el radio button al grupo
                            self.button_group.addButton(custom_widget.radio_button)
                            custom_widget.radio_button.setProperty("path", directory)
                            custom_widget.radio_button.setProperty("item", item)
                            
                            # Establecer el estado seleccionado si corresponde
                            if is_selected:
                                custom_widget.radio_button.setChecked(True)
                                self.selected_directory = directory
                                item.setSelected(True)
                            
                            # Asegurar que el elemento tenga suficiente altura
                            item.setSizeHint(custom_widget.sizeHint())
                            self.list_widget.setItemWidget(item, custom_widget)
                        
                        # Desconectar y reconectar señales
                        try:
                            self.button_group.buttonClicked.disconnect()
                        except:
                            pass
                        
                        self.button_group.buttonClicked.connect(self.on_radio_clicked)
                        
                        # Forzar actualización de la interfaz
                        self.list_widget.repaint()
                        self.message.setText("Arrastra un directorio o disco aquí")
                        self.message.repaint()
                        QApplication.processEvents()
                    
                except Exception as e:
                    print(f"Error cargando config.json: {e}")
        except Exception as e:
            print(f"Error en load_saved_directories: {e}")
    
    def on_radio_clicked(self, button):
        """Maneja la selección de un radio button"""
        # Obtener la ruta y el elemento asociados al botón
        path = button.property("path")
        item = button.property("item")
        
        # Si ya está seleccionado, deseleccionar
        if self.selected_directory == path:
            button.setChecked(False)
            self.selected_directory = None
            if item:
                item.setSelected(False)
            # Guardar el estado deseleccionado
            self.save_selection(None)
            # Forzar actualización visual
            self.list_widget.repaint()
            QApplication.processEvents()
            return
        
        # Si es una nueva selección, deseleccionar otros primero
        for other_button in self.button_group.buttons():
            if other_button != button:
                other_button.setChecked(False)
                if other_button.property("item"):
                    other_button.property("item").setSelected(False)
        
        # Seleccionar el nuevo
        button.setChecked(True)
        self.selected_directory = path
        if item:
            item.setSelected(True)
        
        # Guardar la selección en config.json
        self.save_selection(path)
        
        # Forzar actualización visual
        self.list_widget.repaint()
        QApplication.processEvents()

    def save_selection(self, selected_path):
        """Guarda la selección actual en config.json"""
        try:
            # Determinar la ruta del ejecutable
            if getattr(sys, 'frozen', False):
                app_path = os.path.dirname(sys.executable)
            else:
                app_path = os.path.dirname(os.path.abspath(__file__))
            
            config_path = os.path.join(app_path, 'config.json')
            
            # Cargar la configuración existente
            config = {}
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                except json.JSONDecodeError:
                    config = {}
            
            # Inicializar la sección de matriz si no existe
            if 'matrix' not in config:
                config['matrix'] = []
            
            # Actualizar el estado de selección para cada entrada
            for entry in config['matrix']:
                if entry.get('directory', '') == selected_path:
                    entry['selected'] = True
                else:
                    entry['selected'] = False
            
            # Si selected_path es None, deseleccionar todo
            if selected_path is None:
                entry['selected'] = False
            
            # Guardar el archivo JSON
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            print(f"Selección guardada en config.json: {selected_path}")
            
        except Exception as e:
            print(f"Error guardando selección en config.json: {e}")

    def apply_acrylic_effect(self):
        """Aplica el efecto acrílico a la ventana"""
        hwnd = int(self.winId())
        accent = ACCENT_POLICY()
        if is_windows_11_or_greater():
            accent.AccentState = ACCENT_ENABLE_ACRYLICBLURBEHIND
            accent.GradientColor = 0x01000000  # Transparencia para Windows 11
        else:
            accent.AccentState = ACCENT_ENABLE_FLUENT
            accent.GradientColor = 0x20000000  # 12.5% de opacidad para Windows 10
        accent.AccentFlags = 0x20 | 0x40 | 0x80 | 0x100
        
        data = WINDOWCOMPOSITIONATTRIBDATA()
        data.Attribute = WCA_ACCENT_POLICY
        data.SizeOfData = ctypes.sizeof(accent)
        data.Data = ctypes.cast(ctypes.pointer(accent), ctypes.POINTER(ctypes.c_int))
        ctypes.windll.user32.SetWindowCompositionAttribute(hwnd, ctypes.byref(data))

    def showEvent(self, event):
        super().showEvent(event)
        # Actualizar el efecto cada vez que se muestra el diálogo
        self.apply_acrylic_effect()
        
        # Cargar directorios guardados
        self.load_saved_directories()
        
        # Asegurarse de que el mensaje sea genérico
        self.message.setText("Arrastra un directorio o disco aquí")
        self.message.repaint()
        QApplication.processEvents()
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """Maneja el evento cuando se arrastra algo sobre el diálogo"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            self.message.setText("Suelta para guardar")
    
    def dragLeaveEvent(self, event):
        """Maneja el evento cuando se arrastra algo fuera del diálogo"""
        self.message.setText("")
        # Recargar el último directorio guardado
        self.load_saved_directories()
    
    def dropEvent(self, event: QDropEvent):
        """Maneja el evento cuando se suelta algo en el diálogo"""
        urls = event.mimeData().urls()
        if urls and urls[0].isLocalFile():
            path = urls[0].toLocalFile()
            
            # Verificar si es un directorio o un disco
            if os.path.isdir(path):
                # Guardar la información en config.json
                self.save_to_json(path)
                
                # Aceptar el evento
                event.acceptProposedAction()
            else:
                self.message.setText("Solo se aceptan directorios o discos")
                self.message.repaint()
                QApplication.processEvents()
    
    def save_to_json(self, directory_path):
        """Guarda la información del directorio y los discos en config.json"""
        try:
            # Determinar la ruta del ejecutable
            if getattr(sys, 'frozen', False):
                app_path = os.path.dirname(sys.executable)
            else:
                app_path = os.path.dirname(os.path.abspath(__file__))
            
            config_path = os.path.join(app_path, 'config.json')
            
            # Cargar la configuración existente
            config = {}
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                except json.JSONDecodeError:
                    config = {}
            
            # Inicializar la sección de matriz si no existe
            if 'matrix' not in config:
                config['matrix'] = []
            
            # Obtener la letra de unidad del directorio
            drive_letter = os.path.splitdrive(directory_path)[0]
            
            # Convertir cualquier directorio a una entrada de disco
            if not (directory_path.endswith(':\\') or directory_path.endswith(':/')) and drive_letter:
                # Si es un directorio, usamos la letra de unidad para crear una entrada de disco
                drive_path = drive_letter + "\\"
                
                # Verificar si el disco ya existe en la lista
                disk_exists = False
                for entry in config['matrix']:
                    if entry.get('directory', '') == drive_path:
                        disk_exists = True
                        # Mover la entrada existente al final de la lista
                        config['matrix'].remove(entry)
                        config['matrix'].append(entry)
                        break
                
                # Solo crear una nueva entrada si el disco no existe
                if not disk_exists:
                    try:
                        volume_name = win32api.GetVolumeInformation(drive_path)[0]
                        if not volume_name:
                            volume_name = f"Disco {drive_letter[0]}"
                    except Exception as e:
                        print(f"Error obteniendo información del volumen: {e}")
                        volume_name = f"Disco {drive_letter[0]}"
                    
                    entry = {
                        'directory': drive_path,
                        'directory_name': '',
                        'drive_letter': drive_letter,
                        'drive_name': volume_name,
                        'timestamp': time.time(),
                        'selected': False
                    }
                    
                    # Añadir la nueva entrada
                    config['matrix'].append(entry)
            else:
                # Es un disco completo
                if drive_letter.endswith(':'):
                    drive_path = drive_letter + "\\"
                else:
                    drive_path = directory_path
                
                # Verificar si el disco ya existe en la lista
                disk_exists = False
                for entry in config['matrix']:
                    if entry.get('directory', '') == drive_path:
                        disk_exists = True
                        # Mover la entrada existente al final de la lista
                        config['matrix'].remove(entry)
                        config['matrix'].append(entry)
                        break
                
                # Solo crear una nueva entrada si el disco no existe
                if not disk_exists:
                    try:
                        volume_name = win32api.GetVolumeInformation(drive_path)[0]
                        if not volume_name:
                            volume_name = f"Disco {drive_path[0]}"
                    except Exception as e:
                        print(f"Error obteniendo información del volumen: {e}")
                        volume_name = f"Disco {drive_path[0]}"
                    
                    entry = {
                        'directory': drive_path,
                        'directory_name': '',
                        'drive_letter': drive_letter,
                        'drive_name': volume_name,
                        'timestamp': time.time(),
                        'selected': False
                    }
                    
                    # Añadir la nueva entrada
                    config['matrix'].append(entry)
            
            # Guardar el archivo JSON preservando todas las demás configuraciones
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            print(f"Información de matriz guardada en {config_path}")
            
            # Cargar inmediatamente el último directorio guardado
            self.load_saved_directories()
            
        except Exception as e:
            print(f"Error guardando en config.json: {e}")
            import traceback
            traceback.print_exc()
    
    def update_ui_after_save(self):
        """Actualiza la interfaz después de guardar"""
        # Forzar actualización de la interfaz
        if hasattr(self, 'message'):
            self.message.repaint()
        QApplication.processEvents()
        
        # Esperar un momento para asegurar que la interfaz se actualice
        for _ in range(5):
            QApplication.processEvents()

    def closeEvent(self, event):
        # Solo aceptar el evento de cierre si viene del botón de cierre
        if hasattr(self, '_close_requested') and self._close_requested:
            event.accept()
        else:
            event.ignore()
    
    def request_close(self):
        """Cierra el diálogo y oculta cualquier tooltip visible"""
        # Ocultar el tooltip del botón de cierre si está visible
        if hasattr(self.close_button, 'tooltip') and self.close_button.tooltip.isVisible():
            self.close_button.tooltip.hide()
        
        # Si el botón tiene un método para ocultar el tooltip, llamarlo
        if hasattr(self.close_button, 'hideTooltip'):
            self.close_button.hideTooltip()
        
        # Cerrar el diálogo
        self.accept()

def show_matrix_dialog(parent=None):
    """Muestra el diálogo de Matrix para arrastrar directorios"""
    dialog = MatrixDialog(parent)
    
    # Centrar el diálogo en la ventana principal
    if parent:
        # Obtener el centro de la ventana principal
        parent_center = parent.geometry().center()
        
        # Mover el diálogo para que su centro coincida con el centro de la ventana principal
        dialog_geometry = dialog.geometry()
        dialog_geometry.moveCenter(parent_center)
        dialog.setGeometry(dialog_geometry)
    
    dialog.exec()