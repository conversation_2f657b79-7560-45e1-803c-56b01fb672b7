from PyQt6.QtWidgets import QLabel
from PyQt6.QtCore import Qt, QRectF, QPointF
from PyQt6.QtGui import (
    QPainter, QColor, QRadialGradient, 
    QLinearGradient, QPen, QPainterPath
)
import win32api

class NotificationBadge(QLabel):
    def __init__(self, count, parent=None, drive_letter=None, main_window=None):
        super().__init__(parent)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.drive_letter = drive_letter
        self.main_window = main_window
        
        # Configurar el texto
        self.setText(str(count))
        
        # Calcular el ancho basado en el contenido
        font = self.font()
        font.setPointSize(10)
        font.setBold(True)
        self.setFont(font)
        
        # Calcular el ancho mínimo basado en el texto
        metrics = self.fontMetrics()
        text_width = metrics.horizontalAdvance(str(count))
        min_width = max(16, text_width + 10)  # 10 pixels de padding
        
        self.setFixedHeight(16)
        self.setFixedWidth(min_width)
        
        # Estilo base
        self.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10px;
                font-weight: bold;
                padding: 0px 2px;
            }
        """)
        
        # Hacer que el badge sea clickeable
        self.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.restore_drive_copies()
        elif event.button() == Qt.MouseButton.RightButton:
            self.delete_pending_copies()
        super().mousePressEvent(event)
    
    def restore_drive_copies(self):
        """Restaura las copias pendientes solo para este disco"""
        if self.drive_letter and self.main_window:
            try:
                # --- INICIO: VERIFICACIÓN DE LICENCIA ---
                # Verificar el estado de la licencia antes de continuar
                if hasattr(self.main_window, 'license_active') and not self.main_window.license_active:
                    # Importar y mostrar el diálogo de activación de licencia
                    from ACTIVATE import show_license_activation_dialog
                    show_license_activation_dialog(self.main_window, self.main_window)
                    return
                # --- FIN: VERIFICACIÓN DE LICENCIA ---
                
                # Reproducir sonido
                from ZETACOPY import play_drag_sound
                play_drag_sound()
                
                # Obtener la ruta del archivo de copias pendientes
                import os, sys, json
                if getattr(sys, 'frozen', False):
                    base_path = os.path.dirname(sys.executable)
                else:
                    base_path = os.path.dirname(__file__)
                save_path = os.path.join(base_path, 'pending_copies.json')
                
                if not os.path.exists(save_path):
                    self.main_window.list_widget.show_tooltip(f"No hay copias pendientes para {self.drive_letter}", duration=3000)
                    return
                
                # Cargar todas las copias pendientes
                with open(save_path, 'r', encoding='utf-8') as f:
                    pending_copies = json.load(f)
                
                # Obtener el nombre del volumen
                volume_name = ""
                try:
                    volume_name = win32api.GetVolumeInformation(f"{self.drive_letter}\\")[0]
                except:
                    pass
                
                # Buscar la clave correcta para este disco
                drive_key = None
                for key in pending_copies.keys():
                    # Verificar si la clave contiene la letra del disco
                    if isinstance(key, str) and self.drive_letter + ":" in key:
                        drive_key = key
                        break
                    # O verificar si el campo drive_letter coincide
                    elif isinstance(pending_copies[key], dict) and 'drive_letter' in pending_copies[key] and pending_copies[key]['drive_letter'] == self.drive_letter:
                        drive_key = key
                        break
                
                # Verificar si hay copias para este disco
                if not drive_key:
                    self.main_window.list_widget.show_tooltip(f"No hay copias pendientes para {self.drive_letter}", duration=3000)
                    return
                
                # Crear una copia del diccionario original con solo este disco
                single_drive_pending = {drive_key: pending_copies[drive_key]}
                
                # Guardar el original (esto es lo importante)
                original_path = os.path.join(base_path, 'original_pending.json')
                with open(original_path, 'w', encoding='utf-8') as f:
                    json.dump(pending_copies, f)
                
                # Reemplazar el archivo original con solo este disco
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(single_drive_pending, f)
                
                # Llamar al método de restauración
                self.main_window.restore_pending_copies()
                
                # Esperar un poco para asegurarnos de que la restauración ha terminado
                import time
                time.sleep(0.5)
                
                # Restaurar el archivo original COMPLETO sin eliminar ninguna entrada
                if os.path.exists(original_path):
                    # Simplemente restaurar el archivo original tal como estaba
                    with open(original_path, 'r', encoding='utf-8') as f:
                        original_pending = json.load(f)
                    
                    with open(save_path, 'w', encoding='utf-8') as f:
                        json.dump(original_pending, f, indent=2, ensure_ascii=False)
                    
                    print(f"Archivo de copias pendientes restaurado completamente (sin eliminar entradas)")
                    
                    # Limpiar archivos temporales
                    if os.path.exists(original_path):
                        os.remove(original_path)
                
            except Exception as e:
                print(f"Error al restaurar copias para {self.drive_letter}: {e}")
                import traceback
                traceback.print_exc()
                
                # Intentar recuperar el archivo original en caso de error
                try:
                    if os.path.exists(original_path):
                        with open(original_path, 'r', encoding='utf-8') as f:
                            original_pending = json.load(f)
                        with open(save_path, 'w', encoding='utf-8') as f:
                            json.dump(original_pending, f)
                        print("Archivo de copias pendientes recuperado después de error")
                except:
                    pass
                    
                self.main_window.list_widget.show_tooltip(f"Error: {str(e)}", duration=3000)

    def delete_pending_copies(self):
        """Elimina las copias pendientes para este disco sin restaurarlas"""
        if self.drive_letter and self.main_window:
            try:
                # --- INICIO: VERIFICACIÓN DE LICENCIA ---
                # Verificar el estado de la licencia antes de continuar
                if hasattr(self.main_window, 'license_active') and not self.main_window.license_active:
                    # Importar y mostrar el diálogo de activación de licencia
                    from ACTIVATE import show_license_activation_dialog
                    show_license_activation_dialog(self.main_window, self.main_window)
                    return
                # --- FIN: VERIFICACIÓN DE LICENCIA ---
                
                # Reproducir sonido
                from ZETACOPY import play_drag_sound
                play_drag_sound()
                
                # Obtener la ruta del archivo de copias pendientes
                import os, sys, json
                if getattr(sys, 'frozen', False):
                    base_path = os.path.dirname(sys.executable)
                else:
                    base_path = os.path.dirname(__file__)
                save_path = os.path.join(base_path, 'pending_copies.json')
                
                if not os.path.exists(save_path):
                    self.main_window.list_widget.show_tooltip(f"No hay copias pendientes para {self.drive_letter}", duration=3000)
                    return
                
                # Cargar todas las copias pendientes
                with open(save_path, 'r', encoding='utf-8') as f:
                    pending_copies = json.load(f)
                
                # Buscar la clave correcta para este disco
                drive_key = None
                for key in pending_copies.keys():
                    # Verificar si la clave contiene la letra del disco
                    if isinstance(key, str) and self.drive_letter + ":" in key:
                        drive_key = key
                        break
                    # O verificar si el campo drive_letter coincide
                    elif isinstance(pending_copies[key], dict) and 'drive_letter' in pending_copies[key] and pending_copies[key]['drive_letter'] == self.drive_letter:
                        drive_key = key
                        break
                
                # Verificar si hay copias para este disco
                if not drive_key:
                    self.main_window.list_widget.show_tooltip(f"No hay copias pendientes para {self.drive_letter}", duration=3000)
                    return
                
                # Eliminar las copias de este disco
                del pending_copies[drive_key]
                
                # Guardar el archivo actualizado si quedan discos
                if pending_copies:
                    with open(save_path, 'w', encoding='utf-8') as f:
                        json.dump(pending_copies, f, indent=2, ensure_ascii=False)
                    self.main_window.list_widget.show_tooltip(f"Copias pendientes de {self.drive_letter} eliminadas", duration=3000)
                else:
                    # Si no quedan discos, eliminar el archivo
                    if os.path.exists(save_path):
                        os.remove(save_path)
                    self.main_window.list_widget.show_tooltip(f"Todas las copias pendientes eliminadas", duration=3000)
                
                # Eliminar el badge
                from NOTIFICACION import remove_badge_from_disk
                remove_badge_from_disk(self.main_window, self.drive_letter)
                
            except Exception as e:
                print(f"Error al eliminar copias pendientes para {self.drive_letter}: {e}")
                import traceback
                traceback.print_exc()
                self.main_window.list_widget.show_tooltip(f"Error: {str(e)}", duration=3000)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect()
        x, y, w, h = rect.x(), rect.y(), rect.width(), rect.height()
        
        # Radio del borde redondeado
        radius = h/2 if w > h else w/2
        
        # Dibujar sombra
        shadow_path = QPainterPath()
        shadow_path.addRoundedRect(QRectF(x + 1, y + 1, w - 2, h - 2), radius, radius)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QColor(0, 0, 0, 40))
        painter.drawPath(shadow_path)
        
        # Gradiente para el fondo
        gradient = QLinearGradient(x, y, x, y + h)
        gradient.setColorAt(0, QColor("#007AFF"))  # Azul iOS más claro
        gradient.setColorAt(1, QColor("#0066FF"))  # Azul iOS más oscuro
        
        # Dibujar el fondo
        path = QPainterPath()
        path.addRoundedRect(QRectF(x + 0.5, y + 0.5, w - 1, h - 1), radius, radius)
        painter.setBrush(gradient)
        painter.setPen(QPen(QColor(255, 255, 255, 40), 0.5))
        painter.drawPath(path)
        
        # Efecto de brillo
        shine_path = QPainterPath()
        shine_rect = QRectF(x + 2, y + 1, w - 4, h/2)
        shine_path.addRoundedRect(shine_rect, radius/2, radius/2)
        
        shine_gradient = QLinearGradient(0, y, 0, y + h/2)
        shine_gradient.setColorAt(0, QColor(255, 255, 255, 100))
        shine_gradient.setColorAt(1, QColor(255, 255, 255, 0))
        
        painter.setBrush(shine_gradient)
        painter.drawPath(shine_path)
        
        # Dibujar texto
        painter.setPen(QColor(255, 255, 255))
        painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, self.text())


def add_badge_to_disk(main_window, drive_letter, pending_count=None):
    """Añade un badge a un disco específico"""
    try:
        for i in range(main_window.list_widget.count()):
            item = main_window.list_widget.item(i)
            if drive_letter in item.text():
                widget = main_window.list_widget.itemWidget(item)
                if widget:
                    # Eliminar badge existente si hay uno
                    if hasattr(widget, 'notification_badge'):
                        widget.notification_badge.deleteLater()
                    
                    # Crear y añadir el nuevo badge
                    badge = NotificationBadge(
                        pending_count if pending_count is not None else "!", 
                        widget, 
                        drive_letter=drive_letter,
                        main_window=main_window
                    )
                    
                    # Obtener la referencia al label del volumen
                    volume_label = widget.volume_label
                    
                    # Calcular la posición basada en el volume_label
                    volume_label_pos = volume_label.pos()
                    
                    # Encontrar la posición de la letra del disco en el texto
                    text = volume_label.text()
                    drive_index = text.find(drive_letter)
                    if drive_index != -1:
                        # Calcular el ancho aproximado hasta la letra del disco
                        metrics = volume_label.fontMetrics()
                        width_to_drive = metrics.horizontalAdvance(text[:drive_index + 2])
                        
                        # Posicionar el badge después de la letra del disco
                        badge_x = volume_label_pos.x() + width_to_drive + 5
                        badge_y = volume_label_pos.y() + (volume_label.height() - badge.height()) // 2
                        
                        badge.setGeometry(badge_x, badge_y, badge.width(), badge.height())
                        badge.show()
                        badge.raise_()
                        widget.notification_badge = badge
                        print(f"Badge añadido para {drive_letter} con {pending_count} archivos")
                        widget.update()
                break
    except Exception as e:
        print(f"Error añadiendo badge al disco {drive_letter}: {e}")

def update_badge_positions(main_window):
    """Actualiza las posiciones de los badges al redimensionar"""
    for i in range(main_window.list_widget.count()):
        item = main_window.list_widget.item(i)
        widget = main_window.list_widget.itemWidget(item)
        if hasattr(widget, 'notification_badge'):
            volume_label = widget.volume_label
            badge = widget.notification_badge
            
            # Encontrar la posición de la letra del disco en el texto
            text = volume_label.text()
            drive_letter = None
            for char in text:
                if char + ":\\" in text:
                    drive_letter = char
                    break
            if drive_letter:
                drive_index = text.find(drive_letter)
                metrics = volume_label.fontMetrics()
                width_to_drive = metrics.horizontalAdvance(text[:drive_index + 2])
                
                # Mantener la misma lógica de posicionamiento
                badge_x = volume_label.pos().x() + width_to_drive + 5
                badge_y = volume_label.pos().y() + (volume_label.height() - badge.height()) // 2
                badge.setGeometry(badge_x, badge_y, badge.width(), badge.height())
                badge.raise_()

def remove_badge_from_disk(main_window, drive_letter):
    """Elimina el badge de un disco específico"""
    for i in range(main_window.list_widget.count()):
        item = main_window.list_widget.item(i)
        if drive_letter in item.text():
            widget = main_window.list_widget.itemWidget(item)
            if widget and hasattr(widget, 'notification_badge'):
                widget.volume_label.setContentsMargins(0, 0, 0, 0)
                # Eliminar el badge
                widget.notification_badge.deleteLater()
                delattr(widget, 'notification_badge')
                # Forzar actualización visual
                widget.update()
                main_window.list_widget.update()
                print(f"Badge eliminado para {drive_letter}")
            break 