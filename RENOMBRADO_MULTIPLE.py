from PyQt6.QtWidgets import Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QListWidget, QPushButton, QLabel, QLineEdit, QMessageBox, QSpinBox, QWidget, QFrame
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QColor, QPalette, QPainter, QPainterPath
import os
import json
import re
from collections import Counter
from APARIENCIA import apply_acrylic_and_rounded
from REDIMENSIONAR import WindowResizer

# Ruta para guardar el historial de renombrado
HISTORIAL_PATH = os.path.join(os.path.dirname(__file__), 'historial_renombrado.json')

class CustomFrame(QFrame):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(43, 43, 43, 0.5);
                border: 1px solid rgba(58, 58, 58, 0.5);
                border-radius: 5px;
            }
        """)

class CustomListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QListWidget {
                background-color: rgba(30, 30, 30, 0.5);
                border: 1px solid rgba(58, 58, 58, 0.5);
                border-radius: 5px;
                color: #ffffff;
            }
            QListWidget::item {
                padding: 5px;
            }
            QListWidget::item:selected {
                background-color: rgba(0, 120, 212, 0.6);
            }
        """)
        # Habilitar arrastrar y soltar
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)
        self.setDragDropMode(QListWidget.DragDropMode.InternalMove)
        self.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)

class RenombrarMultipleDialog(QDialog):
    def __init__(self, archivos_seleccionados, parent=None):
        super().__init__(parent)
        self.parent = parent
        # Guardar las rutas completas de los archivos
        self.archivos_seleccionados = []
        # Guardar los nombres originales para posible restauración
        self.nombres_originales = []
        
        # Determinar la ruta base (directorio actual)
        self.directorio_base = ""
        if parent and hasattr(parent, 'current_drive'):
            self.directorio_base = parent.current_drive
        
        # Construir rutas completas
        for archivo in archivos_seleccionados:
            if os.path.isabs(archivo):
                ruta_completa = archivo
            else:
                ruta_completa = os.path.join(self.directorio_base, archivo)
            
            self.archivos_seleccionados.append(ruta_completa)
            self.nombres_originales.append(os.path.basename(ruta_completa))
        
        # Detectar el nombre común para usar como sufijo
        nombre_comun = self.detectar_nombre_comun()
        
        # Configuración de la ventana
        self.setWindowTitle("Renombrado Múltiple")
        self.setMinimumSize(900, 600)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.Window |  # Usar Window en lugar de Dialog
            Qt.WindowType.WindowStaysOnTopHint  # Mantener encima
        )
        # Desactivar la modalidad para permitir interactuar con la ventana principal
        self.setModal(False)
        
        # Asegurar que la ventana se destruya al cerrar
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, True)
        
        # Inicializar el redimensionador de ventana
        self.window_resizer = WindowResizer(self)
        
        # Crear un layout principal para toda la ventana
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Crear un widget de fondo semitransparente
        self.background_widget = QWidget()
        self.background_widget.setObjectName("background_widget")
        self.background_widget.setStyleSheet("""
            QWidget#background_widget {
                background-color: rgba(20, 20, 20, 0.6);
                border-radius: 10px;
            }
        """)
        
        # Layout para el contenido dentro del widget de fondo
        content_layout = QHBoxLayout(self.background_widget)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(10)
        
        # Añadir el widget de fondo al layout principal
        main_layout.addWidget(self.background_widget)
        
        # Aplicar efecto acrílico (se hará en showEvent)
        
        # Estilo general para la ventana
        self.setStyleSheet("""
            QDialog {
                background-color: rgba(30, 30, 30, 0.7);
                color: white;
                border-radius: 10px;
            }
            QLabel {
                color: white;
            }
            QLineEdit, QSpinBox {
                background-color: rgba(45, 45, 45, 0.7);
                color: white;
                border: 1px solid rgba(100, 100, 100, 0.5);
                border-radius: 4px;
                padding: 5px;
            }
            QListWidget {
                background-color: rgba(40, 40, 40, 0.7);
                color: white;
                border: 1px solid rgba(100, 100, 100, 0.5);
                border-radius: 4px;
            }
            QPushButton {
                background-color: rgba(60, 60, 60, 0.7);
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: rgba(80, 80, 80, 0.7);
            }
            QPushButton:pressed {
                background-color: rgba(100, 100, 100, 0.7);
            }
            QFrame {
                border: none;
            }
        """)
        
        # Layout principal
        layout = QHBoxLayout()
        layout.setSpacing(10)
        
        # Panel izquierdo (archivos originales)
        panel_izquierdo = CustomFrame()
        layout_izquierdo = QVBoxLayout(panel_izquierdo)
        layout_izquierdo.addWidget(QLabel("Nombres Originales (arrastrar para reordenar):"))
        self.lista_original = CustomListWidget()
        self.lista_original.addItems([os.path.basename(f) for f in archivos_seleccionados])
        layout_izquierdo.addWidget(self.lista_original)
        
        # Conectar señal de cambio de orden en la lista original
        self.lista_original.model().rowsMoved.connect(self.actualizar_orden_archivos)
        
        # Panel central (controles)
        panel_central = CustomFrame()
        layout_central = QVBoxLayout(panel_central)
        
        # Crear la barra de título personalizada
        titulo_layout = QHBoxLayout()
        titulo_layout.setContentsMargins(10, 5, 10, 5)
        
        titulo_label = QLabel("Renombrado Múltiple")
        titulo_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        
        # Botón de cerrar
        btn_cerrar = QPushButton("✕")
        btn_cerrar.setFixedSize(30, 30)
        btn_cerrar.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #e81123;
                border-radius: 15px;
            }
        """)
        # Usar una conexión directa y explícita
        btn_cerrar.clicked.connect(lambda: self.close())
        
        titulo_layout.addWidget(titulo_label)
        titulo_layout.addStretch()
        titulo_layout.addWidget(btn_cerrar)
        
        layout_central.addLayout(titulo_layout)
        
        # Línea separadora después del título
        linea = QFrame()
        linea.setFrameShape(QFrame.Shape.HLine)
        linea.setFrameShadow(QFrame.Shadow.Sunken)
        linea.setStyleSheet("background-color: rgba(255, 255, 255, 0.1);")
        layout_central.addWidget(linea)
        
        # Controles para el renombrado
        grupo_prefijo = QHBoxLayout()
        grupo_prefijo.addWidget(QLabel("Prefijo:"))
        self.prefijo_edit = QLineEdit()
        self.prefijo_edit.setStyleSheet("""
            QLineEdit {
                background-color: rgba(45, 45, 45, 0.5);
                color: white;
                border: 1px solid rgba(100, 100, 100, 0.5);
                border-radius: 4px;
                padding: 5px;
            }
        """)
        grupo_prefijo.addWidget(self.prefijo_edit)
        layout_central.addLayout(grupo_prefijo)
        
        grupo_nombre = QHBoxLayout()
        grupo_nombre.addWidget(QLabel("Nombre Base:"))
        self.nombre_base_edit = QLineEdit()
        self.nombre_base_edit.setStyleSheet("""
            QLineEdit {
                background-color: rgba(45, 45, 45, 0.5);
                color: white;
                border: 1px solid rgba(100, 100, 100, 0.5);
                border-radius: 4px;
                padding: 5px;
            }
        """)
        grupo_nombre.addWidget(self.nombre_base_edit)
        layout_central.addLayout(grupo_nombre)
        
        grupo_sufijo = QHBoxLayout()
        grupo_sufijo.addWidget(QLabel("Sufijo:"))
        self.sufijo_edit = QLineEdit()
        # Establecer el nombre común detectado como sufijo predeterminado
        if nombre_comun:
            self.sufijo_edit.setText(nombre_comun)
        self.sufijo_edit.setStyleSheet("""
            QLineEdit {
                background-color: rgba(45, 45, 45, 0.5);
                color: white;
                border: 1px solid rgba(100, 100, 100, 0.5);
                border-radius: 4px;
                padding: 5px;
            }
        """)
        grupo_sufijo.addWidget(self.sufijo_edit)
        layout_central.addLayout(grupo_sufijo)
        
        grupo_numero = QHBoxLayout()
        grupo_numero.addWidget(QLabel("Número Inicial:"))
        self.numero_inicial = QSpinBox()
        self.numero_inicial.setValue(1)
        self.numero_inicial.setStyleSheet("""
            QSpinBox {
                background-color: rgba(45, 45, 45, 0.5);
                color: white;
                border: 1px solid rgba(100, 100, 100, 0.5);
                border-radius: 4px;
                padding: 5px;
            }
        """)
        grupo_numero.addWidget(self.numero_inicial)
        layout_central.addLayout(grupo_numero)
        
        grupo_digitos = QHBoxLayout()
        grupo_digitos.addWidget(QLabel("Dígitos:"))
        self.digitos_spin = QSpinBox()
        self.digitos_spin.setValue(3)
        self.digitos_spin.setRange(1, 10)
        self.digitos_spin.setStyleSheet("""
            QSpinBox {
                background-color: rgba(45, 45, 45, 0.5);
                color: white;
                border: 1px solid rgba(100, 100, 100, 0.5);
                border-radius: 4px;
                padding: 5px;
            }
        """)
        grupo_digitos.addWidget(self.digitos_spin)
        layout_central.addLayout(grupo_digitos)
        
        # Botón de vista previa
        self.btn_vista_previa = QPushButton("Vista Previa")
        self.btn_vista_previa.setStyleSheet("""
            QPushButton {
                background-color: rgba(60, 60, 60, 0.7);
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: rgba(80, 80, 80, 0.7);
            }
            QPushButton:pressed {
                background-color: rgba(100, 100, 100, 0.7);
            }
        """)
        self.btn_vista_previa.clicked.connect(self.actualizar_vista_previa)
        layout_central.addWidget(self.btn_vista_previa)
        
        # Botón de organización inteligente
        self.btn_organizacion_inteligente = QPushButton("Organización Inteligente")
        self.btn_organizacion_inteligente.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 120, 212, 0.7);
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: rgba(0, 140, 232, 0.7);
            }
            QPushButton:pressed {
                background-color: rgba(0, 100, 192, 0.7);
            }
        """)
        self.btn_organizacion_inteligente.clicked.connect(self.aplicar_organizacion_inteligente)
        layout_central.addWidget(self.btn_organizacion_inteligente)
        
        layout_central.addStretch()
        
        # Botones de acción
        botones = QHBoxLayout()
        self.btn_aceptar = QPushButton("Renombrar")
        self.btn_aceptar.setStyleSheet("""
            QPushButton {
                background-color: rgba(60, 60, 60, 0.7);
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: rgba(80, 80, 80, 0.7);
            }
            QPushButton:pressed {
                background-color: rgba(100, 100, 100, 0.7);
            }
        """)
        self.btn_cancelar = QPushButton("Cancelar")
        self.btn_cancelar.setStyleSheet("""
            QPushButton {
                background-color: rgba(60, 60, 60, 0.7);
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: rgba(80, 80, 80, 0.7);
            }
            QPushButton:pressed {
                background-color: rgba(100, 100, 100, 0.7);
            }
        """)
        self.btn_restaurar = QPushButton("Restaurar Último")
        self.btn_restaurar.setStyleSheet("""
            QPushButton {
                background-color: rgba(211, 47, 47, 0.7);
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: rgba(239, 83, 80, 0.7);
            }
            QPushButton:pressed {
                background-color: rgba(198, 40, 40, 0.7);
            }
            QPushButton:disabled {
                background-color: rgba(90, 90, 90, 0.7);
                color: rgba(158, 158, 158, 0.7);
            }
        """)
        
        self.btn_aceptar.clicked.connect(self.aceptar)
        self.btn_cancelar.clicked.connect(self.close)  # Cambiar de reject a close
        self.btn_restaurar.clicked.connect(self.restaurar_ultimo_renombrado)
        
        # Verificar si existe historial para habilitar/deshabilitar el botón
        self.btn_restaurar.setEnabled(os.path.exists(HISTORIAL_PATH))
        
        botones.addWidget(self.btn_restaurar)
        botones.addWidget(self.btn_aceptar)
        botones.addWidget(self.btn_cancelar)
        layout_central.addLayout(botones)
        
        # Panel derecho (vista previa)
        panel_derecho = CustomFrame()
        layout_derecho = QVBoxLayout(panel_derecho)
        layout_derecho.addWidget(QLabel("Vista Previa:"))
        self.lista_preview = CustomListWidget()
        layout_derecho.addWidget(self.lista_preview)
        
        # Añadir los paneles al layout principal
        layout.addWidget(panel_izquierdo)
        layout.addWidget(panel_central)
        layout.addWidget(panel_derecho)
        
        # Añadir el layout al contenido
        content_layout.addLayout(layout)
        
        # Conectar señales para actualización en tiempo real
        self.prefijo_edit.textChanged.connect(self.actualizar_vista_previa)
        self.nombre_base_edit.textChanged.connect(self.actualizar_vista_previa)
        self.sufijo_edit.textChanged.connect(self.actualizar_vista_previa)
        self.numero_inicial.valueChanged.connect(self.actualizar_vista_previa)
        self.digitos_spin.valueChanged.connect(self.actualizar_vista_previa)
        
        # Inicializar vista previa
        self.actualizar_vista_previa()
        
      
    def paintEvent(self, event):
        """Personaliza el pintado de la ventana para bordes redondeados"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Crear un path con bordes redondeados
        path = QPainterPath()
        path.addRoundedRect(0, 0, self.width(), self.height(), 10, 10)
        
        # Establecer el clip para que todo se dibuje dentro de los bordes redondeados
        painter.setClipPath(path)
        
        # Continuar con el pintado normal
        super().paintEvent(event)

    def showEvent(self, event):
        """Aplica el efecto acrílico cuando se muestra la ventana"""
        super().showEvent(event)
        
        # Aplicar efecto acrílico y bordes redondeados
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd, mode='dark')
        
        # Verificar si existe historial para habilitar/deshabilitar el botón de restaurar
        self.btn_restaurar.setEnabled(os.path.exists(HISTORIAL_PATH))
        
        # Asegurar que los grips de redimensionamiento estén visibles
        for grip in self.window_resizer.size_grips.values():
            grip.raise_()
            grip.show()

    def mousePressEvent(self, event):
        """Maneja eventos de clic para mover la ventana"""
        if not self.window_resizer.handle_mouse_press(event):
            if event.button() == Qt.MouseButton.LeftButton:
                self._drag_pos = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
                event.accept()

    def mouseMoveEvent(self, event):
        """Maneja el movimiento del ratón para mover la ventana"""
        if not self.window_resizer.handle_mouse_move(event):
            if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, '_drag_pos'):
                self.move(event.globalPosition().toPoint() - self._drag_pos)
                event.accept()

    def mouseReleaseEvent(self, event):
        """Finaliza el movimiento"""
        if not self.window_resizer.handle_mouse_release(event):
            if event.button() == Qt.MouseButton.LeftButton:
                event.accept()

    def resizeEvent(self, event):
        """Actualiza el tamaño del widget de fondo y los grips cuando se redimensiona la ventana"""
        super().resizeEvent(event)
        if hasattr(self, 'background_widget'):
            self.background_widget.setGeometry(0, 0, self.width(), self.height())
        
        # Actualizar las posiciones de los grips de redimensionamiento
        if hasattr(self, 'window_resizer'):
            self.window_resizer.update_positions()

    def generar_nuevo_nombre(self, indice, extension):
        # Formatear el número con la cantidad de dígitos especificada
        numero = str(self.numero_inicial.value() + indice).zfill(self.digitos_spin.value())
        # Construir el nuevo nombre
        nuevo_nombre = f"{self.prefijo_edit.text()}{self.nombre_base_edit.text()}{numero}{self.sufijo_edit.text()}{extension}"
        return nuevo_nombre
    
    def actualizar_vista_previa(self):
        self.lista_preview.clear()
        for i, archivo_path in enumerate(self.archivos_seleccionados):
            nombre_base, extension = os.path.splitext(os.path.basename(archivo_path))
            nuevo_nombre = self.generar_nuevo_nombre(i, extension)
            self.lista_preview.addItem(nuevo_nombre)

    def actualizar_orden_archivos(self):
        """Actualiza el orden de los archivos seleccionados según el orden en la lista"""
        nuevos_archivos = []
        nuevos_nombres = []
        
        # Obtener el nuevo orden de los archivos
        for i in range(self.lista_original.count()):
            nombre_archivo = self.lista_original.item(i).text()
            # Buscar la ruta completa correspondiente al nombre
            for j, ruta in enumerate(self.archivos_seleccionados):
                if os.path.basename(ruta) == nombre_archivo:
                    nuevos_archivos.append(ruta)
                    nuevos_nombres.append(nombre_archivo)
                    break
        
        # Actualizar las listas con el nuevo orden
        self.archivos_seleccionados = nuevos_archivos
        self.nombres_originales = nuevos_nombres
        
        # Actualizar la vista previa con el nuevo orden
        self.actualizar_vista_previa()

    def aceptar(self):
        # Verificar si hay archivos para renombrar
        if not self.archivos_seleccionados:
            QMessageBox.warning(self, "Advertencia", "No hay archivos seleccionados para renombrar.")
            return
        
        # Confirmar la acción
        respuesta = QMessageBox.question(
            self, 
            "Confirmar renombrado", 
            f"¿Está seguro de renombrar {len(self.archivos_seleccionados)} archivos?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            errores = []
            renombrados = 0
            historial = []
            
            # Procesar cada archivo
            for i, archivo_path in enumerate(self.archivos_seleccionados):
                try:
                    # Obtener el directorio y la extensión
                    directorio = os.path.dirname(archivo_path)
                    nombre_original = os.path.basename(archivo_path)
                    _, extension = os.path.splitext(nombre_original)
                    
                    # Generar el nuevo nombre
                    nuevo_nombre = self.generar_nuevo_nombre(i, extension)
                    nuevo_path = os.path.join(directorio, nuevo_nombre)
                    
                    # Verificar si el archivo destino ya existe
                    if os.path.exists(nuevo_path) and archivo_path.lower() != nuevo_path.lower():
                        errores.append(f"Ya existe un archivo con el nombre: {nuevo_nombre}")
                        continue
                    
                    # Guardar en historial antes de renombrar
                    historial.append({
                        "ruta_original": archivo_path,
                        "nombre_original": nombre_original,
                        "ruta_nueva": nuevo_path,
                        "nombre_nuevo": nuevo_nombre
                    })
                    
                    # Renombrar el archivo
                    os.rename(archivo_path, nuevo_path)
                    renombrados += 1
                    
                except Exception as e:
                    errores.append(f"Error al renombrar {os.path.basename(archivo_path)}: {str(e)}")
            
            # Guardar historial si se renombró al menos un archivo
            if renombrados > 0:
                try:
                    with open(HISTORIAL_PATH, 'w', encoding='utf-8') as f:
                        json.dump(historial, f, ensure_ascii=False, indent=2)
                    # Habilitar el botón de restaurar
                    self.btn_restaurar.setEnabled(True)
                except Exception as e:
                    print(f"Error al guardar historial: {e}")
            
            if errores:
                mensaje = f"Se renombraron {renombrados} archivos.\n\nErrores ({len(errores)}):\n" + "\n".join(errores[:5])
                if len(errores) > 5:
                    mensaje += f"\n... y {len(errores) - 5} errores más."
                QMessageBox.warning(self, "Resultado del renombrado", mensaje)
            else:
                QMessageBox.information(self, "Éxito", f"Se renombraron {renombrados} archivos correctamente.")
            
            # Actualizar la vista del explorador si es posible
            if self.parent and hasattr(self.parent, 'show_drive_contents') and hasattr(self.parent, 'current_drive'):
                self.parent.show_drive_contents(self.parent.current_drive, refresh=True)
            
            # Actualizar la lista de archivos originales con los nuevos nombres
            self.actualizar_listas_despues_de_renombrar(historial)

    def restaurar_ultimo_renombrado(self):
        """Restaura los archivos a sus nombres originales según el último historial"""
        try:
            # Verificar si existe el archivo de historial
            if not os.path.exists(HISTORIAL_PATH):
                QMessageBox.warning(self, "Advertencia", "No hay historial de renombrado para restaurar.")
                return
            
            # Cargar el historial
            with open(HISTORIAL_PATH, 'r', encoding='utf-8') as f:
                historial = json.load(f)
            
            if not historial:
                QMessageBox.warning(self, "Advertencia", "El historial de renombrado está vacío.")
                return
            
            # Confirmar la acción
            respuesta = QMessageBox.question(
                self, 
                "Confirmar restauración", 
                f"¿Está seguro de restaurar {len(historial)} archivos a sus nombres originales?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if respuesta == QMessageBox.StandardButton.Yes:
                errores = []
                restaurados = 0
                
                # Procesar cada archivo en orden inverso para evitar conflictos
                for item in reversed(historial):
                    try:
                        ruta_actual = item["ruta_nueva"]
                        ruta_original = os.path.join(os.path.dirname(item["ruta_original"]), item["nombre_original"])
                        
                        # Verificar si el archivo existe
                        if not os.path.exists(ruta_actual):
                            errores.append(f"No se encuentra el archivo: {item['nombre_nuevo']}")
                            continue
                        
                        # Verificar si el destino ya existe
                        if os.path.exists(ruta_original) and ruta_actual.lower() != ruta_original.lower():
                            errores.append(f"Ya existe un archivo con el nombre original: {item['nombre_original']}")
                            continue
                        
                        # Renombrar el archivo de vuelta a su nombre original
                        os.rename(ruta_actual, ruta_original)
                        restaurados += 1
                        
                    except Exception as e:
                        errores.append(f"Error al restaurar {item['nombre_nuevo']}: {str(e)}")
                
                # Eliminar el historial después de restaurar
                if restaurados > 0:
                    try:
                        os.remove(HISTORIAL_PATH)
                        self.btn_restaurar.setEnabled(False)
                    except Exception as e:
                        print(f"Error al eliminar historial: {e}")
                
                if errores:
                    mensaje = f"Se restauraron {restaurados} archivos.\n\nErrores ({len(errores)}):\n" + "\n".join(errores[:5])
                    if len(errores) > 5:
                        mensaje += f"\n... y {len(errores) - 5} errores más."
                    QMessageBox.warning(self, "Resultado de la restauración", mensaje)
                else:
                    QMessageBox.information(self, "Éxito", f"Se restauraron {restaurados} archivos correctamente.")
                
                # Actualizar la vista del explorador si es posible
                if self.parent and hasattr(self.parent, 'show_drive_contents') and hasattr(self.parent, 'current_drive'):
                    self.parent.show_drive_contents(self.parent.current_drive, refresh=True)
                
                # Actualizar las listas después de restaurar
                self.actualizar_listas_despues_de_restaurar()
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al restaurar los archivos: {str(e)}")

    def actualizar_listas_despues_de_renombrar(self, historial):
        """Actualiza las listas después de renombrar los archivos"""
        try:
            # Actualizar la lista de archivos seleccionados con las nuevas rutas
            nuevas_rutas = []
            for item in historial:
                nuevas_rutas.append(item["ruta_nueva"])
            
            self.archivos_seleccionados = nuevas_rutas
            
            # Actualizar la lista original con los nuevos nombres
            self.lista_original.clear()
            for ruta in nuevas_rutas:
                self.lista_original.addItem(os.path.basename(ruta))
            
            # Actualizar la vista previa
            self.actualizar_vista_previa()
            
        except Exception as e:
            print(f"Error al actualizar listas después de renombrar: {e}")

    def actualizar_listas_despues_de_restaurar(self):
        """Actualiza las listas después de restaurar los archivos"""
        try:
            # Actualizar la lista original con los nombres originales
            self.lista_original.clear()
            for ruta in self.archivos_seleccionados:
                # Verificar si el archivo existe después de la restauración
                if os.path.exists(ruta):
                    self.lista_original.addItem(os.path.basename(ruta))
                else:
                    # Buscar el archivo restaurado en el mismo directorio
                    directorio = os.path.dirname(ruta)
                    nombre_base = os.path.basename(ruta)
                    for nombre in os.listdir(directorio):
                        if nombre.lower() == nombre_base.lower():
                            nueva_ruta = os.path.join(directorio, nombre)
                            self.lista_original.addItem(nombre)
                            # Actualizar la ruta en archivos_seleccionados
                            indice = self.archivos_seleccionados.index(ruta)
                            self.archivos_seleccionados[indice] = nueva_ruta
                            break
            
            # Actualizar la vista previa
            self.actualizar_vista_previa()
            
        except Exception as e:
            print(f"Error al actualizar listas después de restaurar: {e}")

    def reject(self):
        """Sobrescribir el método reject para cerrar la ventana sin bloquear"""
        print("reject llamado")  # Depuración
        # Forzar el cierre directamente
        QDialog.reject(self)
        self.close()
        self.deleteLater()
    
    def closeEvent(self, event):
        """Manejar el evento de cierre de la ventana"""
        print("closeEvent llamado")  # Depuración
        
        # Asegurarse de que los recursos se liberen correctamente
        if hasattr(self, 'window_resizer'):
            # Ocultar los grips antes de cerrar
            for grip in self.window_resizer.size_grips.values():
                grip.hide()
        
        # Aceptar el evento de cierre explícitamente
        event.accept()
        
        # Llamar al método close de la clase base
        super().closeEvent(event)
        
        # Forzar la destrucción del widget
        self.deleteLater()

    def detectar_nombre_comun(self):
        """
        Detecta el nombre común que más se repite entre los archivos seleccionados.
        """
        try:
            if not self.nombres_originales:
                return ""
            
            # Patrones a remover antes de buscar el nombre común
            patrones_a_limpiar = [
                r'\d+x\d+',              # Formato 1x01, 2x15, etc
                r'S\d+E\d+',             # Formato S01E01, etc
                r'T\d+C\d+',             # Formato T01C01, etc
                r'\[.*?\]|\(.*?\)',      # Contenido entre [] o ()
                r'(?i)temp\s*\d+',       # Temporada X
                r'(?i)season\s*\d+',     # Season X
                r'(?i)cap[ií]tulo?\s*\d+', # Capítulo X
                r'(?i)ep(?:isodio)?\s*\d+', # Episodio X
                r'\d{1,2}[-/]\d{1,2}[-/]\d{2,4}', # Fechas
                r'(?i)(720p|1080p|4k|hd|full\s*hd)', # Calidad
                r'(?i)esp|spanish|castellano|latino', # Idiomas
                r'\d{3,4}p',             # Resolución
                r'(?i)x264|x265|h264|h265|xvid', # Códecs
                r'(?i)web-dl|bluray|brrip|dvdrip', # Fuentes
            ]

            def limpiar_nombre(nombre):
                """Limpia el nombre removiendo todos los patrones no deseados"""
                nombre_limpio = nombre.strip()
                for patron in patrones_a_limpiar:
                    nombre_limpio = re.sub(patron, '', nombre_limpio, flags=re.IGNORECASE)
                # Limpiar caracteres especiales y espacios múltiples
                nombre_limpio = re.sub(r'[^\w\s-]', ' ', nombre_limpio)
                nombre_limpio = re.sub(r'\s+', ' ', nombre_limpio)
                return nombre_limpio.strip()

            # Limpiar nombres y encontrar el nombre común
            nombres_limpios = []
            for nombre in self.nombres_originales:
                nombre_base, _ = os.path.splitext(nombre)
                nombre_limpio = limpiar_nombre(nombre_base)
                if nombre_limpio:  # Solo agregar si no está vacío
                    nombres_limpios.append(nombre_limpio)

            if not nombres_limpios:
                return ""

            # Encontrar la parte común más larga que tenga sentido
            nombre_comun = nombres_limpios[0]
            for nombre in nombres_limpios[1:]:
                palabras1 = nombre_comun.split()
                palabras2 = nombre.split()
                
                # Encontrar las palabras comunes en el mismo orden
                palabras_comunes = []
                i = 0
                while i < len(palabras1):
                    if i < len(palabras2) and palabras1[i].lower() == palabras2[i].lower():
                        palabras_comunes.append(palabras1[i])
                    else:
                        break
                    i += 1
                
                nombre_comun = ' '.join(palabras_comunes)

            # Verificar que el nombre común tenga sentido
            if len(nombre_comun.split()) >= 1 and len(nombre_comun) >= 3:
                return " - " + nombre_comun.strip()

            return ""

        except Exception as e:
            print(f"Error al detectar nombre común: {e}")
            return ""

    def aplicar_organizacion_inteligente(self):
        """
        Analiza los nombres de los archivos para detectar patrones de numeración de capítulos,
        ordena los archivos según estos números y configura el número inicial correctamente.
        También detecta temporadas en series y ajusta el sufijo apropiadamente.
        """
        try:
            if not self.archivos_seleccionados:
                return
            
            # Patrones para detectar números de capítulo y temporada (en orden de prioridad)
            patrones_temporada = [
                # Patrón para "SxxEyy" (S01E02, S1E2, etc.)
                r's(\d+)e\d+',
                # Patrón para "TxxCyy" (T01C02, T1C2, etc.)
                r't(\d+)c\d+',
                # Patrón para "xxxyy" (1x02, 01x02, etc.)
                r'(\d+)x\d+',
                # Patrón para "Temporada x" o "Temp x"
                r'(?:temporada|temp)[\s\.\-]*(\d+)',
                # Patrón para [Txx] o (Txx)
                r'[\[\(]t(\d+)[\]\)]',
                # Patrón para "Season x"
                r'season[\s\.\-]*(\d+)'
            ]
            
            patrones_capitulo = [
                # Patrón para "Cap-X", "Capítulo X", "Capitulo X", etc.
                r'(?:cap(?:i|í|)t?(?:u|ú)lo[\s\-]*|cap[\s\-]*)(\d+)',
                # Patrón para [Cap-X], (Cap X), etc.
                r'[\[\(](?:cap(?:i|í|)t?(?:u|ú)lo[\s\-]*|cap[\s\-]*)(\d+)[\]\)]',
                # Patrón para "Episodio X", "Ep X", etc.
                r'(?:episodio[\s\-]*|ep[\s\-]*)(\d+)',
                # Patrón para [Episodio X], (Ep X), etc.
                r'[\[\(](?:episodio[\s\-]*|ep[\s\-]*)(\d+)[\]\)]',
                # Patrón para "SxxEyy" (S01E02, S1E2, etc.)
                r's\d+e(\d+)',
                # Patrón para "TxxCyy" (T01C02, T1C2, etc.)
                r't\d+c(\d+)',
                # Patrón para "xxxyy" (1x02, 01x02, etc.)
                r'\d+x(\d+)',
                # Patrón para números entre corchetes o paréntesis que no sean fechas
                r'[\[\(](\d{1,3})[\]\)](?!\s*[\-\/]\s*\d{1,2}[\-\/]\s*\d{2,4})',
                # Patrón para números aislados (último recurso, menos confiable)
                r'(?<!\d)(\d{1,3})(?!\d)(?!\s*[\-\/]\s*\d{1,2}[\-\/]\s*\d{2,4})'
            ]
            
            # Detectar si es una serie con temporadas
            es_serie_con_temporadas = False
            temporada_detectada = None
            nombre_serie = ""
            
            # Analizar el primer archivo para detectar si es una serie con temporadas
            if self.archivos_seleccionados:
                primer_nombre = os.path.basename(self.archivos_seleccionados[0])
                
                # Buscar patrones de temporada
                for patron in patrones_temporada:
                    match = re.search(patron, primer_nombre, re.IGNORECASE)
                    if match:
                        try:
                            temporada_detectada = int(match.group(1))
                            es_serie_con_temporadas = True
                            
                            # Extraer el nombre de la serie
                            # Buscar la parte antes del patrón de temporada
                            pos_temporada = re.search(patron, primer_nombre, re.IGNORECASE).span()[0]
                            nombre_serie_candidato = primer_nombre[:pos_temporada].strip()
                            
                            # Limpiar el nombre de la serie
                            nombre_serie_candidato = re.sub(r'[\.\-_]+', ' ', nombre_serie_candidato).strip()
                            
                            # Si el nombre es muy corto, buscar en todos los archivos un nombre común
                            if len(nombre_serie_candidato) < 3:
                                # Usar el método existente para detectar nombre común
                                nombre_serie = self.detectar_nombre_comun().strip()
                                if nombre_serie.startswith(" - "):
                                    nombre_serie = nombre_serie[3:].strip()
                            else:
                                nombre_serie = nombre_serie_candidato
                            
                            break
                        except (ValueError, IndexError):
                            continue
            
            # Extraer números de capítulo y asociarlos con sus archivos
            archivos_con_numeros = []
            for archivo_path in self.archivos_seleccionados:
                nombre = os.path.basename(archivo_path)
                numero_capitulo = None
                
                # Probar cada patrón en orden
                for patron in patrones_capitulo:
                    match = re.search(patron, nombre, re.IGNORECASE)
                    if match:
                        try:
                            numero_capitulo = int(match.group(1))
                            break  # Usar el primer patrón que coincida
                        except (ValueError, IndexError):
                            continue
                
                # Si no se encontró un número, asignar un valor alto para que quede al final
                if numero_capitulo is None:
                    numero_capitulo = 9999
                
                archivos_con_numeros.append((archivo_path, numero_capitulo))
            
            # Ordenar los archivos por número de capítulo
            archivos_con_numeros.sort(key=lambda x: x[1])
            
            # Actualizar las listas con el nuevo orden
            nuevos_archivos = [item[0] for item in archivos_con_numeros]
            nuevos_nombres = [os.path.basename(ruta) for ruta in nuevos_archivos]
            
            self.archivos_seleccionados = nuevos_archivos
            self.nombres_originales = nuevos_nombres
            
            # Actualizar la lista visual
            self.lista_original.clear()
            self.lista_original.addItems(nuevos_nombres)
            
            # Establecer el número inicial basado en el primer archivo (si se encontró un número)
            if archivos_con_numeros and archivos_con_numeros[0][1] != 9999:
                self.numero_inicial.setValue(archivos_con_numeros[0][1])
            
            # Si es una serie con temporadas, configurar el sufijo apropiadamente
            if es_serie_con_temporadas and temporada_detectada is not None and nombre_serie:
                # Configurar el sufijo con el nombre de la serie y la temporada
                nuevo_sufijo = f" - {nombre_serie} (T{temporada_detectada})"
                self.sufijo_edit.setText(nuevo_sufijo)
                
                # Limpiar el prefijo y el nombre base para que solo quede el número
                self.prefijo_edit.setText("")
                self.nombre_base_edit.setText("")
            
            # Actualizar la vista previa
            self.actualizar_vista_previa()
            
            if es_serie_con_temporadas:
                QMessageBox.information(self, "Organización Inteligente", 
                                      f"Se ha detectado la serie '{nombre_serie}' Temporada {temporada_detectada}.\n"
                                      f"Los archivos han sido organizados según los números de capítulo.")
            else:
                QMessageBox.information(self, "Organización Inteligente", 
                                      "Se han organizado los archivos según los números de capítulo detectados.")
            
        except Exception as e:
            print(f"Error en organización inteligente: {e}")
            QMessageBox.warning(self, "Error", f"Error al organizar archivos: {str(e)}")
