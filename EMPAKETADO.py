import os
import shutil
import re
from PyQt6.QtCore import <PERSON><PERSON><PERSON><PERSON>
def create_empaketado(main_window):
    selected_items = main_window.file_tree_widget.selectedItems()
    if not selected_items:
        print("No hay archivos o carpetas seleccionados.")
        return
    folder_name = "EMPAKETADO"
    empaketado_folder_path = os.path.join(main_window.current_drive, folder_name)
    counter = 1
    while os.path.exists(empaketado_folder_path):
        folder_name = f"EMPAKETADO_{counter}"
        empaketado_folder_path = os.path.join(main_window.current_drive, folder_name)
        counter += 1
    try:
        os.makedirs(empaketado_folder_path)
    except Exception as e:
        print(f"Error al crear la carpeta: {e}")
        return
    for item in selected_items:
        item_name = item.text(0)
        item_path = os.path.join(main_window.current_drive, item_name)
        new_path = os.path.join(empaketado_folder_path, item_name)
        try:
            if os.path.isfile(item_path):
                shutil.move(item_path, new_path)
            elif os.path.isdir(item_path):
                shutil.move(item_path, os.path.join(empaketado_folder_path, os.path.basename(item_path)))
            print(f"Movido {item_path} a {new_path}")
            index = main_window.file_tree_widget.indexOfTopLevelItem(item)
            main_window.file_tree_widget.takeTopLevelItem(index)
        except Exception as e:
            print(f"Error al mover {item_path}: {e}")
    main_window.file_tree_widget.clear()
    main_window.show_drive_contents(main_window.current_drive)
    print(f"Empaketado creado en: {empaketado_folder_path}")

    def select_and_rename_empaketado(): # Encontrar y seleccionar el nuevo item EMPAKETADO
        for i in range(main_window.file_tree_widget.topLevelItemCount()):
            item = main_window.file_tree_widget.topLevelItem(i)
            if item.text(0) == folder_name:
                main_window.file_tree_widget.setCurrentItem(item)
                main_window.show_rename_dialog(folder_name)
                break
    QTimer.singleShot(100, select_and_rename_empaketado) # Usar QTimer para asegurar que la interfaz se haya actualizado antes de seleccionar y renombrar

def remove_dates(name):
    return re.sub(r'\d{4}-\d{2}-\d{2}', '', name).strip()

def find_common_name(item_names):
    if not item_names:
        return None
    common_name = os.path.commonprefix(item_names).strip()
    if common_name and len(common_name) > 1:
        return common_name
    return None
