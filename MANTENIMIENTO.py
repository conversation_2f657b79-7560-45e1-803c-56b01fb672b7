import subprocess
import os
import threading
from PyQt6.QtWidgets import QMainWindow, QRadioButton, QVBoxLayout, QButtonGroup, QDialog, QPushButton, QLabel, QHBoxLayout
from PyQt6.QtGui import QCursor, QPainter, QColor, QPainterPath
from PyQt6.QtCore import Qt, QTimer, QThreadPool, QRunnable, pyqtSignal, QObject
from BOTONES import CustomMessageBox
import ctypes
from APARIENCIA import ACCENT_POLICY, WINDOWCOMPOSITIONATTRIBDATA, ACCENT_ENABLE_ACRYLICBLURBEHIND, WCA_ACCENT_POLICY
from CREAR import create_repair_icon, create_format_icon
class WorkerSignals(QObject):
    finished = pyqtSignal()
    error = pyqtSignal(str)

class Worker(QRunnable):
    def __init__(self, fn, *args, **kwargs):
        super().__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = WorkerSignals()

    def run(self):
        try:
            self.fn(*self.args, **self.kwargs)
        except Exception as e:
            self.signals.error.emit(str(e))
        finally:
            self.signals.finished.emit()

def is_disk_accessible(drive_letter):
    try:
        test_path = os.path.join(drive_letter, "test_access")
        with open(test_path, 'w') as f:
            f.write("test")
        os.remove(test_path)
        return True
    except Exception as e:
        print(f"Disco {drive_letter} inaccesible: {e}")
        return False

def obtener_nombre_volumen(drive_letter):
    try:
        result = subprocess.run(
            ["cmd", "/c", f"vol {drive_letter}"],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if "El volumen de la unidad" in line:
                    return line.split("es")[-1].strip()
    except Exception as e:
        print(f"Error al obtener el nombre del volumen: {e}")
    return ""

def borrar_informacion_disco(main_window, drive_letter):
    if is_disk_accessible(drive_letter):
        def borrar_thread():
            try:
                volume_name = obtener_nombre_volumen(drive_letter)
                result = subprocess.run(
                    ["cmd", "/c", f"format {drive_letter} /q /x /y /v:{volume_name}"],
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                if result.returncode == 0:
                    print(f"Borrado de información completado para {drive_letter}.")
                else:
                    print(f"Error al borrar información de {drive_letter}: {result.stderr}")
            except Exception as e:
                print(f"Excepción al intentar borrar información de {drive_letter}: {e}")
            finally:
                QTimer.singleShot(0, lambda: main_window.restaurar_icono_disco(drive_letter))

        worker = Worker(borrar_thread)
        QThreadPool.globalInstance().start(worker)
    else:
        print(f"El disco {drive_letter} no está accesible para borrar información.")
        main_window.restaurar_icono_disco(drive_letter)

def repair_disks_in_background(main_window, disks_info):
    # Crear el diálogo con el mismo estilo que el de confirmación
    dialog = QDialog(main_window)
    dialog.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
    dialog.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
    dialog.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
    
    # Aplicar efecto acrílico y bordes redondeados
    hwnd = int(dialog.winId())
    
    # Aplicar bordes redondeados
    ctypes.windll.dwmapi.DwmSetWindowAttribute(
        hwnd,
        33,  # DWMWA_WINDOW_CORNER_PREFERENCE
        ctypes.byref(ctypes.c_int(2)),  # DWMWCP_ROUND
        ctypes.sizeof(ctypes.c_int)
    )
    
    # Aplicar efecto acrílico
    accent = ACCENT_POLICY()
    accent.AccentState = ACCENT_ENABLE_ACRYLICBLURBEHIND
    accent.GradientColor = 0x99000000
    accent.AccentFlags = 0x20 | 0x40 | 0x80 | 0x100
    
    data = WINDOWCOMPOSITIONATTRIBDATA()
    data.Attribute = WCA_ACCENT_POLICY
    data.SizeOfData = ctypes.sizeof(accent)
    data.Data = ctypes.cast(ctypes.pointer(accent), ctypes.POINTER(ctypes.c_int))
    ctypes.windll.user32.SetWindowCompositionAttribute(hwnd, ctypes.byref(data))

    # Layout principal
    layout = QVBoxLayout(dialog)
    layout.setContentsMargins(15, 15, 15, 15)
    layout.setSpacing(10)

    # Mensaje
    message = QLabel("¿REPARAR?")
    message.setStyleSheet("""
        QLabel {
            color: white;
            font-size: 14px;
            font-weight: bold;
            background: transparent;
        }
    """)
    message.setAlignment(Qt.AlignmentFlag.AlignCenter)
    layout.addWidget(message)

    # Radio buttons
    radio_layout = QVBoxLayout()
    repair_radio = QRadioButton("REPARAR")
    format_radio = QRadioButton("BORRAR DISCO")
    
    radio_style = """
        QRadioButton {
            color: white;
            font-size: 14px;
            font-weight: bold;
            background: transparent;
            padding: 5px;
        }
        QRadioButton::indicator {
            width: 18px;
            height: 18px;
            border-radius: 9px;
            border: 2px solid white;
        }
        QRadioButton::indicator:checked {
            background-color: #0078d7;
            border: 2px solid white;
        }
        QRadioButton::indicator:unchecked {
            background-color: transparent;
            border: 2px solid white;
        }
    """
    repair_radio.setStyleSheet(radio_style)
    format_radio.setStyleSheet(radio_style)
    repair_radio.setChecked(True)
    
    radio_layout.addWidget(repair_radio)
    radio_layout.addWidget(format_radio)
    layout.addLayout(radio_layout)

    # Botones
    button_layout = QHBoxLayout()
    button_layout.setSpacing(8)

    ok_button = QPushButton("OK")
    ok_button.setStyleSheet("""
        QPushButton {
            background-color: #0078d7;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 5px 15px;
            font-weight: bold;
            min-width: 60px;
            height: 24px;
        }
        QPushButton:hover {
            background-color: #1e90ff;
        }
        QPushButton:pressed {
            background-color: #005fb3;
        }
    """)
    ok_button.setCursor(Qt.CursorShape.PointingHandCursor)
    ok_button.clicked.connect(dialog.accept)

    cancel_button = QPushButton("CANCELAR")
    cancel_button.setStyleSheet("""
        QPushButton {
            background-color: #d32f2f;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 5px 15px;
            font-weight: bold;
            min-width: 60px;
            height: 24px;
        }
        QPushButton:hover {
            background-color: #ef5350;
        }
        QPushButton:pressed {
            background-color: #b71c1c;
        }
    """)
    cancel_button.setCursor(Qt.CursorShape.PointingHandCursor)
    cancel_button.clicked.connect(dialog.reject)

    button_layout.addWidget(ok_button)
    button_layout.addWidget(cancel_button)
    layout.addLayout(button_layout)

    def actualizar_iconos():
        for volume_name, drive_letter in disks_info:
            if format_radio.isChecked():
                main_window.cambiar_icono_disco(drive_letter, create_format_icon(size=32))
            elif repair_radio.isChecked():
                # Usar directamente la función create_repair_icon
                main_window.cambiar_icono_disco(drive_letter, create_repair_icon(size=32))
            else:
                main_window.restaurar_icono_disco(drive_letter)

    repair_radio.toggled.connect(actualizar_iconos)
    format_radio.toggled.connect(actualizar_iconos)
    actualizar_iconos()

    # Agregar método paintEvent para el fondo con bordes redondeados
    def paintEvent(event):
        painter = QPainter(dialog)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = dialog.rect()
        path = QPainterPath()
        path.addRoundedRect(
            float(rect.x()), 
            float(rect.y()), 
            float(rect.width()), 
            float(rect.height()), 
            8.0, 
            8.0
        )
        
        painter.fillPath(path, QColor(0, 0, 0, 50))

    dialog.paintEvent = paintEvent

    if dialog.exec() == QDialog.DialogCode.Accepted:
        if repair_radio.isChecked():
            for volume_name, drive_letter in disks_info:
                def repair_thread(drive):
                    try:
                        # Obtener letra de unidad sin los dos puntos
                        drive_letter_clean = drive.replace(":", "")
                        
                        # Comando PowerShell avanzado para reparación de disco
                        ps_command = f'''
                        $ErrorActionPreference = "Stop"
                        # Obtener información del volumen
                        $volume = Get-Volume -DriveLetter {drive_letter_clean} -ErrorAction SilentlyContinue
                        
                        if ($volume) {{
                            # Verificar el estado de salud del volumen
                            $healthStatus = $volume.HealthStatus
                            Write-Output "Estado inicial del volumen: $healthStatus"
                            
                            # Reparar el volumen usando Repair-Volume (más avanzado que chkdsk)
                            Write-Output "Iniciando reparación del volumen {drive}..."
                            Repair-Volume -DriveLetter {drive_letter_clean} -OfflineScanAndFix
                            
                            # Verificar el resultado
                            $volumeAfter = Get-Volume -DriveLetter {drive_letter_clean} -ErrorAction SilentlyContinue
                            Write-Output "Estado final del volumen: $($volumeAfter.HealthStatus)"
                            
                            # Optimizar el volumen después de la reparación
                            Write-Output "Optimizando volumen {drive}..."
                            Optimize-Volume -DriveLetter {drive_letter_clean} -Defrag -ReTrim -SlabConsolidate -Verbose
                            
                            Write-Output "Proceso de reparación y optimización completado para {drive}."
                        }} else {{
                            Write-Error "No se pudo encontrar el volumen {drive}"
                        }}
                        '''
                        
                        # Configurar para ocultar la consola
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE
                        
                        # Ejecutar PowerShell con el comando avanzado
                        result = subprocess.run(
                            ["powershell", "-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", ps_command],
                            capture_output=True,
                            text=True,
                            startupinfo=startupinfo,
                            creationflags=subprocess.CREATE_NO_WINDOW
                        )
                        
                        if result.returncode == 0:
                            print(f"Reparación completada para {drive}:")
                            print(result.stdout)
                        else:
                            print(f"Error al reparar {drive}: {result.stderr}")
                    except Exception as e:
                        print(f"Excepción al intentar reparar {drive}: {e}")
                    finally:
                        # Actualizar la UI de forma segura
                        QTimer.singleShot(0, lambda d=drive: main_window.restaurar_icono_disco(d))
                
                # Crear worker con señales para mejor seguimiento
                worker = Worker(repair_thread, drive_letter)
                worker.signals.finished.connect(lambda: print(f"Proceso de reparación finalizado para {drive_letter}"))
                worker.signals.error.connect(lambda err: print(f"Error en reparación de {drive_letter}: {err}"))
                
                # Iniciar en el pool global de hilos
                QThreadPool.globalInstance().start(worker)
        elif format_radio.isChecked():
            for volume_name, drive_letter in disks_info:
                borrar_informacion_disco(main_window, drive_letter)
    else:
        for volume_name, drive_letter in disks_info:
            main_window.restaurar_icono_disco(drive_letter)
