import threading
import time
import traceback
import psutil
import os
import sys
import queue
import win32api
import win32con
from datetime import datetime
from PyQt6.QtCore import QObject, pyqtSignal, QTimer, QThread

class WMIThreadPool:
    """
    Clase para manejar llamadas WMI en hilos separados para evitar bloqueos en la UI.
    """
    def __init__(self, max_workers=2):
        self.queue = queue.Queue()
        self.results = {}
        self.lock = threading.Lock()
        self.workers = []
        self.max_workers = max_workers
        self.running = True
        self.task_counter = 0
        
    def start(self):
        """Inicia los trabajadores del pool."""
        self.running = True
        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker_loop, daemon=True, 
                                     name=f"WMI-Worker-{i}")
            worker.start()
            self.workers.append(worker)
        print(f"[WMIThreadPool] Iniciado con {self.max_workers} trabajadores")
        
    def stop(self):
        """Detiene los trabajadores del pool."""
        self.running = False
        # Esperar a que terminen los trabajadores (con timeout)
        for worker in self.workers:
            worker.join(timeout=0.5)  # No esperar demasiado
        print("[WMIThreadPool] Detenido")
        
    def _worker_loop(self):
        """Bucle principal de cada trabajador."""
        while self.running:
            try:
                # Usar timeout para poder comprobar periódicamente si debemos detenernos
                task_id, func, args, kwargs = self.queue.get(timeout=1.0)
                try:
                    result = func(*args, **kwargs)
                    with self.lock:
                        self.results[task_id] = {"status": "completed", "result": result}
                except Exception as e:
                    with self.lock:
                        self.results[task_id] = {"status": "error", "error": str(e)}
                finally:
                    self.queue.task_done()
            except queue.Empty:
                # Timeout en la cola, seguir esperando
                continue
            except Exception as e:
                print(f"[WMIThreadPool] Error en worker: {e}")
                
    def submit_task(self, func, *args, **kwargs):
        """
        Envía una tarea al pool y devuelve un ID para consultar el resultado.
        """
        with self.lock:
            task_id = self.task_counter
            self.task_counter += 1
            self.results[task_id] = {"status": "pending"}
            
        self.queue.put((task_id, func, args, kwargs))
        return task_id
        
    def get_result(self, task_id, timeout=None, default=None):
        """
        Obtiene el resultado de una tarea, esperando si es necesario.
        
        Args:
            task_id: ID de la tarea
            timeout: Tiempo máximo de espera en segundos (None = esperar indefinidamente)
            default: Valor a devolver si hay timeout o error
            
        Returns:
            El resultado de la tarea o el valor por defecto
        """
        start_time = time.time()
        while timeout is None or time.time() - start_time < timeout:
            with self.lock:
                if task_id in self.results:
                    result_info = self.results[task_id]
                    if result_info["status"] == "completed":
                        # Limpiar el resultado para no acumular memoria
                        result = result_info["result"]
                        del self.results[task_id]
                        return result
                    elif result_info["status"] == "error":
                        # Limpiar el resultado para no acumular memoria
                        error = result_info["error"]
                        del self.results[task_id]
                        print(f"[WMIThreadPool] Error en tarea {task_id}: {error}")
                        return default
            
            # Esperar un poco antes de volver a comprobar
            time.sleep(0.05)
            
        # Timeout alcanzado
        print(f"[WMIThreadPool] Timeout esperando resultado de tarea {task_id}")
        return default

class FreezeDetector(QObject):
    """
    Clase que monitorea la aplicación para detectar congelamientos.
    Utiliza un hilo separado para verificar si la interfaz de usuario responde.
    """
    freeze_detected = pyqtSignal(str, float)
    freeze_resolved = pyqtSignal(float)
    
    def __init__(self, parent=None, check_interval=0.5, freeze_threshold=1.0):
        """
        Inicializa el detector de congelamientos.
        
        Args:
            parent: Objeto padre de Qt
            check_interval: Intervalo en segundos para verificar la respuesta
            freeze_threshold: Tiempo en segundos para considerar un congelamiento
        """
        super().__init__(parent)
        self.check_interval = check_interval
        self.freeze_threshold = freeze_threshold
        self.last_response_time = time.time()
        self.is_frozen = False
        self.freeze_start_time = 0
        self.running = False
        self.monitor_thread = None
        self.heartbeat_timer = QTimer(self)
        self.heartbeat_timer.timeout.connect(self.heartbeat)
        self.process = psutil.Process(os.getpid())
        
    def start(self):
        """Inicia el monitoreo de congelamientos."""
        if self.running:
            return
            
        self.running = True
        self.last_response_time = time.time()
        
        # Iniciar el timer de heartbeat en el hilo principal
        self.heartbeat_timer.start(int(self.check_interval * 500))  # La mitad del intervalo en ms
        
        # Iniciar el hilo de monitoreo
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        print("[FreezeDetector] Monitorización de congelamientos iniciada")
        
    def stop(self):
        """Detiene el monitoreo de congelamientos."""
        self.running = False
        self.heartbeat_timer.stop()
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1.0)
        print("[FreezeDetector] Monitorización de congelamientos detenida")
        
    def heartbeat(self):
        """Actualiza el tiempo de la última respuesta (llamado desde el hilo principal)."""
        self.last_response_time = time.time()
        
    def _monitor_loop(self):
        """Bucle principal de monitoreo que se ejecuta en un hilo separado."""
        while self.running:
            try:
                current_time = time.time()
                time_since_last_response = current_time - self.last_response_time
                
                # Verificar si hay congelamiento
                if time_since_last_response > self.freeze_threshold and not self.is_frozen:
                    self.is_frozen = True
                    self.freeze_start_time = current_time
                    freeze_info = self._collect_freeze_info()
                    self.freeze_detected.emit(freeze_info, time_since_last_response)
                    
                # Verificar si el congelamiento se ha resuelto
                elif time_since_last_response <= self.freeze_threshold and self.is_frozen:
                    self.is_frozen = False
                    freeze_duration = current_time - self.freeze_start_time
                    self.freeze_resolved.emit(freeze_duration)
                    
                time.sleep(self.check_interval)
            except Exception as e:
                print(f"[FreezeDetector] Error en el hilo de monitoreo: {e}")
                traceback.print_exc()
                time.sleep(1.0)  # Esperar un poco más en caso de error
                
    def _collect_freeze_info(self):
        """Recopila información sobre el estado actual del sistema durante un congelamiento."""
        try:
            info = []
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            info.append(f"=== CONGELAMIENTO DETECTADO A LAS {timestamp} ===")
            
            # Información de CPU y memoria
            info.append(f"CPU total: {psutil.cpu_percent(interval=0.1)}%")
            
            # Información del proceso actual
            try:
                with self.process.oneshot():
                    cpu_percent = self.process.cpu_percent(interval=0.1)
                    memory_info = self.process.memory_info()
                    memory_percent = self.process.memory_percent()
                    num_threads = self.process.num_threads()
                    
                    info.append(f"Proceso: CPU {cpu_percent:.1f}%, Memoria: {memory_info.rss / (1024 * 1024):.1f} MB ({memory_percent:.1f}%), Hilos: {num_threads}")
                    
                    # Obtener información de los hilos más activos
                    threads = self.process.threads()
                    threads.sort(key=lambda t: t.user_time + t.system_time, reverse=True)
                    
                    info.append("Hilos más activos:")
                    for i, thread in enumerate(threads[:5]):
                        info.append(f"  Hilo ID {thread.id}: Usuario {thread.user_time:.2f}s, Sistema {thread.system_time:.2f}s")
            except Exception as e:
                info.append(f"Error obteniendo información del proceso: {e}")
                
            # Obtener pilas de llamadas de los hilos de Python
            info.append("\nPilas de llamadas de hilos Python:\n")
            
            try:
                import sys
                import traceback
                
                # Obtener todos los hilos
                for thread_id, frame in sys._current_frames().items():
                    info.append(f"Hilo {thread_id}:")
                    stack = traceback.extract_stack(frame)
                    # Mostrar solo las últimas 10 llamadas para no sobrecargar el log
                    for filename, lineno, name, line in stack[-10:]:
                        if line:
                            info.append(f"  File \"{filename}\", line {lineno}, in {name}")
                            info.append(f"    {line}")
                    info.append("")
            except Exception as e:
                info.append(f"Error obteniendo pilas de llamadas: {e}")
                
            # Información de E/S de disco
            try:
                io_counters = self.process.io_counters()
                read_mb = io_counters.read_bytes / (1024 * 1024)
                write_mb = io_counters.write_bytes / (1024 * 1024)
                info.append(f"E/S de disco: Lectura {read_mb:.1f} MB, Escritura {write_mb:.1f} MB")
            except Exception as e:
                info.append(f"Error obteniendo información de E/S: {e}")
                
            return "\n".join(info)
        except Exception as e:
            return f"Error recopilando información del congelamiento: {e}"

class PerformanceMonitor(QThread):
    """
    Monitorea el rendimiento de la aplicación y registra información periódicamente.
    """
    performance_update = pyqtSignal(dict)
    
    def __init__(self, parent=None, interval=5.0):
        """
        Inicializa el monitor de rendimiento.
        
        Args:
            parent: Objeto padre de Qt
            interval: Intervalo en segundos para recopilar estadísticas
        """
        super().__init__(parent)
        self.interval = interval
        self.running = False
        self.process = psutil.Process(os.getpid())
        
    def run(self):
        """Método principal que se ejecuta cuando se inicia el hilo."""
        self.running = True
        while self.running:
            try:
                stats = self._collect_stats()
                self.performance_update.emit(stats)
                time.sleep(self.interval)
            except Exception as e:
                print(f"[PerformanceMonitor] Error: {e}")
                traceback.print_exc()
                time.sleep(1.0)
                
    def stop(self):
        """Detiene el monitor de rendimiento."""
        self.running = False
        self.wait(1000)
        
    def _collect_stats(self):
        """Recopila estadísticas de rendimiento del sistema y la aplicación."""
        stats = {}
        
        try:
            # Estadísticas del sistema
            stats['cpu_percent'] = psutil.cpu_percent(interval=0.1)
            stats['memory_percent'] = psutil.virtual_memory().percent
            
            # Estadísticas del proceso
            with self.process.oneshot():
                stats['process_cpu_percent'] = self.process.cpu_percent(interval=0.1)
                memory_info = self.process.memory_info()
                stats['process_memory_mb'] = memory_info.rss / (1024*1024)
                stats['process_memory_percent'] = self.process.memory_percent()
                stats['thread_count'] = self.process.num_threads()
                
                # Información de E/S
                try:
                    io_counters = self.process.io_counters()
                    stats['io_read_mb'] = io_counters.read_bytes / (1024*1024)
                    stats['io_write_mb'] = io_counters.write_bytes / (1024*1024)
                except Exception:
                    stats['io_error'] = True
                    
                # Información de hilos
                threads = self.process.threads()
                stats['top_threads'] = []
                for thread in sorted(threads, key=lambda x: x.user_time + x.system_time, reverse=True)[:3]:
                    stats['top_threads'].append({
                        'id': thread.id,
                        'user_time': thread.user_time,
                        'system_time': thread.system_time
                    })
                    
                # Información de handles (solo en Windows)
                if hasattr(self.process, 'num_handles'):
                    stats['handle_count'] = self.process.num_handles()
        except Exception as e:
            stats['error'] = str(e)
            
        return stats

class AppMonitor:
    """
    Clase principal que coordina la monitorización de la aplicación.
    Combina el detector de congelamientos y el monitor de rendimiento.
    """
    def __init__(self, main_window):
        """
        Inicializa el monitor de la aplicación.
        
        Args:
            main_window: Ventana principal de la aplicación
        """
        self.main_window = main_window
        self.freeze_detector = FreezeDetector(main_window, 
                                             check_interval=0.5,  # Verificar cada 0.5 segundos
                                             freeze_threshold=1.0)  # Considerar congelamiento después de 1 segundo
        
        self.performance_monitor = PerformanceMonitor(main_window, 
                                                     interval=10.0)  # Recopilar estadísticas cada 10 segundos
        
        self.wmi_pool = WMIThreadPool(max_workers=2)
        
        # Conectar señales
        self.freeze_detector.freeze_detected.connect(self._on_freeze_detected)
        self.freeze_detector.freeze_resolved.connect(self._on_freeze_resolved)
        self.performance_monitor.performance_update.connect(self._on_performance_update)
        
        # Crear archivo de registro
        self.log_file = self._setup_log_file()
        
    def start(self):
        """Inicia todos los monitores."""
        self._log("Iniciando monitorización de la aplicación")
        self.freeze_detector.start()
        self.performance_monitor.start()
        self.wmi_pool.start()
        
    def stop(self):
        """Detiene todos los monitores."""
        self._log("Deteniendo monitorización de la aplicación")
        self.freeze_detector.stop()
        self.performance_monitor.stop()
        self.wmi_pool.stop()
        
    def _on_freeze_detected(self, freeze_info, time_frozen):
        """Maneja la detección de un congelamiento."""
        self._log(f"ALERTA: Congelamiento detectado - {time_frozen:.2f} segundos sin respuesta")
        self._log(freeze_info)
        print(f"\n[ALERTA DE CONGELAMIENTO] La aplicación no ha respondido por {time_frozen:.2f} segundos")
        print(freeze_info)
        
    def _on_freeze_resolved(self, duration):
        """Maneja la resolución de un congelamiento."""
        self._log(f"Congelamiento resuelto - Duración: {duration:.2f} segundos")
        print(f"[CONGELAMIENTO RESUELTO] Duración: {duration:.2f} segundos")
        
    def _on_performance_update(self, stats):
        """Maneja las actualizaciones periódicas de rendimiento."""
        if stats.get('error'):
            self._log(f"Error en monitoreo de rendimiento: {stats['error']}")
            return
            
        # Registrar solo si hay valores anormales
        if (stats.get('process_cpu_percent', 0) > 30 or 
            stats.get('process_memory_percent', 0) > 50 or
            stats.get('thread_count', 0) > 100):
            
            log_msg = [
                f"Rendimiento: CPU {stats.get('process_cpu_percent', 0):.1f}%, "
                f"Memoria {stats.get('process_memory_mb', 0):.1f} MB ({stats.get('process_memory_percent', 0):.1f}%), "
                f"Hilos: {stats.get('thread_count', 0)}"
            ]
            
            if 'io_read_mb' in stats:
                log_msg.append(f"E/S: Lectura {stats['io_read_mb']:.1f} MB, Escritura {stats['io_write_mb']:.1f} MB")
                
            if 'handle_count' in stats:
                log_msg.append(f"Handles: {stats['handle_count']}")
                
            self._log(" | ".join(log_msg))
            
    def _setup_log_file(self):
        """Configura el archivo de registro para la monitorización usando la carpeta temporal única por instancia."""
        try:
            from ZETACOPY import get_instance_temp_dir
            log_dir = get_instance_temp_dir()
            os.makedirs(log_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_path = os.path.join(log_dir, f"zetacopy_monitor_{timestamp}.log")
            with open(log_path, 'w', encoding='utf-8') as f:
                f.write(f"=== Registro de monitorización de ZetaCopy - {datetime.now()} ===\n")
                f.write(f"PID: {os.getpid()}\n")
                f.write(f"Python: {sys.version}\n\n")
            return log_path
        except Exception as e:
            print(f"Error al configurar archivo de registro: {e}")
            return None
            
    def _log(self, message):
        """Escribe un mensaje en el archivo de registro."""
        if not self.log_file:
            return
            
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {message}\n")
        except Exception as e:
            print(f"Error al escribir en el archivo de registro: {e}")

    def execute_wmi_task(self, func, *args, **kwargs):
        """
        Ejecuta una tarea WMI en un hilo separado para evitar bloquear la UI.
        
        Args:
            func: Función WMI a ejecutar
            *args, **kwargs: Argumentos para la función
            
        Returns:
            Resultado de la función o None si hay error
        """
        task_id = self.wmi_pool.submit_task(func, *args, **kwargs)
        result = self.wmi_pool.get_result(task_id, timeout=10.0)
        if result is not None:
            return result
        else:
            print(f"[WMI] Error ejecutando tarea: {result}")
            return None

# Funciones auxiliares para reemplazar las llamadas WMI problemáticas

def get_disk_serial_number_async(drive_letter, app_monitor):
    """
    Versión asíncrona de get_disk_serial_number que usa el pool de hilos WMI.
    
    Args:
        drive_letter: Letra de unidad (ej: "C:")
        app_monitor: Instancia de AppMonitor
        
    Returns:
        Número de serie del disco o None si hay error
    """
    def _get_disk_serial(drive):
        import wmi
        try:
            c = wmi.WMI()
            for physical_disk in c.Win32_DiskDrive():
                for partition in physical_disk.associators("Win32_DiskDriveToDiskPartition"):
                    for logical_disk in partition.associators("Win32_LogicalDiskToPartition"):
                        if logical_disk.DeviceID == drive:
                            return physical_disk.SerialNumber.strip()
            return None
        except Exception as e:
            print(f"Error obteniendo número de serie del disco {drive}: {e}")
            return None
    
    return app_monitor.execute_wmi_task(_get_disk_serial, drive_letter)

def iniciar_monitorizacion(main_window):
    """
    Función para iniciar la monitorización desde el archivo principal.
    
    Args:
        main_window: Ventana principal de la aplicación
    
    Returns:
        Instancia de AppMonitor
    """
    try:
        monitor = AppMonitor(main_window)
        monitor.start()
        print("[MONITORIZACION] Sistema de monitorización iniciado correctamente")
        return monitor
    except Exception as e:
        print(f"[MONITORIZACION] Error al iniciar el sistema de monitorización: {e}")
        traceback.print_exc()
        return None

def detener_monitorizacion(monitor):
    """
    Función para detener la monitorización.
    
    Args:
        monitor: Instancia de AppMonitor a detener
    """
    if monitor:
        try:
            monitor.stop()
            print("[MONITORIZACION] Sistema de monitorización detenido correctamente")
        except Exception as e:
            print(f"[MONITORIZACION] Error al detener el sistema de monitorización: {e}")
            traceback.print_exc()
