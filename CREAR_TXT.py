import os
from datetime import datetime
import threading
from PyQt6 import Qt<PERSON>ore
from PyQt6.QtWidgets import QTreeWidgetItem
from PyQt6.QtGui import QIcon
from PyQt6.QtCore import Qt, QTimer, QObject, pyqtSignal, QRunnable, QThreadPool
from queue import Queue
import asyncio
from CREAR import registro
from io import StringIO

def play_drag_sound(files_added=0, sound_type=None):
    """
    Reproduce un sonido según el tipo especificado.
    
    Args:
        files_added (int): Número de archivos añadidos (para compatibilidad)
        sound_type (str): Tipo de sonido a reproducir ('txt', 'drag', etc.)
    """
    try:
        from SONIDOS import sound_manager
        if sound_type == "txt":
            sound_manager.play_sound('txt')
        else:
            sound_manager.play_sound('drag')
    except Exception as e:
        print(f"Error al reproducir sonido: {e}")

def format_size(size):
    """
    Formatea un tamaño en bytes a una cadena legible
    """
    try:
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.2f} {unit}"
            size /= 1024.0
        return f"{size:.2f} PB"
    except:
        return "0 B"

class TxtCreatorSignals(QObject):
    finished = pyqtSignal(str, int)  # nombre_archivo, tamaño
    error = pyqtSignal(str)

class TxtCreatorTask(QRunnable):
    def __init__(self, file_path, original_name):
        super().__init__()
        self.file_path = file_path
        self.original_name = original_name
        self.signals = TxtCreatorSignals()
        self.setAutoDelete(True)

    def run(self):
        try:
            # Crear buffer en RAM con tamaño predefinido
            buffer_size = 64 * 1024  # 64KB buffer
            ram_file = StringIO()
            ram_file.seek(0)
            
            # Preparar contenido en RAM
            contenido = [
                f"Archivo creado para: {self.original_name}",
                f"Fecha de creación: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ]
            
            try:
                # Obtener stats de una sola vez y guardar en RAM
                stat = os.stat(os.path.dirname(self.file_path) + '/' + self.original_name)
                contenido.extend([
                    f"Tamaño original: {format_size(stat.st_size)}",
                    f"Última modificación: {datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')}"
                ])
            except:
                pass

            # Escribir a RAM de una sola vez
            contenido_final = '\n'.join(contenido) + '\n'
            ram_file.write(contenido_final)
            
            # Escribir directamente al disco desde RAM usando un buffer optimizado
            with open(self.file_path, 'w', buffering=buffer_size, encoding='utf-8') as f:
                f.write(ram_file.getvalue())

            size = len(contenido_final.encode('utf-8'))
            self.signals.finished.emit(os.path.basename(self.file_path), size)

        except Exception as e:
            self.signals.error.emit(str(e))
        finally:
            ram_file.close()

class TxtFileCreator(QObject):
    # Definir la señal
    txt_created = pyqtSignal(str)
    
    def __init__(self, main_window):
        super().__init__()  # Inicializar QObject
        self.main_window = main_window
        self.thread_pool = QThreadPool()
        # Optimizar el pool de hilos
        self.thread_pool.setMaxThreadCount(4)  # Aumentar threads para mejor rendimiento
        self.thread_pool.setExpiryTimeout(500)  # Reducir timeout para liberar recursos más rápido
        self.pending_files = set()
        self.sound_played = set()
        # Buffer para actualizaciones de UI
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._process_pending_updates)
        self.update_timer.start(100)  # Actualizar UI cada 100ms
        self.pending_updates = Queue()
        
    def create_txt_file_buffered(self, file_name):
        try:
            if file_name.lower().endswith('.txt'):
                return

            # Crear directamente en la ubicación final
            final_path = os.path.join(self.main_window.current_drive, f"{file_name}.txt")

            if os.path.exists(final_path):
                return

            # Crear tarea y conectar señales
            task = TxtCreatorTask(final_path, file_name)
            task.signals.finished.connect(self.on_txt_created)
            task.signals.error.connect(self.on_txt_error)
            
            # Iniciar tarea con prioridad alta (1)
            task.setAutoDelete(True)
            self.thread_pool.start(task, 1)  # 1 = Alta prioridad

        except Exception as e:
            print(f"Error: {e}")

    def on_txt_created(self, file_name, size):
        """Callback cuando se completa la creación del TXT"""
        try:
            # Eliminar de la lista de pendientes
            if file_name in self.pending_files:
                self.pending_files.remove(file_name)
            
            # Emitir señal solo si el archivo existe
            txt_path = os.path.join(self.main_window.current_drive, file_name)
            
            if os.path.exists(txt_path):
                print(f"Archivo TXT creado exitosamente: {file_name}")
                # Emitir señal para actualizar la UI
                self.txt_created.emit(file_name)
            else:
                print(f"Error: El archivo TXT no existe después de la creación: {txt_path}")
        except Exception as e:
            print(f"Error en on_txt_created: {e}")

    def _process_pending_updates(self):
        """Procesa las actualizaciones pendientes de UI en lotes"""
        try:
            updates = []
            # Procesar hasta 10 actualizaciones por lote
            for _ in range(10):
                if self.pending_updates.empty():
                    break
                updates.append(self.pending_updates.get_nowait())

            if not updates:
                return

            # Actualizar UI en lote
            for file_name, size in updates:
                new_item = QTreeWidgetItem([file_name, self.main_window.format_size(size)])
                new_item.setIcon(0, registro(size=24))
                
                font = new_item.font(0)
                font.setBold(True)
                new_item.setFont(0, font)
                new_item.setFont(1, font)
                new_item.setFlags(new_item.flags() | Qt.ItemFlag.ItemIsEditable)
                
                self.main_window.file_tree_widget.addTopLevelItem(new_item)
                self.main_window.file_tree_widget.scrollToItem(new_item)

        except Exception as e:
            print(f"Error procesando actualizaciones: {e}")

    def on_txt_error(self, error):
        """Callback de error"""
        print(f"Error creando TXT: {error}")

    def update_ui_with_new_file(self, file_name):
        """Actualiza la UI con el nuevo archivo TXT creado"""
        try:
            # Verificar si el archivo ya está en la vista
            for i in range(self.main_window.file_tree_widget.topLevelItemCount()):
                item = self.main_window.file_tree_widget.topLevelItem(i)
                if item.text(0) == file_name:
                    print(f"El archivo {file_name} ya está en la vista, no se agregará de nuevo")
                    return
            
            # Crear nuevo item
            new_item = QTreeWidgetItem([file_name])
            
            # Configurar icono
            from CREAR import registro
            new_item.setIcon(0, registro(size=24))
            
            # Obtener y establecer el tamaño del archivo
            file_path = os.path.join(self.main_window.current_drive, file_name)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                new_item.setText(1, self.main_window.format_size(size))
                # Establecer alineación a la derecha para la columna de tamaño
                new_item.setTextAlignment(1, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            
            # Configurar fuente en negrita
            font = new_item.font(0)
            font.setBold(True)
            new_item.setFont(0, font)
            new_item.setFont(1, font)
            
            # Hacer editable
            new_item.setFlags(new_item.flags() | Qt.ItemFlag.ItemIsEditable)
            
            # Añadir al árbol
            self.main_window.file_tree_widget.addTopLevelItem(new_item)
            
            # Seleccionar y hacer visible el nuevo item
            self.main_window.file_tree_widget.setCurrentItem(new_item)
            self.main_window.file_tree_widget.scrollToItem(new_item)
            
            # Imprimir información de depuración
            print(f"Archivo TXT añadido a la vista: {file_name}")
            print(f"Número total de items en la vista: {self.main_window.file_tree_widget.topLevelItemCount()}")
            
        except Exception as e:
            print(f"Error añadiendo nuevo TXT a la vista: {e}")
