# MAPEO_PUERTO.py - Módulo para gestionar el mapeo de puertos USB físicos en ZETACOPY
import os
import subprocess
import json
import traceback
import hashlib
import threading
import random
import time
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtWidgets import Q<PERSON>essageBox, QDialog
from CREAR import boton_mapear_puertos
from RENOMBRAR import CustomRenameDialog
from APARIENCIA import apply_acrylic_and_rounded

# Cache compartida para información de puertos USB
_USB_PORT_CACHE = {}
_USB_PORT_CACHE_LOCK = threading.Semaphore(1)

def create_hidden_startupinfo():
    """Crea un objeto STARTUPINFO para ocultar la ventana de consola."""
    startupinfo = subprocess.STARTUPINFO()
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    startupinfo.wShowWindow = subprocess.SW_HIDE
    return startupinfo

def map_port(main_window):
    """Maneja el mapeo de puertos físicos del PC"""
    if hasattr(main_window, 'rename_dialog') and main_window.rename_dialog.isVisible():
        return
    
    # Obtener el drive_letter directamente sin cambiar el cursor al inicio
    try:
        selected_items = main_window.list_widget.selectedItems()
        if not selected_items:
            return
            
        item = selected_items[0]
        widget = main_window.list_widget.itemWidget(item)
        if not widget or not hasattr(widget, 'volume_label'):
            return
            
        current_text = widget.volume_label.text()
        parts = current_text.split(' (')
        if len(parts) < 2:
            QMessageBox.warning(main_window, "Error", "No se pudo determinar la letra de unidad.")
            return
            
        drive_letter_part = parts[1].split(')')[0]
        drive_letter = drive_letter_part.split('|')[0].strip()
        
        if drive_letter.upper() == "C:":
            QMessageBox.warning(main_window, "Error", "No se puede mapear el disco del sistema.")
            return
            
        drive_type = main_window.worker.get_drive_type(drive_letter + "\\")
        if drive_type not in ["Disco duro externo", "Unidad extraíble (USB)"]:
            QMessageBox.warning(main_window, "Error", f"No se puede mapear una unidad de tipo: {drive_type}")
            return
        
        # Ahora cambiamos el cursor, pero solo después de validar todo
        cursor_original = main_window.cursor()
        
        # Vamos a intentar el método más rápido primero
        port_id = None
        current_name = ""
        
        # Intentar obtener de MONITOREO usando el worker (que debería tener la caché)
        if hasattr(main_window, 'worker') and hasattr(main_window.worker, 'get_usb_port_info'):
            try:
                port_id, current_name = main_window.worker.get_usb_port_info(drive_letter)
                if port_id:
                    print(f"DEBUG: Puerto rápido encontrado en caché: {drive_letter} -> {port_id} ({current_name})")
                    show_port_rename_dialog(main_window, port_id, current_name)
                    return
            except Exception as e:
                print(f"Error usando método rápido para obtener puerto: {e}")
        
        # Si no funcionó, obtener con fallback rápido
        try:
            # Usar directamente el método fallback que es el más rápido
            port_id = get_port_id_fallback(drive_letter)
            if port_id:
                current_name = get_port_name(port_id)
                if not current_name:
                    current_name = "-"
                    
                print(f"DEBUG: Puerto obtenido con fallback rápido: {drive_letter} -> {port_id} ({current_name})")
                show_port_rename_dialog(main_window, port_id, current_name)
                return
        except Exception as e:
            print(f"Error usando fallback rápido para obtener puerto: {e}")
        
        # Si llegamos aquí, hay que mostrar el cursor de espera porque vamos a usar el worker
        main_window.setCursor(Qt.CursorShape.WaitCursor)
            
        # Si llegamos aquí, tenemos que usar el worker
        print(f"DEBUG: Iniciando worker para obtener puerto de {drive_letter}...")
        worker = PortMappingWorker(drive_letter, main_window)
        worker.finished.connect(lambda port_id, current_name: on_port_id_ready(main_window, port_id, current_name, cursor_original))
        worker.start()
        
    except Exception as e:
        print(f"Error en map_port: {e}")
        main_window.setCursor(cursor_original)

def on_port_id_ready(main_window, port_id, current_name, original_cursor):
    """Función callback cuando se completa la identificación del puerto."""
    main_window.setCursor(original_cursor)
    if not port_id:
        QMessageBox.warning(main_window, "Error", "No se pudo identificar el puerto físico para este dispositivo.")
        return
    show_port_rename_dialog(main_window, port_id, current_name)

def get_stable_physical_port_id(drive_letter):
    """
    Obtiene un identificador único y estable para el puerto USB físico
    basado en la jerarquía de dispositivos PnP de Windows.
    Este es el método principal y más fiable.
    """
    drive_letter_only = drive_letter.replace(":", "")
    powershell_cmd = f"""
    $OutputEncoding = [System.Text.Encoding]::UTF8;
    try {{
        $partition = Get-Partition -DriveLetter {drive_letter_only}
        $disk = $partition | Get-Disk
        $pnpDevice = $disk | Get-PnpDevice

        $parent = $pnpDevice
        $usbPortDevice = $null
        while ($parent) {{
            if (($parent.Class -eq 'USB') -or ($parent.CompatibleIds -like 'USB\\*')) {{
                $usbPortDevice = $parent
                break
            }}
            $parent = $parent | Get-PnpDeviceProperty -KeyName 'DEVPKEY_Device_Parent' | ForEach-Object {{ Get-PnpDevice -InstanceId $_.Data -ErrorAction SilentlyContinue }}
        }}

        if ($usbPortDevice) {{
            $parentHub = $usbPortDevice | Get-PnpDeviceProperty -KeyName 'DEVPKEY_Device_Parent' | ForEach-Object {{ Get-PnpDevice -InstanceId $_.Data }}
            
            if ($parentHub) {{
                $address = ($usbPortDevice | Get-PnpDeviceProperty -KeyName DEVPKEY_Device_Address).Data
                $hubId = $parentHub.InstanceId.Split('\\')[-1]
                $stablePortId = "HUB_{{$hubId}}_PORT_{{$address}}"
                Write-Output $stablePortId
            }}
        }}
    }} catch {{
        Write-Error "Error en PowerShell al procesar la unidad {drive_letter_only}: $_"
    }}
    """
    try:
        # Crear startupinfo para ocultar la consola
        startupinfo = create_hidden_startupinfo()
        
        process = subprocess.Popen(
            ["powershell", "-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", powershell_cmd],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',
            startupinfo=startupinfo,  # Añadido para ocultar consola
            creationflags=subprocess.CREATE_NO_WINDOW  # Bandera adicional para ocultar ventana
        )
        stdout, stderr = process.communicate(timeout=15)
        
        if stderr:
             print(f"DEBUG: Error en script PowerShell primario para {drive_letter}: {stderr.strip()}")
             return None

        if stdout.strip():
            return stdout.strip()
        else:
            return None
            
    except Exception as e:
        print(f"Error ejecutando el proceso de PowerShell: {e}")
        return None

def get_port_id_fallback(drive_letter):
    """
    Método de respaldo para obtener un ID de puerto si el método principal falla.
    Usa información de ubicación y número de serie.
    """
    try:
        drive = drive_letter.replace(":", "")
        command = f'powershell -NoProfile -ExecutionPolicy Bypass -Command "Get-Disk | Where-Object {{ $_.Number -eq (Get-Partition | Where-Object {{ $_.DriveLetter -eq \'{drive}\' }}).DiskNumber }} | Select-Object Location, SerialNumber, Number | ConvertTo-Json"'
        
        # Crear startupinfo para ocultar la consola
        startupinfo = create_hidden_startupinfo()
        
        process = subprocess.Popen(
            command, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, 
            shell=True, 
            startupinfo=startupinfo,  # Para ocultar consola
            creationflags=subprocess.CREATE_NO_WINDOW  # Bandera adicional
        )
        stdout, _ = process.communicate()
        
        disk_info = json.loads(stdout.decode('latin-1', errors='ignore'))
        location = disk_info.get('Location', '')
        serial = disk_info.get('SerialNumber', '')
        disk_number = disk_info.get('Number', '')

        # La ubicación física es un buen candidato para un ID estable.
        if location:
            location_hash = hashlib.md5(location.encode()).hexdigest()[:8]
            return f"LOC_{disk_number}_{location_hash}"
            
        # Si no hay ubicación, el serial del dispositivo es el siguiente mejor.
        if serial:
            serial_hash = hashlib.md5(serial.encode()).hexdigest()[:8]
            return f"SERIAL_{disk_number}_{serial_hash}"
            
        # Como último recurso, un ID basado en el tiempo.
        timestamp = int(time.time())
        return f"GEN_{drive}_{timestamp % 10000}"

    except Exception as e:
        print(f"ERROR en fallback de identificación de puerto: {e}")
        timestamp = int(time.time())
        random_suffix = ''.join([str(random.randint(0, 9)) for _ in range(4)])
        return f"FAIL_{drive_letter.replace(':', '')}_{random_suffix}_{timestamp % 10000}"

def get_usb_port_info(drive_letter):
    """
    Obtiene el ID del puerto y su nombre personalizado.
    Usa directamente el método fallback que es más rápido y confiable.
    """
    # Usar directamente el método fallback que es más rápido y confiable
    port_id = get_port_id_fallback(drive_letter)
    
    # Obtener el nombre del puerto
    port_name = get_port_name(port_id)
    
    if port_id:
        # Actualizar caché global
        _USB_PORT_CACHE[drive_letter] = (port_id, port_name)
        
    return port_id, port_name

def show_port_rename_dialog(main_window, port_id, current_name):
    """Muestra el diálogo para renombrar/mapear el puerto físico."""
    print(f"DEBUG: Mostrando diálogo para puerto: {port_id}, nombre actual: '{current_name}'")
    
    # Ya no necesitamos el cursor de espera, independientemente de cómo hayamos llegado aquí
    main_window.setCursor(Qt.CursorShape.ArrowCursor)
    
    # Intentar crear y mostrar el diálogo de forma más rápida
    try:
        title = f"Mapear Puerto Físico ({port_id})"
        if current_name:
            title = f"Editar Nombre de Puerto ({port_id})"
            
        main_window.rename_dialog = CustomRenameDialog(
            main_window,
            current_name, 
            boton_mapear_puertos(size=24),
            is_volume=False,
            is_alias=False
        )
        main_window.rename_dialog.setWindowTitle(title)
        
        # Conectar la aceptación del diálogo a un lambda que actualiza el mapping
        if main_window.rename_dialog.exec() == QDialog.DialogCode.Accepted:
            new_name = main_window.rename_dialog.get_new_name().strip()
            if add_port_mapping(port_id, new_name):
                # Refrescar la vista para mostrar el nuevo nombre
                if hasattr(main_window, 'actualizar_vista_discos'):
                    main_window.actualizar_vista_discos()
                print(f"DEBUG: Mapeo para puerto {port_id} actualizado a '{new_name}'. Vista actualizada.")
    except Exception as e:
        print(f"Error mostrando diálogo de puerto: {e}")
        traceback.print_exc()
        # Si falla, intentar un enfoque más simple
        from PyQt6.QtWidgets import QInputDialog
        new_name, ok = QInputDialog.getText(
            main_window, 
            "Nombre del puerto", 
            f"Introduce un nombre para el puerto {port_id}:",
            text=current_name
        )
        if ok and new_name:
            if add_port_mapping(port_id, new_name):
                # Refrescar la vista
                if hasattr(main_window, 'actualizar_vista_discos'):
                    main_window.actualizar_vista_discos()

def get_port_name(port_id):
    """Obtiene el nombre guardado de un puerto desde USBPorts.txt."""
    if not port_id or not os.path.exists('USBPorts.txt'):
        return ""
    try:
        with open('USBPorts.txt', 'r', encoding='utf-8') as f:
            for line in f:
                if ':' in line:
                    key, value = line.strip().split(':', 1)
                    if key.strip() == port_id:
                        return value.strip()
    except Exception as e:
        print(f"Error leyendo nombre del puerto desde USBPorts.txt: {e}")
    return ""

def add_port_mapping(port_id, port_name):
    """
    Actualiza el archivo USBPorts.txt y la caché global.
    Si el nombre está vacío, elimina el mapeo. Si no, lo crea o actualiza.
    """
    if not port_id:
        return False
        
    mappings = {}
    if os.path.exists('USBPorts.txt'):
        try:
            with open('USBPorts.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    if ':' in line:
                        key, value = line.strip().split(':', 1)
                        mappings[key.strip()] = value.strip()
        except Exception as e:
            print(f"DEBUG: No se pudo leer USBPorts.txt: {e}")

    if port_name:
        mappings[port_id] = port_name
    elif port_id in mappings:
        del mappings[port_id]
        
    try:
        with open('USBPorts.txt', 'w', encoding='utf-8') as f:
            for key, value in mappings.items():
                f.write(f"{key}:{value}\n")
                
        # Actualizar también la caché global en MONITOREO si es posible
        try:
            # Actualizar primero nuestra caché local
            for drive_letter, (cached_port_id, _) in list(_USB_PORT_CACHE.items()):
                if cached_port_id == port_id:
                    _USB_PORT_CACHE[drive_letter] = (port_id, port_name)
                    print(f"Caché local actualizada para {drive_letter}: {port_id}={port_name}")
            
            # Luego la caché en MONITOREO (si está disponible)
            import MONITOREO
            if hasattr(MONITOREO, '_USB_PORT_CACHE') and hasattr(MONITOREO, '_USB_PORT_CACHE_LOCK'):
                with MONITOREO._USB_PORT_CACHE_LOCK:
                    for drive_letter, (cached_port_id, _) in list(MONITOREO._USB_PORT_CACHE.items()):
                        if cached_port_id == port_id:
                            MONITOREO._USB_PORT_CACHE[drive_letter] = (port_id, port_name)
                            print(f"Caché global actualizada para {drive_letter}: {port_id}={port_name}")
        except Exception as e:
            print(f"Error actualizando cachés: {e}")
            
        return True
    except Exception as e:
        print(f"Error actualizando USBPorts.txt: {e}")
        return False

class PortMappingWorker(QThread):
    """Hilo para identificar el puerto físico de un dispositivo sin bloquear la UI."""
    finished = pyqtSignal(str, str)  # port_id, current_name
    
    def __init__(self, drive_letter, parent=None):
        super().__init__(parent)
        self.drive_letter = drive_letter
        self.parent = parent
    
    def run(self):
        """Obtiene el ID del puerto y su nombre personalizado con máxima velocidad."""
        try:
            # Usar directamente el método fallback que es más rápido y confiable
            port_id = get_port_id_fallback(self.drive_letter)
            if port_id:
                port_name = get_port_name(port_id)
                if not port_name:
                    port_name = "-"
                print(f"PortMappingWorker: Puerto obtenido: {self.drive_letter} -> {port_id} ({port_name})")
                self.finished.emit(port_id, port_name)
                return
            
            # Si realmente no pudimos obtener un ID, generamos uno único para no bloquear
            import time
            import random
            timestamp = int(time.time())
            random_suffix = ''.join([str(random.randint(0, 9)) for _ in range(4)])
            port_id = f"FALLBACK_{self.drive_letter.replace(':', '')}_{random_suffix}_{timestamp % 10000}"
            self.finished.emit(port_id, "-")
            
        except Exception as e:
            print(f"PortMappingWorker: Error en hilo de mapeo: {e}")
            traceback.print_exc()
            self.finished.emit("", "")

def init_usb_port_mappings():
    """Pre-carga en la caché los mapeos para todos los dispositivos USB conectados al iniciar."""
    print("Iniciando mapeo de puertos USB en segundo plano...")
    try:
        import pythoncom
        pythoncom.CoInitialize()
        
        import wmi
        c = wmi.WMI()
        
        # Mapear todos los discos, no solo los removibles
        # Primero los removibles (DriveType=2)
        for disk in c.Win32_LogicalDisk(DriveType=2):
            drive_letter = disk.DeviceID
            print(f"Detectada unidad removible: {drive_letter}. Mapeando puerto...")
            try:
                # La función get_usb_port_info se encarga de todo y guarda en caché
                port_id, port_name = get_usb_port_info(drive_letter)
                if port_id:
                    print(f"Puerto USB para {drive_letter} mapeado: {port_id} ({port_name})")
            except Exception as e:
                print(f"Error mapeando puerto para {drive_letter} durante la inicialización: {e}")
        
        # Luego los discos fijos (DriveType=3)
        for disk in c.Win32_LogicalDisk(DriveType=3):
            drive_letter = disk.DeviceID
            if drive_letter != "C:":  # Evitar el disco del sistema para mejorar rendimiento
                print(f"Detectado disco fijo: {drive_letter}. Mapeando puerto...")
                try:
                    # La función get_usb_port_info se encarga de todo y guarda en caché
                    port_id, port_name = get_usb_port_info(drive_letter)
                    if port_id:
                        print(f"Puerto USB para {drive_letter} mapeado: {port_id} ({port_name})")
                except Exception as e:
                    print(f"Error mapeando puerto para {drive_letter} durante la inicialización: {e}")
                
    except Exception as e:
        print(f"Error global durante la inicialización del mapeo de puertos: {e}")
    finally:
        pythoncom.CoUninitialize()
        
    print(f"Mapeo inicial de puertos USB completado. {len(_USB_PORT_CACHE)} puertos en caché.")

# Iniciar el mapeo de puertos en segundo plano al importar el módulo
threading.Thread(target=init_usb_port_mappings, daemon=True).start()