import subprocess
import sys
import time
# ESTO ES EL COMANDO PARA INSTALARLAS --------->  python instalar_dependencias_secuencial.py
# Lista de dependencias necesarias (completa con todas las dependencias)
dependencies = [
    "PyQt6",              # Framework principal de GUI
    "cryptography",       # Para encriptación y funciones de seguridad
    "pywin32",           # Para funciones específicas de Windows
    "psutil",            # Para monitoreo de sistema
    "Cython",            # Para compilación
    "pygame",            # Para efectos de sonido
    "pillow",            # Para procesamiento de imágenes
    "qrcode",            # Para generación de códigos QR
    "wmi",              # Para acceso a información del sistema Windows
    "ripgrep",           # Para búsqueda de archivos
    "segno",             # Para códigos QR adicionales
    "typing_extensions", # Para soporte de tipado
    "colorama",          # Para colores en consola
    "pycparser",         # Para análisis de código C
    "comtypes",          # Para interfaces COM de Windows
]

print("Iniciando instalación secuencial de dependencias...")
print("--------------------------------------------------")

for dep in dependencies:
    print(f"\nInstalando {dep}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
        print(f"✅ {dep} instalado correctamente")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando {dep}: {e}")
    except Exception as e:
        print(f"❌ Error inesperado instalando {dep}: {e}")
    
    # Pausa entre instalaciones
    time.sleep(1)

print("\nProceso de instalación completado.")
input("Presione Enter para salir...")