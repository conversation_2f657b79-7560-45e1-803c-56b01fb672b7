from PyQt6.QtWidgets import Q<PERSON>ialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QFrame, QGraphicsDropShadowEffect, QLineEdit
from PyQt6.QtCore import Qt, QTimer, QSize, QP<PERSON>tyAnimation, pyqtProperty, QPoint
from PyQt6.QtGui import QIcon, QPixmap, QColor, QPainter, QPainterPath
import os
from io import BytesIO
import AJUSTES
import hashlib
from APARIENCIA import apply_acrylic_and_rounded
from APARIENCIA import (
    ACCENT_POLICY, 
    WINDOW<PERSON>MPOSITIONATTRIBDATA, 
    ACCENT_ENABLE_ACRYLICBLURBEHIND, 
    WCA_ACCENT_POLICY,
    DWMWA_WINDOW_CORNER_PREFERENCE,
    DWMWCP_ROUND,
    ACCENT_ENABLE_FLUENT,
    is_windows_11_or_greater
)
import ctypes

class AnimatedButton(QPushButton):
    def __init__(self, icon_path=None, parent=None):
        super().__init__(parent)
        if icon_path:
            self.setIcon(QIcon(icon_path))
            self.setIconSize(QSize(25, 25))  # Initial icon size
        self.setStyleSheet("border: none; background: transparent;")
        self.setMinimumSize(35, 35)
        self.setMaximumSize(35, 35)
        AnimatedButton.add_shadow_effect(self, Qt.GlobalColor.white)
        self.setMouseTracking(True)
        self._icon_size = 25

    @staticmethod
    def add_shadow_effect(widget, color):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

    @pyqtProperty(int)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.setIconSize(QSize(size, size))

    def enterEvent(self, event):
        self.animate_icon_size(30)  # Enlarged icon size

    def leaveEvent(self, event):
        self.animate_icon_size(25)  # Original icon size

    def animate_icon_size(self, target_size):
        self.animation = QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(50)  # Duration of the animation in milliseconds
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

class CloseConfirmationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.Popup | 
            Qt.WindowType.NoDropShadowWindowHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        
        # Layout principal
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Mensaje
        message_label = QLabel("¿Desea cerrar la aplicación?")
        message_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                background: transparent;
            }
        """)
        layout.addWidget(message_label)
        
        # Botones
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.accept_button = QPushButton("Aceptar")
        self.cancel_button = QPushButton("Cancelar")
        
        for button in [self.accept_button, self.cancel_button]:
            button.setStyleSheet("""
                QPushButton {
                    background-color: rgba(43, 43, 43, 0.5);
                    color: white;
                    border: none;
                    border-radius: 15px;
                    padding: 8px 16px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: rgba(59, 59, 59, 0.6);
                }
                QPushButton:pressed {
                    background-color: rgba(27, 27, 27, 0.7);
                }
            """)
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(10)
            shadow.setXOffset(0)
            shadow.setYOffset(0)
            shadow.setColor(QColor(0, 0, 0, 60))
            button.setGraphicsEffect(shadow)
            
        button_layout.addWidget(self.accept_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
        
        # Conectar botones
        self.accept_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)

    def showEvent(self, event):
        super().showEvent(event)
        # Aplicar el efecto según la versión de Windows
        hwnd = int(self.winId())
        accent = ACCENT_POLICY()
        if is_windows_11_or_greater():
            accent.AccentState = ACCENT_ENABLE_ACRYLICBLURBEHIND
            accent.GradientColor = 0x01000000
        else:
            accent.AccentState = ACCENT_ENABLE_FLUENT
            accent.GradientColor = 0xCC000000  # 80% de opacidad para el efecto Fluent
        accent.AccentFlags = 0x20 | 0x40 | 0x80 | 0x100
        
        data = WINDOWCOMPOSITIONATTRIBDATA()
        data.Attribute = WCA_ACCENT_POLICY
        data.SizeOfData = ctypes.sizeof(accent)
        data.Data = ctypes.cast(ctypes.pointer(accent), ctypes.POINTER(ctypes.c_int))
        ctypes.windll.user32.SetWindowCompositionAttribute(hwnd, ctypes.byref(data))
        
        # Aplicar bordes redondeados
        ctypes.windll.dwmapi.DwmSetWindowAttribute(
            hwnd,
            DWMWA_WINDOW_CORNER_PREFERENCE,
            ctypes.byref(ctypes.c_int(DWMWCP_ROUND)),
            ctypes.sizeof(ctypes.c_int)
        )
        
        # Animación de aparición
        self.final_pos = self.pos()
        start_pos = self.final_pos + QPoint(0, 50)
        self.move(start_pos)
        
        anim = QPropertyAnimation(self, b"pos")
        anim.setDuration(300)
        anim.setStartValue(start_pos)
        anim.setEndValue(self.final_pos)
        anim.start()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect()
        path = QPainterPath()
        path.addRoundedRect(
            float(rect.x()),
            float(rect.y()),
            float(rect.width()),
            float(rect.height()),
            8.0,
            8.0
        )
        # Reducimos la opacidad del fondo negro para coincidir con los otros diálogos
        painter.fillPath(path, QColor(0, 0, 0, 15))

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Escape:
            self.reject()
        elif event.key() in (Qt.Key.Key_Enter, Qt.Key.Key_Return):
            self.accept()
        else:
            super().keyPressEvent(event)

class ExtensionDiffDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Extensión diferente")
        
        layout = QVBoxLayout(self)
        
        # Mostrar un mensaje genérico
        self.message_label = QLabel(
            "La extensión del siguiente archivo es diferente.\n\n"
            "¿Desea continuar con la copia?"
        )
        self.message_label.setStyleSheet("color: white; font-size: 14px;")
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.message_label)
        
        # Botones de aceptar y cancelar
        self.accept_button = QPushButton("Continuar")
        self.accept_button.clicked.connect(self.accept)
        
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)
        
        layout.addWidget(self.accept_button)
        layout.addWidget(self.cancel_button)
