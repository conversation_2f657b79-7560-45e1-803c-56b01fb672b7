from PyQt6.QtWidgets import QProgressBar
from PyQt6.QtCore import Qt, QRectF
from PyQt6.QtGui import <PERSON><PERSON><PERSON>ter, QColor, QPainterPath, QLinearGradient

class DragProgressBar(QProgressBar):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTextVisible(False)
        self.setFixedHeight(22)
        self.setStyleSheet("""
            QProgressBar {
                background-color: transparent;
                border: none;
                border-radius: 11px;
            }
        """)
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Dibujar el fondo con efecto de cristal
        bg_path = QPainterPath()
        bg_path.addRoundedRect(QRectF(0, 0, self.width(), self.height()), 11, 11)
        painter.fillPath(bg_path, QColor(0, 0, 0, 30))
        
        # Calcular el progreso
        progress = self.value() / (self.maximum() - self.minimum())
        progress_width = progress * self.width()
        
        if progress > 0:
            # Crear gradiente para la barra de progreso
            gradient = QLinearGradient(0, 0, progress_width, 0)
            gradient.setColorAt(0, QColor("#0078d7"))
            gradient.setColorAt(1, QColor("#00a2ff"))
            
            # Dibujar la barra de progreso con gradiente
            progress_path = QPainterPath()
            progress_path.addRoundedRect(
                QRectF(0, 0, progress_width, self.height()),
                11, 11
            )
            painter.fillPath(progress_path, gradient)
            
            # Dibujar el texto del porcentaje con sombra
            text = f"{int(progress * 100)}%"
            
            # Dibujar sombra del texto
            painter.setPen(QColor(0, 0, 0, 100))
            painter.drawText(
                self.rect().adjusted(1, 1, 1, 1),
                Qt.AlignmentFlag.AlignCenter,
                text
            )
            
            # Dibujar texto principal
            painter.setPen(QColor(255, 255, 255))
            painter.drawText(
                self.rect(),
                Qt.AlignmentFlag.AlignCenter,
                text
            )
            
            # Efecto de brillo más pronunciado para efecto 3D
            shine_gradient = QLinearGradient(0, 0, 0, self.height())
            shine_gradient.setColorAt(0, QColor(255, 255, 255, 160))  # Brillo superior más intenso
            shine_gradient.setColorAt(0.2, QColor(255, 255, 255, 100)) # Transición suave
            shine_gradient.setColorAt(0.4, QColor(255, 255, 255, 40))  # Desvanecimiento gradual
            shine_gradient.setColorAt(0.6, QColor(255, 255, 255, 0))   # Desvanecimiento completo
            shine_gradient.setColorAt(0.8, QColor(0, 0, 0, 15))        # Sombra sutil
            shine_gradient.setColorAt(1.0, QColor(0, 0, 0, 25))        # Sombra inferior
            
            # Dibujar el brillo con ajuste más preciso al ancho de la barra
            highlight = QPainterPath()
            highlight.addRoundedRect(
                QRectF(1, 1, progress_width - 2, self.height() - 2),
                10, 10  # Radio ajustado para mantener consistencia con la barra
            )
            painter.fillPath(highlight, shine_gradient)