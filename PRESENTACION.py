from PyQt6.QtWidgets import (QSplashScreen, QApplication, QGraphicsOpacityEffect, QWidget, QLabel, QGraphicsDropShadowEffect)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRectF, QPoint, QPointF, QSize
from PyQt6.QtGui import QPixmap, QFont, QPainter, QColor, QRadialGradient, QPainterPath, QPen, QLinearGradient, QIcon, QFontMetrics, QConicalGradient
import os, sys
import random
import math
from APARIENCIA import apply_acrylic_and_rounded, is_windows_11_or_greater

class IconShadowEffect(QWidget):
    def __init__(self, pixmap, parent=None):
        super().__init__(parent)
        self.pixmap = pixmap
        self.opacity = 0.5
        self.blur_radius = 15
        self.setFixedSize(pixmap.size() + QSize(30, 30))
        self._cached_shadow = None
        self._create_cached_shadow()
        
    def _create_cached_shadow(self):
        """Pre-renderiza la sombra para mejorar el rendimiento"""
        self._cached_shadow = QPixmap(self.size())
        self._cached_shadow.fill(Qt.GlobalColor.transparent)
        painter = QPainter(self._cached_shadow)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        x = (self.width() - self.pixmap.width()) // 2
        y = (self.height() - self.pixmap.height()) // 2
        
        for offset in range(self.blur_radius, 0, -1):
            opacity = int(120 * self.opacity * (offset / self.blur_radius))
            painter.save()
            painter.translate(x + offset/2, y + offset/2)
            painter.setOpacity(opacity/255.0)
            
            shadow_pixmap = QPixmap(self.pixmap.size())
            shadow_pixmap.fill(QColor(0, 0, 0, opacity))
            shadow_mask = self.pixmap.mask()
            shadow_pixmap.setMask(shadow_mask)
            
            painter.drawPixmap(0, 0, shadow_pixmap)
            painter.restore()
        
        painter.end()
        
    def paintEvent(self, event):
        if not self._cached_shadow:
            self._create_cached_shadow()
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Dibujar la sombra pre-renderizada
        painter.drawPixmap(0, 0, self._cached_shadow)
        
        # Dibujar el icono
        x = (self.width() - self.pixmap.width()) // 2
        y = (self.height() - self.pixmap.height()) // 2
        painter.drawPixmap(x, y, self.pixmap)

class ModernZWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(200, 200)
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        w = self.width()
        h = self.height()
        
        path = QPainterPath()
        
        # Z moderna invertida
        path.moveTo(w * 0.8, h * 0.2)
        path.lineTo(w * 0.2, h * 0.2)
        path.lineTo(w * 0.25, h * 0.25)
        path.lineTo(w * 0.7, h * 0.25)
        path.lineTo(w * 0.3, h * 0.75)
        path.lineTo(w * 0.75, h * 0.75)
        path.lineTo(w * 0.8, h * 0.8)
        path.lineTo(w * 0.2, h * 0.8)
        path.closeSubpath()
        
        thickness = int(w * 0.08)
        
        # Sombras en gris oscuro para la Z
        for i in range(8):
            shadow_path = QPainterPath(path)
            opacity = 70 - i * 7
            shadow_pen = QPen(QColor(40, 40, 40, opacity), thickness)
            shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            shadow_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
            
            painter.setPen(shadow_pen)
            painter.drawPath(shadow_path.translated(i + 2.5, i + 2.5))
        
        # Gradiente blanco para la Z
        gradient = QLinearGradient(w, 0, 0, h)
        gradient.setColorAt(0.0, QColor(255, 255, 255))  # Blanco puro
        gradient.setColorAt(1.0, QColor(240, 240, 240))  # Blanco suave
        
        painter.fillPath(path, gradient)
        
        # Borde blanco brillante
        main_pen = QPen(QColor(255, 255, 255))
        main_pen.setWidth(thickness)
        main_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        main_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        
        painter.setPen(main_pen)
        painter.drawPath(path)
        
        # Brillo superior
        highlight = QLinearGradient(w, 0, w * 0.5, h * 0.5)
        highlight.setColorAt(0.0, QColor(255, 255, 255, 90))
        highlight.setColorAt(1.0, QColor(255, 255, 255, 0))
        
        highlight_path = QPainterPath()
        highlight_path.moveTo(w * 0.8, h * 0.2)
        highlight_path.lineTo(w * 0.2, h * 0.2)
        highlight_path.lineTo(w * 0.5, h * 0.4)
        highlight_path.closeSubpath()
        
        painter.fillPath(highlight_path, highlight)

    def create_icon(self, size=64):
        icon_pixmap = QPixmap(size, size)
        icon_pixmap.fill(Qt.GlobalColor.transparent)
        painter = QPainter(icon_pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        w = size
        h = size
        center_x = w // 2
        center_y = h // 2
        outer_radius = int(w * 0.42)
        
        # Sombra del aro
        for i in range(2):
            shadow_pen = QPen()
            shadow_pen.setWidth(3)
            shadow_pen.setColor(QColor(0, 0, 0, 40 - i * 15))
            shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            shadow_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
            painter.setPen(shadow_pen)
            painter.drawEllipse(QPoint(center_x + i + 1, center_y + i + 1), outer_radius, outer_radius)
        
        # Aro principal con gradiente
        gradient_outer = QLinearGradient(0, center_y - outer_radius, 0, center_y + outer_radius)
        gradient_outer.setColorAt(0.0, QColor(255, 82, 82))
        gradient_outer.setColorAt(0.5, QColor(244, 67, 54))
        gradient_outer.setColorAt(1.0, QColor(211, 47, 47))
        
        pen_outer = QPen()
        pen_outer.setWidth(3)
        pen_outer.setBrush(gradient_outer)
        pen_outer.setCapStyle(Qt.PenCapStyle.RoundCap)
        pen_outer.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        painter.setPen(pen_outer)
        painter.drawEllipse(QPoint(center_x, center_y), outer_radius, outer_radius)
        
        # Z moderna en el centro
        path = QPainterPath()
        z_size = 0.35  # Reducido para mantener proporción con el aro más pequeño
        
        # Líneas de la Z
        path.moveTo(w * (0.5 - z_size/2), h * (0.5 - z_size/2))
        path.lineTo(w * (0.5 + z_size/2), h * (0.5 - z_size/2))
        path.lineTo(w * (0.5 - z_size/2), h * (0.5 + z_size/2))
        path.lineTo(w * (0.5 + z_size/2), h * (0.5 + z_size/2))
        
        thickness = int(w * 0.09)  # Ajustado el grosor de la Z
        
        # Sombra de la Z
        for i in range(2):
            shadow_path = QPainterPath(path)
            shadow_pen = QPen(QColor(0, 0, 0, 40 - i * 10), thickness)
            shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            shadow_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
            painter.setPen(shadow_pen)
            painter.drawPath(shadow_path.translated(i + 1, i + 1))
        
        # Z principal con gradiente
        gradient = QLinearGradient(0, 0, 0, h)
        gradient.setColorAt(0.0, QColor(231, 76, 60))  # Rojo coral moderno
        gradient.setColorAt(0.3, QColor(219, 50, 54))  # Rojo medio elegante
        gradient.setColorAt(0.7, QColor(192, 57, 43))  # Rojo profundo suave
        gradient.setColorAt(1.0, QColor(165, 42, 42))  # Rojo oscuro elegante
        
        main_pen = QPen()
        main_pen.setBrush(gradient)
        main_pen.setWidth(thickness)
        main_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        main_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        
        painter.setPen(main_pen)
        painter.drawPath(path)
        
        # Brillo de la Z
        highlight = QLinearGradient(0, 0, 0, h * 0.5)
        highlight.setColorAt(0.0, QColor(255, 255, 255, 30))
        highlight.setColorAt(1.0, QColor(255, 255, 255, 0))
        
        highlight_pen = QPen()
        highlight_pen.setBrush(highlight)
        highlight_pen.setWidth(int(thickness * 0.8))
        highlight_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        highlight_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        
        painter.setPen(highlight_pen)
        painter.drawPath(path.translated(-1, -1))
        
        painter.end()
        return QIcon(icon_pixmap)

    def draw_text(self, painter):
        text = "ZETACOPY"
        letter_height = 24
        spacing = 4
        stroke_width = 3
        
        font = QFont("Arial Black", letter_height)
        font.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, spacing)
        font.setBold(True)
        
        text_path = QPainterPath()
        text_path.addText(0, 0, font, text)
        text_rect = text_path.boundingRect()
        
        start_x = (self.width() - text_rect.width()) // 2
        center_y = 180
        
        text_path.translate(start_x, center_y)
        
        # Sombras en gris oscuro para mejor efecto 3D
        for i in range(8):
            shadow_path = QPainterPath(text_path)
            opacity = 70 - i * 7
            # Sombra gris oscura
            shadow_pen = QPen(QColor(40, 40, 40, opacity), stroke_width)
            shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            shadow_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
            
            painter.setPen(shadow_pen)
            painter.drawPath(shadow_path.translated(i + 2, i + 2))
        
        # Gradiente blanco puro a blanco suave
        gradient = QLinearGradient(start_x, center_y - letter_height, 
                                 start_x + text_rect.width(), center_y)
        gradient.setColorAt(0.0, QColor(255, 255, 255))  # Blanco puro
        gradient.setColorAt(1.0, QColor(240, 240, 240))  # Blanco suave
        
        painter.fillPath(text_path, gradient)
        
        # Brillo sutil
        highlight = QLinearGradient(start_x + text_rect.width(), center_y - letter_height,
                                  start_x + text_rect.width() * 0.5, center_y - letter_height * 0.5)
        highlight.setColorAt(0.0, QColor(255, 255, 255, 50))
        highlight.setColorAt(1.0, QColor(255, 255, 255, 0))
        
        highlight_path = QPainterPath()
        highlight_path.addPath(text_path)
        painter.fillPath(highlight_path, highlight)

class Presentacion(QSplashScreen):
    def __init__(self):
        # Tamaño base y configuración inicial
        self.base_size = 190
        self.shadow_margin = 20
        self.total_size = self.base_size + (self.shadow_margin * 2)
        
        # Crear el pixmap base con el tamaño total
        base_pixmap = QPixmap(self.total_size, self.total_size)
        base_pixmap.fill(Qt.GlobalColor.transparent)
        
        super().__init__(base_pixmap)
        
        # Configurar la ventana
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        
        # Habilitar transparencia
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        
        # Configurar tamaños
        self.circle_size = 170
        self.light_angle = 0
        
        # Centrar en la pantalla
        self.center_window()
        
        # Configurar el icono
        self.setup_icon()
        
        # Configurar el texto
        self.setup_text()
        
        # Configurar temporizadores
        self.setup_timers()

    def center_window(self):
        """Centra la ventana en la pantalla"""
        screen = QApplication.primaryScreen().geometry()
        self.move(
            (screen.width() - self.total_size) // 2,
            (screen.height() - self.total_size) // 2
        )

    def setup_icon(self):
        """Configura el icono y sus efectos"""
        icon_path = os.path.join(os.path.dirname(__file__), 'ICONOS', 'ZETACOPY.ico')
        if os.path.exists(icon_path):
            self.icon_pixmap = QPixmap(icon_path)
            self.icon_pixmap = self.icon_pixmap.scaled(70, 70, 
                Qt.AspectRatioMode.KeepAspectRatio, 
                Qt.TransformationMode.SmoothTransformation)
            
            self.icon_label = QLabel(self)
            self.icon_label.setPixmap(self.icon_pixmap)
            self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # Efecto de sombra
            shadow = QGraphicsDropShadowEffect(self)
            shadow.setBlurRadius(15)
            shadow.setXOffset(0)
            shadow.setYOffset(0)
            shadow.setColor(QColor(0, 0, 0, 220))
            self.icon_label.setGraphicsEffect(shadow)
            
            # Posicionar icono
            icon_x = (self.total_size - self.icon_pixmap.width()) // 2
            icon_y = self.shadow_margin + 35
            self.icon_label.move(icon_x, icon_y)
            self.icon_label.show()

    def setup_text(self):
        """Configura el texto y sus efectos"""
        self.text_label = QLabel("ZETACOPY", self)
        font = QFont("Arial Black", 16)
        font.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, 1)
        font.setBold(True)
        self.text_label.setFont(font)
        self.text_label.setStyleSheet("color: #E73C3C; background: transparent;")
        
        # Calcular dimensiones
        fm = QFontMetrics(font)
        text_width = fm.horizontalAdvance("ZETACOPY")
        text_height = fm.height()
        
        self.text_label.setFixedSize(text_width + 10, text_height + 5)
        self.text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Posicionar texto
        text_x = (self.total_size - self.text_label.width()) // 2
        text_y = self.shadow_margin + 120
        self.text_label.move(text_x, text_y)
        
        # Efecto de sombra
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(15)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 220))
        self.text_label.setGraphicsEffect(shadow)
        self.text_label.show()
        
        # Crear y posicionar el label de licencia después del texto principal
        self.license_label = QLabel(self)
        self.license_label.setFont(QFont("Arial Black", 13))
        self.license_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.license_label.setFixedWidth(45)  # Ancho más ajustado
        
        # Verificar licencia y actualizar label
        is_pro = self.check_license()
        if is_pro:
            self.license_label.setText("PRO")
            self.license_label.setStyleSheet("""
                QLabel {
                    color: #FF0000;
                    font-size: 13px;
                    font-weight: bold;
                    padding: 1px 4px;
                    background-color: rgba(128, 128, 128, 0.3);
                    border-radius: 9px;
                    margin: 0px;
                }
            """)
        else:
            self.license_label.setText("Free")
            self.license_label.setStyleSheet("""
                QLabel {
                    color: #007bff;
                    font-size: 13px;
                    font-weight: bold;
                    padding: 1px 4px;
                    background-color: rgba(128, 128, 128, 0.3);
                    border-radius: 9px;
                    margin: 0px;
                }
            """)
        
        # Posicionar el label centrado debajo del texto ZETACOPY
        license_x = (self.total_size - self.license_label.width()) // 2
        license_y = text_y + text_height + 10  # 10 píxeles de separación
        self.license_label.move(license_x, license_y)
        self.license_label.show()

    def setup_timers(self):
        """Configura los temporizadores para animación y efectos"""
        # Timer para animación
        self.animation_timer = QTimer(self)
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(30)
        
        # Timer para efectos
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_effects)
        self.refresh_timer.start(100)
        
        # Aplicar efectos iniciales
        QTimer.singleShot(0, self.apply_effects)

    def update_animation(self):
        """Actualiza el ángulo de la luz animada"""
        self.light_angle = (self.light_angle + 3) % 360
        self.update()  # Solicitar repintar la ventana

    def refresh_effects(self):
        """Refresca los efectos visuales para mantener la transparencia"""
        self.apply_effects()
        self.update()

    def apply_effects(self):
        """Aplica los efectos visuales a la ventana de presentación"""
        hwnd = self.winId().__int__()
        apply_acrylic_and_rounded(hwnd, mode='dark', is_tooltip=False)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Calcular la posición para centrar el círculo, considerando el margen de sombra
        offset = (self.width() - self.circle_size) // 2
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # Dibujar sombra exterior de la ventana
        for i in range(self.shadow_margin, 0, -1):
            shadow_opacity = 25 - i * 1  # Opacidad suave con desvanecimiento gradual
            shadow_pen = QPen(QColor(0, 0, 0, shadow_opacity), 1)
            painter.setPen(shadow_pen)
            shadow_size = self.circle_size + (self.shadow_margin * 2) - i
            shadow_offset = (self.width() - shadow_size) // 2
            painter.drawEllipse(shadow_offset, shadow_offset, shadow_size, shadow_size)
        
        # Dibujar sombra equilibrada alrededor del círculo interior
        # Primera capa: sombra exterior suave y gradual
        for i in range(8, 0, -1):
            shadow_opacity = 40 - i * 4  # Opacidad moderada con desvanecimiento gradual
            shadow_pen = QPen(QColor(0, 0, 0, shadow_opacity), 1)
            painter.setPen(shadow_pen)
            shadow_size = self.circle_size + i * 2
            shadow_offset = (self.width() - shadow_size) // 2
            painter.drawEllipse(shadow_offset, shadow_offset, shadow_size, shadow_size)
        
        # Segunda capa: sombra interior más definida pero no exagerada
        for i in range(3, 0, -1):
            shadow_opacity = 50 - i * 8  # Sombra moderada cerca del círculo
            shadow_pen = QPen(QColor(0, 0, 0, shadow_opacity), 1)
            painter.setPen(shadow_pen)
            shadow_size = self.circle_size + i
            shadow_offset = (self.width() - shadow_size) // 2
            painter.drawEllipse(shadow_offset, shadow_offset, shadow_size, shadow_size)
        
        # Dibujar el círculo principal con un borde sutil
        painter.setPen(QPen(QColor(255, 255, 255, 50), 1))
        painter.drawEllipse(offset, offset, self.circle_size, self.circle_size)
        
        # Efecto de luz que rodea todo el círculo con atenuación
        circle_radius = self.circle_size / 2
        
        painter.save()
        
        # Crear un camino para el círculo completo
        arc_path = QPainterPath()
        arc_rect = QRectF(offset, offset, self.circle_size, self.circle_size)
        
        # Dibujar un círculo completo (360 grados)
        arc_path.addEllipse(arc_rect)
        
        # Crear un degradado conical para el brillo que rodea todo el círculo
        # El ángulo de luz determina dónde está el punto más brillante
        conical_gradient = QConicalGradient(center_x, center_y, self.light_angle)
        
        # Crear un efecto de atenuación con el punto más brillante en la posición actual
        # y degradado suavemente alrededor del círculo
        conical_gradient.setColorAt((self.light_angle % 360) / 360.0, QColor(255, 255, 255, 200))
        conical_gradient.setColorAt(((self.light_angle + 30) % 360) / 360.0, QColor(255, 255, 255, 100))
        conical_gradient.setColorAt(((self.light_angle + 60) % 360) / 360.0, QColor(255, 255, 255, 50))
        conical_gradient.setColorAt(((self.light_angle + 90) % 360) / 360.0, QColor(255, 255, 255, 30))
        conical_gradient.setColorAt(((self.light_angle + 180) % 360) / 360.0, QColor(255, 255, 255, 20))
        conical_gradient.setColorAt(((self.light_angle + 270) % 360) / 360.0, QColor(255, 255, 255, 30))
        conical_gradient.setColorAt(((self.light_angle + 330) % 360) / 360.0, QColor(255, 255, 255, 100))
        
        # Dibujar el borde brillante con un grosor adecuado
        arc_pen = QPen()
        arc_pen.setWidth(2)  # Grosor delgado para un aspecto elegante
        arc_pen.setBrush(conical_gradient)
        
        painter.setPen(arc_pen)
        painter.drawPath(arc_path)
        
        painter.restore()

    def check_license(self):
        """Verifica si existe una licencia válida"""
        try:
            if getattr(sys, 'frozen', False):
                exe_path = os.path.dirname(sys.executable)
            else:
                exe_path = os.path.dirname(__file__)
                
            license_path = os.path.join(exe_path, "license.dat")
            
            if not os.path.exists(license_path):
                return False
                
            with open(license_path, "rb") as license_file:
                # Aquí la misma lógica de verificación que en ZETACOPY
                return True
                
        except Exception as e:
            print(f"Error verificando licencia: {e}")
            return False

    def finish(self, window):
        # Detener el timer de refresco antes de cerrar
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        super().finish(window)

def mostrar_presentacion():
    splash = Presentacion()
    splash.show()
    
    # Forzar el procesamiento de eventos inmediatamente
    QApplication.processEvents()
    
    # Retrasar las operaciones de inicialización
    QTimer.singleShot(500, lambda: _process_background_tasks(splash))
    
    return splash

def _process_background_tasks(splash):
    """Procesa las tareas de fondo mientras mantiene la presentación visible"""
    try:
        # Aquí pueden ir las operaciones de inicialización que necesiten esperar
        QApplication.processEvents()
    except Exception as e:
        print(f"Error en tareas de fondo: {e}")