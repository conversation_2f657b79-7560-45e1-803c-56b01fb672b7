import os
import sys
import time
import ctypes
import subprocess
import json
import threading
import shutil
import win32api
import win32file
from PyQt6.QtWidgets import QWidget, Q<PERSON>abel, QVBoxLayout
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot, QRect, QThread
from PyQt6.QtGui import QCursor
from APARIENCIA import (
    ACCENT_POLICY,
    WINDOWCOMPOSITIONATTRIBDATA,
    ACCENT_ENABLE_ACRYLICBLURBEHIND,
    WCA_ACCENT_POLICY,
    ACCENT_ENABLE_FLUENT,
    is_windows_11_or_greater
)

class PropiedadesDialog(QWidget):
    # Definir señal para actualizar UI desde cualquier hilo
    update_ui_signal = pyqtSignal(str)
    
    # En la clase PropiedadesDialog
    model_updated = pyqtSignal(str, str)  # drive_letter, model
    
    def __init__(self, parent=None):
        super().__init__(parent, Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint | Qt.WindowType.NoDropShadowWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        self.parent = parent
        
        self.current_item = None
        self.current_drive = None
        self.last_cursor_check = 0
        
        # Aplicar bordes redondeados
        hwnd = int(self.winId())
        ctypes.windll.dwmapi.DwmSetWindowAttribute(
            hwnd,
            33,  # DWMWA_WINDOW_CORNER_PREFERENCE
            ctypes.byref(ctypes.c_int(2)),  # DWMWCP_ROUND
            ctypes.sizeof(ctypes.c_int)
        )
        
        # Crear layout vertical
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 5)
        self.layout.setSpacing(0)
        
        # Crear label para la información
        self.label = QLabel(self)
        self.label.setStyleSheet("color: white; padding: 10px; background: transparent;")
        self.label.setTextFormat(Qt.TextFormat.RichText)  # Asegurar que interprete el HTML correctamente
        self.layout.addWidget(self.label)
        
        # Conectar las señales
        self.update_ui_signal.connect(self._update_ui_safe)
        
        # Temporizador para verificar posición del cursor
        self.check_timer = QTimer(self)
        self.check_timer.timeout.connect(self.check_cursor_position)
        self.check_timer.setInterval(100)
        
        # Texto predeterminado
        self._set_loading_text()
        
        # Al inicio de la clase PropiedadesDialog
        self._model_cache = {}
        self._cache_timeout = 300  # 5 minutos
    
    def _set_loading_text(self):
        """Establece un texto simple de carga"""
        html = """
        <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; color: white; font-weight: bold;">
            <div style="background: rgba(50, 50, 50, 0.8); padding: 8px; border-radius: 6px;">
                Cargando información...
            </div>
        </div>
        """
        self.label.setText(html)
        self.label.adjustSize()
        self.resize(self.label.sizeHint().width(), self.label.sizeHint().height() + 25)
    
    @pyqtSlot(str)
    def _update_ui_safe(self, html_content):
        """Actualiza la UI y muestra el diálogo cuando los datos están listos"""
        if not html_content:
            return
            
        # Eliminar el fondo del contenedor principal
        styled_html = f"""
        <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; color: white; font-weight: bold;">
            <div style="padding: 15px;">
                {html_content}
            </div>
        </div>
        """
        
        self.label.setText(styled_html)
        self.label.adjustSize()
        
        # Ajustar margen inferior y altura
        self.layout.setContentsMargins(0, 0, 0, 5)
        self.resize(self.label.sizeHint().width(), self.label.sizeHint().height() + 25)
        
        if not self.isVisible():
            self.show()

    def show_properties(self, drive_letter, pos, item=None, source_disk_info=None):
        """
        Muestra un diálogo con las propiedades del disco.
        
        Args:
            drive_letter: Letra de la unidad
            pos: Posición donde mostrar el diálogo
            item: Item del QListWidget asociado (opcional)
            source_disk_info: Tupla (letra, nombre) del disco origen si está copiando (opcional)
        """
        try:
            # Configurar básico
            self.current_item = item
            self.current_drive = drive_letter
            self.move(pos)
            
            # Aplicar efecto para cuando se muestre
            self._apply_acrylic_effect()
            
            # Iniciar verificación de posición
            if not self.check_timer.isActive():
                self.check_timer.start()
            
            # Obtener información básica del disco INMEDIATAMENTE usando APIs nativas
            try:
                # Obtener información básica del disco usando APIs nativas (muy rápido)
                volume_info = win32api.GetVolumeInformation(f"{drive_letter}\\")
                disk_space = shutil.disk_usage(f"{drive_letter}\\")
                
                # Extraer valores
                drive_name = volume_info[0] or "Sin nombre"
                fs_type = volume_info[4] if volume_info and len(volume_info) > 4 else ""  # Sistema de archivos
                total_bytes, used_bytes, free_bytes = disk_space
                
                # Formatear título con sistema de archivos
                title_with_fs = f"{drive_name} ({drive_letter})"
                if fs_type:
                    title_with_fs += f" ({fs_type})"
                
                # Convertir bytes a GB o TB según corresponda
                total_gb = total_bytes / (1024**3)
                free_gb = free_bytes / (1024**3)
                used_gb = used_bytes / (1024**3)
                
                # Determinar si mostrar en TB (si supera 1024 GB)
                use_tb = total_gb >= 1024
                if use_tb:
                    total_size = total_gb / 1024
                    free_size = free_gb / 1024
                    used_size = used_gb / 1024
                    size_unit = "TB"
                else:
                    total_size = total_gb
                    free_size = free_gb
                    used_size = used_gb
                    size_unit = "GB"
                
                # Intentar obtener el modelo del disco
                model = self.get_precargado_model(drive_letter)
                
                # Intentar obtener información del puerto USB desde la caché
                port_id = ""
                port_name = ""
                try:
                    import MONITOREO
                    if hasattr(MONITOREO, '_USB_PORT_CACHE'):
                        with MONITOREO._USB_PORT_CACHE_LOCK:
                            if drive_letter in MONITOREO._USB_PORT_CACHE:
                                port_id, port_name = MONITOREO._USB_PORT_CACHE[drive_letter]
                except Exception as e:
                    print(f"Error obteniendo puerto USB de caché: {e}")
                
                # Crear HTML con toda la información
                html = f"""
                <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; width: 220px; color: white; font-weight: bold;">
                    <div style="font-weight: bold; font-size: 14px; margin-bottom: 10px;">
                        {title_with_fs}
                    </div>
                """
                
                # Añadir modelo si está disponible
                if model:
                    html += f"""
                    <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                        <span>Modelo:</span>
                        <span style="max-width: 150px; overflow: hidden; text-overflow: ellipsis;">{model}</span>
                    </div>
                    """
                
                # Completar HTML con información de espacio
                html += f"""
                    <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                        <span>Total:</span>
                        <span>{total_size:.2f} {size_unit}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                        <span>Libre:</span>
                        <span>{free_size:.2f} {size_unit}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                        <span>En uso:</span>
                        <span>{used_size:.2f} {size_unit}</span>
                    </div>
                """
                
                # Añadir información del puerto USB si está disponible (penúltimo)
                if port_id and port_name:
                    html += f"""
                    <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                        <span>Puerto:</span>
                        <span style="max-width: 150px; overflow: hidden; text-overflow: ellipsis;">{port_name}</span>
                    </div>
                    """
                
                # Añadir información del disco origen si está copiando (último)
                if source_disk_info:
                    source_letter, source_name = source_disk_info
                    html += f"""
                    <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                        <span style="color: #00FF00;">Disco Lector:</span>
                        <span style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; color: #00FF00;">
                            {source_name} ({source_letter})
                        </span>
                    </div>
                    """
                
                html += "</div>"
                
                # Mostrar información inmediatamente
                self.label.setText(html)
                self.label.adjustSize()
                self.resize(self.label.sizeHint().width(), self.label.sizeHint().height() + 20)
                self.show()
                
            except Exception as e:
                # Si ocurre un error, mostrar diálogo básico
                # Intentar obtener el sistema de archivos al menos
                fs_type = ""
                try:
                    volume_info = win32api.GetVolumeInformation(f"{drive_letter}\\")
                    fs_type = volume_info[4] if volume_info and len(volume_info) > 4 else ""
                except:
                    pass
                
                # Formatear título con sistema de archivos si está disponible
                title = drive_letter
                if fs_type:
                    title += f" ({fs_type})"
                
                basic_html = f"""
                <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; width: 220px; color: white; font-weight: bold;">
                    <div style="font-weight: bold; font-size: 14px; margin-bottom: 10px;">{title}</div>
                    <div style="margin: 5px 0;">Calculando espacio...</div>
                </div>
                """
                self.label.setText(basic_html)
                self.label.adjustSize()
                self.resize(self.label.sizeHint().width(), self.label.sizeHint().height() + 20)
                self.show()
                
                # Intentar usar el método alternativo en un hilo
                threading.Thread(target=self.get_disk_info, args=(drive_letter,), daemon=True).start()
                
        except Exception as e:
            print(f"Error preparando propiedades: {str(e)}")
            
            # Intentar obtener el sistema de archivos al menos
            fs_type = ""
            try:
                volume_info = win32api.GetVolumeInformation(f"{drive_letter}\\")
                fs_type = volume_info[4] if volume_info and len(volume_info) > 4 else ""
            except:
                pass
            
            # Formatear título con sistema de archivos si está disponible
            title = drive_letter
            if fs_type:
                title += f" ({fs_type})"
                
            basic_html = f"""
            <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; width: 220px; color: white; font-weight: bold;">
                <div style="font-weight: bold; font-size: 14px; margin-bottom: 10px;">{title}</div>
                <div style="margin: 5px 0;">Error: {str(e)}</div>
            </div>
            """
            self.label.setText(basic_html)
            self.label.adjustSize()
            self.resize(self.label.sizeHint().width(), self.label.sizeHint().height() + 20)
            self.show()

    def get_precargado_model(self, drive_letter):
        """Obtiene el modelo del disco precargado desde MONITOREO.py"""
        try:
            # Importamos MONITOREO aquí para evitar importaciones circulares
            import MONITOREO
            
            # ULTRARRÁPIDO: Verificar primero en la caché global de MONITOREO
            if hasattr(MONITOREO, '_DISK_MODELS_CACHE'):
                if drive_letter in MONITOREO._DISK_MODELS_CACHE:
                    model = MONITOREO._DISK_MODELS_CACHE[drive_letter]
                    if model:
                        return model
            
            # Si no está en caché, utilizar el método rápido de MONITOREO
            model = MONITOREO.get_disk_model_direct(drive_letter)
            return model
        except Exception as e:
            print(f"Error obteniendo modelo precargado: {e}")
            return self.get_disk_model_fast(drive_letter)

    def get_disk_info(self, drive_letter):
        """Obtiene información del disco usando PowerShell"""
        try:
            # Limpiar letra de unidad para comando PowerShell
            drive_letter_clean = drive_letter.replace(":", "")
            
            # Comando PowerShell optimizado para obtener sistema de archivos
            powershell_cmd = f"""
            $drive = "{drive_letter_clean}:"
            $volumeInfo = Get-Volume -DriveLetter $drive[0] -ErrorAction SilentlyContinue | Select-Object FileSystemLabel, FileSystemType
            $driveInfo = Get-PSDrive -Name $drive[0] -PSProvider FileSystem -ErrorAction SilentlyContinue
            
            $free = $driveInfo.Free
            $used = $driveInfo.Used
            $total = $free + $used
            
            $freeGB = [math]::Round($free / 1GB, 2)
            $totalGB = [math]::Round($total / 1GB, 2)
            $usedGB = [math]::Round($used / 1GB, 2)
            
            $driveName = if ($volumeInfo.FileSystemLabel) {{ $volumeInfo.FileSystemLabel }} else {{ "Sin nombre" }}
            $fsType = $volumeInfo.FileSystemType
            
            @{{
                "DriveLetter" = $drive
                "DriveName" = $driveName
                "FileSystem" = $fsType
                "Free" = $freeGB
                "Used" = $usedGB
                "Total" = $totalGB
            }} | ConvertTo-Json
            """
            
            # Ejecutar PowerShell con procesamiento silencioso
            result = subprocess.run(
                ["powershell", "-NoProfile", "-Command", powershell_cmd],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode == 0 and result.stdout.strip():
                try:
                    # Cargar resultados como JSON
                    disk_info = json.loads(result.stdout.strip())
                    
                    # Valores por defecto en caso de problemas
                    drive_name = disk_info.get("DriveName", "Sin nombre")
                    fs_type = disk_info.get("FileSystem", "")
                    free_gb = disk_info.get("Free", 0)
                    used_gb = disk_info.get("Used", 0)
                    total_gb = disk_info.get("Total", 0)
                    
                    # Formatear título con sistema de archivos
                    title_with_fs = f"{drive_name} ({drive_letter})"
                    if fs_type:
                        title_with_fs += f" ({fs_type})"
                    
                    # Determinar si mostrar en TB (si supera 1024 GB)
                    use_tb = total_gb >= 1024
                    if use_tb:
                        total_size = total_gb / 1024
                        free_size = free_gb / 1024
                        used_size = used_gb / 1024
                        size_unit = "TB"
                    else:
                        total_size = total_gb
                        free_size = free_gb
                        used_size = used_gb
                        size_unit = "GB"
                    
                    # Intentar obtener el modelo de la caché global
                    model = None
                    try:
                        import MONITOREO
                        if hasattr(MONITOREO, '_DISK_MODELS_CACHE') and drive_letter in MONITOREO._DISK_MODELS_CACHE:
                            model = MONITOREO._DISK_MODELS_CACHE[drive_letter]
                    except Exception:
                        pass
                    
                    # Crear HTML para mostrar con el modelo si está disponible
                    html = f"""
                    <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; width: 220px; color: white; font-weight: bold;">
                        <div style="font-weight: bold; font-size: 14px; margin-bottom: 10px;">
                            {title_with_fs}
                        </div>
                    """
                    
                    # Añadir modelo si está disponible
                    if model:
                        html += f"""
                        <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                            <span>Modelo:</span>
                            <span style="max-width: 150px; overflow: hidden; text-overflow: ellipsis;">{model}</span>
                        </div>
                        """
                    
                    # Completar HTML con información de espacio
                    html += f"""
                        <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                            <span>Total:</span>
                            <span>{total_size:.2f} {size_unit}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                            <span>Libre:</span>
                            <span>{free_size:.2f} {size_unit}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                            <span>En uso:</span>
                            <span>{used_size:.2f} {size_unit}</span>
                        </div>
                    """
                    
                    html += "</div>"
                    
                    # Actualizar la UI
                    self.update_ui_signal.emit(html)
                except Exception as e:
                    error_html = f"""
                    <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; width: 200px; color: white; font-weight: bold;">
                        <div style="font-weight: bold; font-size: 14px; margin-bottom: 10px;">{drive_letter}</div>
                        <div>Error: {str(e)}</div>
                    </div>
                    """
                    self.update_ui_signal.emit(error_html)
            else:
                error_html = f"""
                <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; width: 200px; color: white; font-weight: bold;">
                    <div style="font-weight: bold; font-size: 14px; margin-bottom: 10px;">{drive_letter}</div>
                    <div>Error obteniendo datos</div>
                </div>
                """
                self.update_ui_signal.emit(error_html)
        except Exception as e:
            error_html = f"""
            <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; width: 200px; color: white; font-weight: bold;">
                <div style="font-weight: bold; font-size: 14px; margin-bottom: 10px;">{drive_letter}</div>
                <div>Error: {str(e)}</div>
            </div>
            """
            self.update_ui_signal.emit(error_html)

    def get_disk_model_fast(self, drive_letter):
        """Obtiene información básica del dispositivo con sistema de archivos"""
        try:
            # Verificar caché primero
            if drive_letter in self._model_cache:
                cache_time, cached_model = self._model_cache[drive_letter]
                if time.time() - cache_time < self._cache_timeout:
                    return cached_model
            
            # Obtener tipo de unidad y sistema de archivos (operaciones inmediatas)
            drive_type = win32file.GetDriveType(f"{drive_letter}\\")
            
            try:
                # Obtener sistema de archivos
                volume_info = win32api.GetVolumeInformation(f"{drive_letter}\\")
                fs_type = volume_info[4] if volume_info else ""  # Sistema de archivos
            except:
                fs_type = ""
            
            # Intentar obtener modelo del dispositivo 
            device_model = self.get_physical_disk_model(drive_letter)
            
            # Si tenemos modelo físico, devolverlo con sistema de archivos
            if device_model:
                if fs_type:
                    result = f"{device_model} ({fs_type})"
                else:
                    result = device_model
                
                # Guardar en caché
                self._model_cache[drive_letter] = (time.time(), result)
                return result
            
            # Si no hay modelo, mostrar información básica según tipo de dispositivo
            if drive_type == win32file.DRIVE_UNKNOWN:
                base_type = "Unidad desconocida"
            elif drive_type == win32file.DRIVE_NO_ROOT_DIR:
                base_type = "Unidad no disponible"
            elif drive_type == win32file.DRIVE_REMOVABLE:
                base_type = "Dispositivo extraíble"
            elif drive_type == win32file.DRIVE_FIXED:
                if drive_letter.upper() == "C:":
                    base_type = "Disco del sistema"
                else:
                    base_type = "Disco"
            elif drive_type == win32file.DRIVE_REMOTE:
                base_type = "Unidad de red"
            elif drive_type == win32file.DRIVE_CDROM:
                base_type = "Unidad óptica"
            elif drive_type == win32file.DRIVE_RAMDISK:
                base_type = "Disco RAM"
            else:
                base_type = "Dispositivo de almacenamiento"
            
            # Combinar tipo base con sistema de archivos
            if fs_type:
                result = f"{base_type} ({fs_type})"
            else:
                result = base_type
                
            # Guardar en caché
            self._model_cache[drive_letter] = (time.time(), result)
            return result
                
        except Exception as e:
            # En caso de error, retornar valor por defecto
            print(f"Error determinando información de unidad: {e}")
            return "Dispositivo de almacenamiento"
    
    def get_physical_disk_model(self, drive_letter):
        """Intenta obtener el modelo físico del dispositivo usando WMI"""
        try:
            # Consulta WMI más directa
            powershell_cmd = f"""
            $drive = "{drive_letter.replace(':', '')}"
            Get-CimInstance -Query "
                SELECT Model FROM Win32_DiskDrive WHERE DeviceID IN (
                    SELECT Dependent FROM Win32_DriveToDiskPartition WHERE Antecedent IN (
                        SELECT Path FROM Win32_LogicalDisk WHERE DeviceID='$drive`:'
                    )
                )
            " | Select-Object -ExpandProperty Model
            """
            
            # Ejecutar PowerShell con procesamiento silencioso
            result = subprocess.run(
                ["powershell", "-NoProfile", "-Command", powershell_cmd],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode == 0 and result.stdout.strip():
                return result.stdout.strip()
            return ""
            
        except Exception as e:
            print(f"Error obteniendo modelo de disco: {str(e)}")
            return ""

    def check_cursor_position(self):
        """Verifica si el cursor sigue sobre el item para decidir si ocultar el diálogo"""
        if not self.current_item:
            self.hide()
            return
            
        # OPTIMIZACIÓN: Evitar verificaciones demasiado frecuentes
        current_time = time.time()
        if current_time - self.last_cursor_check < 0.1:  # Reducir a máximo 10 veces por segundo
            return
        self.last_cursor_check = current_time
        
        current_pos = self.parent.mapFromGlobal(QCursor.pos())
        
        # OPTIMIZACIÓN: Realizar verificación simplificada
        # Obtener el rectángulo del item una sola vez
        item_rect = self.parent.visualItemRect(self.current_item)
        
        # Crear un área ampliada para mejor detección
        # Agregar margen adicional para evitar que el tooltip desaparezca por pequeños movimientos
        icon_area = QRect(
            item_rect.left() - 5,  # Margen izquierdo adicional 
            item_rect.top() - 5,   # Margen superior adicional
            60,                    # Ancho ampliado para mejor detección 
            item_rect.height() + 10  # Altura ampliada con margen
        )
        
        # Verificar si el cursor está fuera del área ampliada
        if not icon_area.contains(current_pos):
            self.hide()
            self.current_drive = None
    
    def _apply_acrylic_effect(self):
        """Aplica el efecto acrílico a la ventana"""
        hwnd = int(self.winId())
        
        # Primero aplicar bordes redondeados
        try:
            # DWMWA_WINDOW_CORNER_PREFERENCE = 33
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                33,  # DWMWA_WINDOW_CORNER_PREFERENCE
                ctypes.byref(ctypes.c_int(2)),  # DWMWCP_ROUND (2 = redondeado)
                ctypes.sizeof(ctypes.c_int)
            )
        except Exception as e:
            print(f"Error aplicando bordes redondeados: {e}")
        
        # Luego aplicar efecto acrílico
        try:
            accent = ACCENT_POLICY()
            if is_windows_11_or_greater():
                accent.AccentState = ACCENT_ENABLE_ACRYLICBLURBEHIND
                accent.GradientColor = 0x20000000  # Ajustar transparencia 
            else:
                accent.AccentState = ACCENT_ENABLE_FLUENT
                accent.GradientColor = 0x30000000  # Más transparente en Windows 10
            
            accent.AccentFlags = 0x20 | 0x40 | 0x80 | 0x100
            data = WINDOWCOMPOSITIONATTRIBDATA()
            data.Attribute = WCA_ACCENT_POLICY
            data.SizeOfData = ctypes.sizeof(accent)
            data.Data = ctypes.cast(ctypes.pointer(accent), ctypes.POINTER(ctypes.c_int))
            ctypes.windll.user32.SetWindowCompositionAttribute(hwnd, ctypes.byref(data))
        except Exception as e:
            print(f"Error aplicando efecto acrílico: {e}")

    def hideEvent(self, event):
        # Detener temporizador
        self.check_timer.stop()
        super().hideEvent(event)

    def update_dialog_with_model(self, drive_letter, drive_name, model):
        """Actualiza el diálogo añadiendo el modelo del disco"""
        try:
            # Obtener información actualizada del disco
            disk_space = shutil.disk_usage(f"{drive_letter}\\")
            total_bytes, used_bytes, free_bytes = disk_space
            
            # Intentar obtener el sistema de archivos
            try:
                volume_info = win32api.GetVolumeInformation(f"{drive_letter}\\")
                fs_type = volume_info[4] if volume_info and len(volume_info) > 4 else ""
            except:
                fs_type = ""
            
            # Formatear título con sistema de archivos
            title_with_fs = f"{drive_name} ({drive_letter})"
            if fs_type:
                title_with_fs += f" ({fs_type})"
            
            # Convertir bytes a GB o TB según corresponda
            total_gb = total_bytes / (1024**3)
            free_gb = free_bytes / (1024**3)
            used_gb = used_bytes / (1024**3)
            
            # Determinar si mostrar en TB (si supera 1024 GB)
            use_tb = total_gb >= 1024
            if use_tb:
                total_size = total_gb / 1024
                free_size = free_gb / 1024
                used_size = used_gb / 1024
                size_unit = "TB"
            else:
                total_size = total_gb
                free_size = free_gb
                used_size = used_gb
                size_unit = "GB"
            
            # Crear HTML con toda la información sin el modelo
            html = f"""
            <div style="font-family: Segoe UI; font-size: 12px; padding: 15px; width: 220px; color: white; font-weight: bold;">
                <div style="font-weight: bold; font-size: 14px; margin-bottom: 10px;">
                    {title_with_fs}
                </div>
            </div>
            """
            
            # Actualizar el diálogo
            self.update_ui_signal.emit(html)
        except Exception as e:
            print(f"Error actualizando diálogo con modelo: {str(e)}")

    def update_model_async(self, drive_letter):
        def worker():
            model = self.get_disk_model_fast(drive_letter)
            self.model_updated.emit(drive_letter, model)
        
        QThread.create(worker).start()
