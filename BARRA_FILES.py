from PyQt6.QtWidgets import QProgressBar
from PyQt6.QtGui import QPainter, QColor, QPainterPath
from PyQt6.QtCore import Qt, QRectF, pyqtProperty

class BarraFiles(QProgressBar):
    """
    Barra de progreso vertical personalizada para archivos
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTextVisible(False)
        self.setStyleSheet("""
            QProgressBar {
                background-color: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        # Color por defecto verde semi-transparente
        self.base_color = QColor(0, 255, 0, 178)  # Verde con 70% opacidad
        self.border_radius = 4  # Radio de borde más pequeño para barra vertical
        self.setFixedWidth(4)  # Ancho fijo pequeño
        
    def setBaseColor(self, color):
        """Establece el color base de la barra de progreso"""
        try:
            if isinstance(color, str):
                self.base_color = QColor(color)
            elif isinstance(color, QColor):
                self.base_color = color
            else:
                self.base_color = QColor(0, 255, 0, 178)
            self.update()
        except Exception as e:
            print(f"Error al establecer color: {e}")
            self.base_color = QColor(0, 255, 0, 178)

    def setBorderRadius(self, radius):
        """Establece el radio de borde redondeado"""
        self.border_radius = radius
        self.update()
    
    @pyqtProperty(QColor)
    def color(self):
        return self.base_color
        
    @color.setter
    def color(self, color):
        self.setBaseColor(color)

    def paintEvent(self, event):
        """Dibuja la barra de progreso vertical"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        width = self.width()
        height = self.height()
        
        # Crear el contenedor con bordes redondeados
        container_path = QPainterPath()
        container_path.addRoundedRect(
            QRectF(0, 0, width, height),
            self.border_radius, self.border_radius
        )
        
        # Calcular altura de la barra según el progreso
        progress = self.value() / (self.maximum() - self.minimum())
        progress_height = int(height * progress)
        
        # Si hay progreso, dibujar la barra vertical
        if progress_height > 0:
            progress_path = QPainterPath()
            # Dibujar desde abajo hacia arriba
            progress_path.addRect(0, height - progress_height, width, progress_height)
            
            # Intersectar con el contenedor redondeado
            progress_path = progress_path.intersected(container_path)
            
            # Dibujar el progreso
            painter.fillPath(progress_path, self.base_color)
        
        painter.end()