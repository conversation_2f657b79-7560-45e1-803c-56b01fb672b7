# CREAR_CARPETA.py
from PyQt6 import Qt<PERSON>ore, QtWidgets, QtGui
from PyQt6.QtWidgets import (
    QDialog, QHBoxLayout, QLabel, QLineEdit, QGraphicsDropShadowEffect, QMessageBox,
    QTreeWidgetItem
)
from PyQt6.QtCore import Qt, QPropertyAnimation
from PyQt6.QtGui import QPainter, QColor, QPainterPath, QIcon
import os
from APARIENCIA import apply_acrylic_and_rounded
from CREAR import nueva_carpeta, create_yes_icon, icono_carpeta, registro

class AnimatedButton(QtWidgets.QPushButton):
    def __init__(self, icon_path=None, parent=None, is_custom_icon=False):
        super().__init__(parent)
        if icon_path and not is_custom_icon:
            self.setIcon(QtGui.QIcon(icon_path))
        self.setStyleSheet("border: none; background: transparent;")
        self.setMinimumSize(35, 35)
        self.setMaximumSize(35, 35)
        self._icon_size = 25
        self.setMouseTracking(True)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # Añadir efecto de sombra
        shadow = QtWidgets.QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QtCore.Qt.GlobalColor.white)
        self.setGraphicsEffect(shadow)

    @QtCore.pyqtProperty(int)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.setIconSize(QtCore.QSize(size, size))

    def enterEvent(self, event):
        self.animate_icon_size(30)  # Enlarged icon size

    def leaveEvent(self, event):
        super().leaveEvent(event)
        self.unsetCursor()  # Restablecer cursor al salir del botón

    def animate_icon_size(self, target_size):
        self.animation = QtCore.QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(50)  # Duration of the animation in milliseconds
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

class CreateEntryDialog(QDialog):
    def __init__(self, parent=None, title="Nuevo", placeholder="Nombre", icon=None):
        super().__init__(parent)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.Popup | 
            Qt.WindowType.NoDropShadowWindowHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)

        # Aplicar efecto acrílico
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # Icono
        self.icon_label = QLabel()
        if icon:
            pixmap = icon.pixmap(30, 30)
            self.icon_label.setPixmap(pixmap)
        self.icon_label.setFixedSize(30, 30)
        self.icon_label.setCursor(Qt.CursorShape.PointingHandCursor)
        icon_shadow = QGraphicsDropShadowEffect()
        icon_shadow.setBlurRadius(15)
        icon_shadow.setXOffset(0)
        icon_shadow.setYOffset(2)
        icon_shadow.setColor(QColor(0, 0, 0, 160))
        self.icon_label.setGraphicsEffect(icon_shadow)
        layout.addWidget(self.icon_label)

        # Campo de texto
        self.line_edit = QLineEdit()
        self.line_edit.setPlaceholderText(placeholder)
        self.line_edit.setMinimumWidth(200)
        self.line_edit.setStyleSheet("""
            QLineEdit {
                background-color: rgba(43, 43, 43, 0.7);
                color: white;
                border: none;
                border-radius: 15px;
                padding: 5px 15px;
                font-size: 13px;
                height: 20px;
            }
            QLineEdit:focus {
                background-color: rgba(55, 55, 55, 0.8);
                border: 1px solid rgba(0, 120, 215, 0.7);
            }
        """)
        line_edit_shadow = QGraphicsDropShadowEffect()
        line_edit_shadow.setBlurRadius(15)
        line_edit_shadow.setXOffset(0)
        line_edit_shadow.setYOffset(2)
        line_edit_shadow.setColor(QColor(0, 0, 0, 160))
        self.line_edit.setGraphicsEffect(line_edit_shadow)
        layout.addWidget(self.line_edit)

        # Botón de confirmación
        self.confirm_button = AnimatedButton(None, self)
        self.confirm_button.setIcon(create_yes_icon(size=30))
        self.confirm_button.setIconSize(QtCore.QSize(25, 25))
        self.confirm_button._icon_size = 25
        self.confirm_button.setFixedSize(30, 30)
        self.confirm_button.clicked.connect(self.accept)
        button_shadow = QGraphicsDropShadowEffect()
        button_shadow.setBlurRadius(15)
        button_shadow.setXOffset(0)
        button_shadow.setYOffset(2)
        button_shadow.setColor(QColor(0, 0, 0, 160))
        self.confirm_button.setGraphicsEffect(button_shadow)
        layout.addWidget(self.confirm_button)

        self.setWindowTitle(title)
        self.adjustSize()
        self.line_edit.setFocus()
        self.line_edit.selectAll()
        self.initial_pos = None
        self.setWindowOpacity(0.0)

    def get_text(self):
        return self.line_edit.text().strip()

    def showEvent(self, event):
        super().showEvent(event)
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd)
        self.final_pos = self.pos()
        start_pos = self.final_pos + QtCore.QPoint(0, 50)
        self.move(start_pos)
        self.pos_anim = QPropertyAnimation(self, b"pos")
        self.pos_anim.setDuration(300)
        self.pos_anim.setStartValue(start_pos)
        self.pos_anim.setKeyValueAt(0.6, self.final_pos - QtCore.QPoint(0, 8))
        self.pos_anim.setKeyValueAt(0.8, self.final_pos + QtCore.QPoint(0, 4))
        self.pos_anim.setEndValue(self.final_pos)
        self.pos_anim.setEasingCurve(QtCore.QEasingCurve.Type.OutBounce)
        self.fade_anim = QPropertyAnimation(self, b"windowOpacity")
        self.fade_anim.setDuration(200)
        self.fade_anim.setStartValue(0.0)
        self.fade_anim.setEndValue(1.0)
        self.fade_anim.setEasingCurve(QtCore.QEasingCurve.Type.OutCubic)
        self.pos_anim.start()
        self.fade_anim.start()
        self.line_edit.setFocus()
        self.line_edit.selectAll()

    def keyPressEvent(self, event):
        if event.key() in (Qt.Key.Key_Enter, Qt.Key.Key_Return):
            self.accept()
        elif event.key() == Qt.Key.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

    def accept(self):
        self.unsetCursor()  # Restablecer cursor antes de cerrar
        if self.parent():
            self.parent().unsetCursor()  # Restablecer cursor en la ventana principal
        super().accept()

    def reject(self):
        self.unsetCursor()  # Restablecer cursor antes de cerrar
        if self.parent():
            self.parent().unsetCursor()
        super().reject()

def create_new_folder(main_window):
    """
    Función para crear una nueva carpeta en el directorio actual.
    
    Args:
        main_window: La ventana principal de la aplicación
    """
    dialog = CreateEntryDialog(
        main_window,
        title="Nueva Carpeta",
        placeholder="Nombre de la carpeta",
        icon=nueva_carpeta(size=30)
    )
    if dialog.exec() == QDialog.DialogCode.Accepted:
        folder_name = dialog.get_text()
        if folder_name:
            new_folder_path = os.path.join(main_window.current_drive, folder_name)
            counter = 1
            base_name = folder_name
            while os.path.exists(new_folder_path):
                new_folder_path = os.path.join(main_window.current_drive, f"{base_name} ({counter})")
                counter += 1
            try:
                os.makedirs(new_folder_path)
                print(f"Nueva carpeta creada en: {new_folder_path}")
                folder_name = os.path.basename(new_folder_path)
                
                # Crear un nuevo ítem en el árbol de archivos
                new_item = QTreeWidgetItem([folder_name, "0 B"])
                new_item.setIcon(0, icono_carpeta(size=24))
                new_item.setData(0, Qt.ItemDataRole.UserRole, True)  # True para indicar que es directorio
                
                # Configurar el estilo del texto
                font = new_item.font(0)
                font.setBold(True)
                new_item.setFont(0, font)
                new_item.setFont(1, font)
                new_item.setFlags(new_item.flags() | Qt.ItemFlag.ItemIsEditable)
                
                # Insertar el nuevo ítem en la parte superior del árbol
                main_window.file_tree_widget.insertTopLevelItem(0, new_item)
            except Exception as e:
                print(f"Error al crear nueva carpeta: {e}")
                QMessageBox.warning(main_window, "Error", f"No se pudo crear la carpeta: {str(e)}")

def create_new_file(main_window):
    dialog = CreateEntryDialog(
        main_window,
        title="Nuevo Archivo de Texto",
        placeholder="archivo.txt",
        icon=registro(size=30)
    )
    if dialog.exec() == QDialog.DialogCode.Accepted:
        file_name = dialog.get_text()
        if file_name:
            if not file_name.lower().endswith('.txt'):
                file_name += '.txt'
            file_path = os.path.join(main_window.current_drive, file_name)
            if os.path.exists(file_path):
                QMessageBox.warning(main_window, "Error", "El archivo ya existe.")
                return
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("")  # Archivo vacío
                # Opcional: actualiza la vista del explorador
                if hasattr(main_window, 'add_new_txt_to_view'):
                    main_window.add_new_txt_to_view(file_name)
            except Exception as e:
                QMessageBox.warning(main_window, "Error", f"No se pudo crear el archivo: {str(e)}")
