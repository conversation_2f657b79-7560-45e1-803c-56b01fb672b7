import sys
import shutil
import win32event
import win32api
import winerror
import win32file
import win32process
import win32con
import traceback  
import win32gui  
import win32con
from datetime import datetime  
from BARRA_MINI import RoundedProgressBar
from DRAGGABLE_LIST_WIDGET import DraggableListWidget

from PyQt6.QtWidgets import (
    QApplication, QTreeWidgetItem, QFileIconProvider, QTreeWidget, QInputDialog, 
    QDialog, QToolTip, QHeaderView, QLineEdit, QFileDialog, QSpacerItem, 
    QToolButton, QMessageBox, QStackedLayout, QSizePolicy, QMainWindow, 
    QDockWidget, QFrame, QListWidget, QVBoxLayout, QListWidgetItem, 
    QProgressBar, QSizeGrip, QAbstractItemView, QLabel, QHBoxLayout, 
    QWidget, QGraphicsDropShadowEffect, QPushButton  # <PERSON>ña<PERSON>do <PERSON>utt<PERSON> aquí
)
from PyQt6.QtCore import Qt, QUrl, QMimeData, QPropertyAnimation, QRectF, QParallelAnimationGroup, QMutex, QSize, QFileInfo, QDateTime, QPoint, QRect, QLocale, QEvent, QTimer, QThread, pyqtSignal, Q_ARG, pyqtSlot, QObject, QRunnable, QThreadPool, QMetaObject  # Añadidas estas importaciones
from PyQt6.QtGui import (
    QPainter, QFont, QColor, QCursor, QDragEnterEvent, QDropEvent, 
    QIcon, QPixmap, QShortcut, QKeySequence, QPainterPath, QPen
)
from MONITOREO import (
    get_motherboard_info,
    get_system_uuid, 
    get_disk_serial_number
)
import logging
import ctypes, win32api
from PyQt6 import QtCore, QtWidgets, QtGui
from RENOMBRAR import CustomRenameDialog
from MANTENIMIENTO import repair_disks_in_background
import os
import math
import errno, pywintypes
from cryptography.fernet import Fernet
from PyQt6.QtCore import QMetaObject, Qt, pyqtSignal
from collections import OrderedDict
from datetime import datetime
import subprocess
from ctypes import windll, byref, sizeof, c_int # PARA LOS BORDES REDONDEADOS DE LA VENTANA
import re
import time
import queue
import wmi 
from AJUSTES import TransparentWindow, save_config, load_config
from BORRADO import confirm_delete_selected_items
import json
from CONTEXTUAL import create_context_menu, reset_temporary_alias, create_registry, hide_drive, explore_drive, toggle_payment_calculation
import threading
from MONITOREO import Worker
from BOTONES import Botones
from COLA import ColaWindow
from threading import Lock
from APARIENCIA import ACCENT_POLICY, WINDOWCOMPOSITIONATTRIBDATA, ACCENT_ENABLE_ACRYLICBLURBEHIND, WCA_ACCENT_POLICY, apply_acrylic_effect, apply_acrylic_and_rounded
from BORRADO import confirm_delete_selected_items
from EMPAKETADO import create_empaketado
from RAM import RAMUsageWidget
from Propiedades_Disco import PropiedadesDialog
from REDIMENSIONAR import WindowResizer
from BARRA_ARRASTRE import DragProgressBar
from ICONOS_EXPLORER import FileTypeIconManager
# CreateFolderDialog se ha movido a CREAR_CARPETA.py
from COPIA_DE_INDEXADO import IndexCopyHandler, ExtensionDiffDialog
import atexit, stat
import weakref
from MONITOREO import get_original_info, generate_unique_code
from PyQt6 import sip
from ACTIVATE import show_license_activation_dialog  # Añadir esta importación al inicio del archivo
from CREAR import icono_carpeta, create_repair_icon, create_temp_name_icon, boton_mapear_puertos, icono_expulsar_usb
from TOOLTIP_APARIENCIA import showTooltipAtCursor, showTooltipAtWidget, _hide_active_tooltip

#--------------------------------------------------------------------------------------------------# PARASONIDOS
GLOBAL_BUFFER_SIZE_PER_DEVICE = 300 * 1024 * 1024 
import pygame                                                                                       
pygame.mixer.init()
def get_config_path():
    if getattr(sys, 'frozen', False):
            # Si está compilado (exe)
        return os.path.join(os.path.dirname(sys.executable), 'config.json')
    else:
            # En desarrollo
        return os.path.join(os.path.dirname(__file__), 'config.json')

def play_drag_sound(files_added=0):
    try:
        from SONIDOS import sound_manager
        if files_added > 0:
            sound_manager.play_sound('drag')
        else:
            sound_manager.play_sound('error')
    except Exception as e:
        print(f"Error al reproducir sonido: {e}")

def get_instance_temp_dir():
    base_temp_dir = os.path.join('C:\\', 'ProgramData', '.zetacopy_temp')
    if not os.path.exists(base_temp_dir):
        os.makedirs(base_temp_dir)
    instance_id = str(os.getpid())
    instance_temp_dir = os.path.join(base_temp_dir, f'instancia_{instance_id}')
    if not os.path.exists(instance_temp_dir):
        os.makedirs(instance_temp_dir)
    return instance_temp_dir
def limpiar_temporal():
    """Limpia SOLO el directorio temporal de esta instancia"""
    temp_dir = get_instance_temp_dir()
    if not os.path.exists(temp_dir):
        return
    try:
        items = os.listdir(temp_dir)
        if not items:
            return
        for item in items:
            item_path = os.path.join(temp_dir, item)
            try:
                if os.path.isfile(item_path):
                    os.chmod(item_path, stat.S_IWRITE)
                    os.unlink(item_path)
                elif os.path.isdir(item_path):
                    shutil.rmtree(item_path, onerror=lambda func, path, exc_info: (
                        os.chmod(path, stat.S_IWRITE),
                        func(path)
                    ))
            except Exception as e:
                print(f"Error al procesar {item_path}: {e}")
                continue
        print("Directorio temporal de la instancia limpiado")
    except Exception as e:
        if "Acceso denegado" not in str(e):
            print(f"Error al limpiar directorio temporal: {e}")
@pyqtSlot()
def cleanup_before_exit(self):
    try:
        QApplication.processEvents()
        QTimer.singleShot(100, lambda: (
            limpiar_temporal(),
            time.sleep(0.2),
            os._exit(0) if hasattr(os, '_exit') else sys.exit(0)
        ))
    except Exception as e:
        print(f"Error en cleanup_before_exit: {e}")
        sys.exit(0)

class EjectDriveTask(QRunnable):
    def __init__(self, drive_letter, main_window):
        super().__init__()
        self.drive_letter = drive_letter
        self.main_window = main_window
    def run(self):
        try:
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            removedrive_path = os.path.join(base_path, 'RemoveDrive.exe')
            
            if not os.path.exists(removedrive_path):
                print(f"Error: RemoveDrive.exe no encontrado en {removedrive_path}")
                return
            # Configuración para ocultar la consola
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            process = subprocess.Popen(
                [
                    removedrive_path, 
                    self.drive_letter,
                    '-l',     # Loop hasta que tenga éxito
                    '-h',     # Mostrar handles abiertos
                    '-a',     # Activar ventanas de aplicaciones con handles abiertos
                    '-i',     # Detener servicio de indexación si es necesario
                    '-f',     # Forzar expulsión si falla el primer intento
                    '-tm',    # Cerrar Task Manager si está bloqueando
                    '-w:1000' # Esperar 1 segundo antes de cerrar
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                startupinfo=startupinfo  # Ocultar consola
            )
            stdout, stderr = process.communicate(timeout=15)
            if "success" in stdout.lower():
                print(f"Disco {self.drive_letter} expulsado correctamente")
                QMetaObject.invokeMethod(
                    self.main_window,
                    "remove_disk_from_view",
                    Qt.ConnectionType.QueuedConnection,
                    Q_ARG(str, self.drive_letter)
                )
            else:
                print(f"Error en primer intento de expulsión: {stdout}")
                print(f"stderr: {stderr}")
                process = subprocess.Popen(
                    [
                        removedrive_path, 
                        self.drive_letter,
                        '-l',     # Loop hasta que tenga éxito
                        '-f',     # Forzar expulsión
                        '-tm',    # Cerrar Task Manager
                        '-i',     # Detener indexación
                        '-d',     # Remover solo el drive en lugar del dispositivo completo 
                        '-vhd'    # Desmontar VHD/VHDX/ISO si existe
                    ],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    startupinfo=startupinfo  # Ocultar consola
                )
                stdout, stderr = process.communicate(timeout=15)
                if "success" in stdout.lower():
                    print(f"Disco {self.drive_letter} expulsado correctamente en segundo intento")
                    QMetaObject.invokeMethod(
                        self.main_window,
                        "remove_disk_from_view",
                        Qt.ConnectionType.QueuedConnection,
                        Q_ARG(str, self.drive_letter)
                    )
                else:
                    print(f"Error en segundo intento de expulsión: {stdout}")
                    print(f"stderr: {stderr}")
        except Exception as e:
            print(f"Error inesperado en expulsión del disco {self.drive_letter}: {e}")
            import traceback
            traceback.print_exc()

class FileProcessingThread(QThread):
    filesProcessed = pyqtSignal(list, str, bool, str, str, bool)
    progressUpdated = pyqtSignal(int)
    calculationStarted = pyqtSignal()
    calculationFinished = pyqtSignal()
    fileProgressUpdated = pyqtSignal(int, int)  # (archivos_procesados, total_archivos)
    def __init__(self, urls, drive_letter, shift_pressed, current_path, volume_name, dragging_folder, total_files, progress_bar):
        super().__init__()
        self.urls = urls
        self.drive_letter = drive_letter
        self.shift_pressed = shift_pressed
        if len(current_path) == 2 and current_path[1] == ':':
            current_path = current_path + '\\'
        self.current_path = current_path
        self.volume_name = volume_name
        self.dragging_folder = dragging_folder
        self.total_files = max(1, total_files)
        self.files_processed = 0
        self.progress_bar = progress_bar
        self.should_stop = False
        self._is_running = False
    def run(self):
        try:
            self._is_running = True
            self.calculationStarted.emit()
            processed_files = []
            valid_files = 0
            for url in self.urls:
                if not self._is_running:
                    break
                file_path = url.toLocalFile()
                file_path = os.path.normpath(file_path)
                if os.path.isfile(file_path):
                    dest_path = os.path.join(self.current_path, os.path.basename(file_path))
                    if not os.path.exists(dest_path) or os.path.getsize(file_path) != os.path.getsize(dest_path):
                        valid_files += 1
                elif os.path.isdir(file_path):
                    for root, _, files in os.walk(file_path):
                        for file in files:
                            src_path = os.path.join(root, file)
                            rel_path = os.path.relpath(src_path, os.path.dirname(file_path))
                            dest_path = os.path.join(self.current_path, rel_path)
                            if not os.path.exists(dest_path) or os.path.getsize(src_path) != os.path.getsize(dest_path):
                                valid_files += 1
            self.files_processed = 0
            total_processed = 0
            for url in self.urls:
                if not self._is_running:
                    break
                file_path = url.toLocalFile()
                try:
                    file_path = os.path.normpath(file_path)
                    if os.path.isfile(file_path):
                        self.process_file(file_path, processed_files, valid_files)
                    elif os.path.isdir(file_path):
                        self.process_directory(file_path, processed_files, valid_files)
                    total_processed += 1
                    if valid_files > 0:
                        progress = int((total_processed * 100) / len(self.urls))
                        self.progressUpdated.emit(progress)
                except Exception as e:
                    print(f"Error procesando {file_path}: {str(e)}")
                    continue
            if processed_files and self._is_running:
                self.filesProcessed.emit(processed_files, self.drive_letter, self.shift_pressed,
                                       self.current_path, self.volume_name, self.dragging_folder)
            # Borrar el archivo pending_copies cuando la copia está completa
                try:
                    pending_file = os.path.join(os.environ.get('PROGRAMDATA'), '.zetacopy_temp', f'pending_copies_{self.drive_letter}.txt')
                    if os.path.exists(pending_file):
                        os.remove(pending_file)
                        print(f"Archivo de copias pendientes eliminado para el disco {self.drive_letter}")
                except Exception as e:
                    print(f"Error al eliminar archivo de copias pendientes: {e}")
        except Exception as e:
            print(f"Error en el procesamiento: {str(e)}")
        finally:
            self._is_running = False
            self.calculationFinished.emit()

    def process_file(self, file_path, processed_files, total_valid):
        try:
            if not self._is_running:
                return
            file_size = os.path.getsize(file_path)
            destination_path = os.path.join(self.current_path, os.path.basename(file_path))
            print(f"DEBUG - process_file - current_path: {self.current_path}, dest_path: {destination_path}")
            if not os.path.exists(destination_path) or os.path.getsize(file_path) != file_size:
                processed_files.append((file_path, file_size, destination_path))
                self.files_processed += 1
                self.fileProgressUpdated.emit(self.files_processed, total_valid)
        except Exception as e:
            print(f"Error procesando archivo {file_path}: {str(e)}")

    def process_directory(self, dir_path, processed_files, total_valid):
        try:
            for root, _, files in os.walk(dir_path):
                if not self._is_running:
                    break
                for file in files:
                    if not self._is_running:
                        break
                    full_path = os.path.join(root, file)
                    try:
                        file_size = os.path.getsize(full_path)
                        relative_path = os.path.relpath(full_path, os.path.dirname(dir_path))
                        destination_path = os.path.join(self.current_path, relative_path)
                        if not os.path.exists(destination_path) or os.path.getsize(full_path) != file_size:
                            processed_files.append((full_path, file_size, destination_path))
                            self.files_processed += 1
                            self.fileProgressUpdated.emit(self.files_processed, total_valid)
                    except Exception as e:
                        print(f"Error procesando archivo en directorio {full_path}: {str(e)}")
                        continue
        except Exception as e:
            print(f"Error procesando directorio {dir_path}: {str(e)}")

    def stop(self):
        self._is_running = False
        drive_letter = self.drive_letter
        if hasattr(self.main_window, 'files_in_queue_by_drive') and drive_letter in self.main_window.files_in_queue_by_drive:
            self.main_window.files_in_queue_by_drive[drive_letter].clear()
            print(f"Cola limpiada para el disco {drive_letter}")
        if hasattr(self.main_window, 'files_in_queue'):
            # Filtrar los elementos que corresponden a este disco
            self.main_window.files_in_queue = {
                (src, dest) for src, dest in self.main_window.files_in_queue 
                if not dest.startswith(drive_letter)
            }



class CustomListWidgetItem(QWidget):
    def __init__(self, volume, free_space_gb, main_window):
        super().__init__()
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.main_window = main_window

        # Configuración del fondo semitransparente (gris claro con 40% transparencia)
        self.background_color = QColor(128, 128, 128, 50)
        self.border_radius = 10  # Radio para la barra principal

        # Configuración de tamaño y fuente basada en escala
        current_scale = main_window.config.get('disk_scale', 100)
        base_height = 35
        base_font = 18
        scale_factor = current_scale / 100.0
        current_height = int(base_height * scale_factor)
        current_font = int(base_font * scale_factor)

        # Modo de visualización
        saved_mode = main_window.config.get('size_mode', 'Normal')
        size_modes = {
            "Normal": {"scale": 100, "height": 40, "font": 12, "icon": 30, "progress": 18, "file": 4, "file_offset": 28},
            "Grande": {"scale": 100, "height": 50, "font": 13, "icon": 35, "progress": 18, "file": 4, "file_offset": 40}
        }
        mode = size_modes.get(saved_mode, size_modes["Normal"])

        # Dimensiones de las barras
        self.progress_height = int(mode["progress"])
        self.file_progress_height = int(mode["file"])
        self.file_width_percentage = 0.1
        self.progress_width_percentage = 0.2

        # Configuración de la interfaz
        bold_font = QFont()
        bold_font.setBold(True)
        bold_font.setPointSize(current_font)
        style = f"color: white; font-size: {current_font}px;"

        layout = QHBoxLayout()
        layout.setSpacing(0)
        layout.setContentsMargins(0, 2, 12, 8)

        self.volume_label = QLabel(volume)
        self.volume_label.setStyleSheet(style)
        self.volume_label.setFont(bold_font)

        if free_space_gb >= 1024:
            free_space_tb = free_space_gb / 1024
            self.size_label = QLabel(f"{free_space_tb:.2f} TB")
        else:
            self.size_label = QLabel(f"{free_space_gb:.2f} GB")
        self.size_label.setStyleSheet(style)
        self.size_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

        self.speed_label = QLabel(" ")
        self.speed_label.setStyleSheet(style)
        self.speed_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        
        self.eject_icon_button = QToolButton()
        icono_expulsar, _ = icono_expulsar_usb(20)  # Tamaño 25 para coincidir con iconSize
        self.eject_icon_button.setIcon(icono_expulsar)
        self.eject_icon_button.setIconSize(QSize(20, 20)) # Ensure icon size is set
        self.eject_icon_button.setStyleSheet("QToolButton { border: none; background-color: transparent; }") # Remove button border/background
        self.eject_icon_button.hide()

        layout.addWidget(self.volume_label)
        layout.addStretch()
        layout.addWidget(self.size_label)
        layout.addWidget(self.speed_label)
        layout.addWidget(self.eject_icon_button, alignment=Qt.AlignmentFlag.AlignRight)
        self.setLayout(layout)

        from BARRA_TOTAL import BarraTotal

        self.progress_bar = BarraTotal(self)
        self.progress_bar.setBaseColor(self.main_window.progress_bar_color)
        self.progress_bar.setValue(0)
        self.progress_bar.setFixedHeight(self.progress_height)

        self.file_progress_bar = BarraTotal(self)
        self.file_progress_bar.setBaseColor(self.main_window.progress_bar_color)
        self.file_progress_bar.setBorderRadius(4)
        self.file_progress_bar.setValue(0)
        self.file_progress_bar.setFixedHeight(self.file_progress_height)
        self.file_progress_bar.setShowPercentage(False)

        self.resizeEvent(None)

    def paintEvent(self, event):
        """Dibuja fondos semitransparentes individuales para cada barra de progreso"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Recalcular las dimensiones correctas basadas en los porcentajes fijos
        width = self.width()
        progress_width = int(width * self.progress_width_percentage)
        file_width = int(width * self.file_width_percentage)
        
        # Calcular posiciones correctas
        saved_mode = self.main_window.config.get('size_mode', 'Normal')
        size_modes = {
            "Normal": {"scale": 100, "height": 40, "font": 12, "icon": 30, "progress": 18, "file": 4, "file_offset": 28},
            "Grande": {"scale": 100, "height": 50, "font": 13, "icon": 35, "progress": 18, "file": 4, "file_offset": 40}
        }
        mode = size_modes.get(saved_mode, size_modes["Normal"])
        
        volume_width = self.volume_label.sizeHint().width() + 10
        progress_x = max((width - progress_width) // 2, volume_width + 10)
        progress_y = mode["file_offset"] - self.progress_height - 1
        
        # Centrar la barra de archivos con respecto a la barra de progreso principal
        file_x = progress_x + (progress_width - file_width) // 2
        file_y = mode["file_offset"]
        
        # Crear rectángulos con las dimensiones correctas
        bg_rect_main = QRect(progress_x, progress_y, progress_width, self.progress_height)
        bg_rect_file = QRect(file_x, file_y, file_width, self.file_progress_height)
        
        # Fondo para la barra principal
        path_main = QPainterPath()
        path_main.addRoundedRect(QRectF(bg_rect_main), self.border_radius, self.border_radius)
        painter.fillPath(path_main, self.background_color)
        
        # Línea de borde más fina para la barra principal
        pen = QPen(QColor(255, 255, 255, 30))
        pen.setWidthF(0.5)  # Reducido de 1 a 0.5 para una línea más fina
        painter.setPen(pen)
        painter.drawPath(path_main)

        # Fondo para la barra de archivos
        path_file = QPainterPath()
        path_file.addRoundedRect(QRectF(bg_rect_file), 4, 4)  # Radio más pequeño
        painter.fillPath(path_file, self.background_color)
        
        # Línea de borde más fina para la barra de archivos
        pen = QPen(QColor(255, 255, 255, 30))
        pen.setWidthF(0.5)  # Reducido de 1 a 0.5 para una línea más fina
        painter.setPen(pen)
        painter.drawPath(path_file)
        
        # Asegurar que las barras de progreso estén alineadas con los fondos
        if self.progress_bar.geometry() != bg_rect_main:
            self.progress_bar.setGeometry(bg_rect_main)
        if self.file_progress_bar.geometry() != bg_rect_file:
            self.file_progress_bar.setGeometry(bg_rect_file)
        super().paintEvent(event)

    def resizeEvent(self, event):
        """Ajusta posiciones y rectángulos de los fondos"""
        super().resizeEvent(event) if event else None
        width = self.width()
        saved_mode = self.main_window.config.get('size_mode', 'Normal')
        size_modes = {
            "Normal": {"scale": 100, "height": 40, "font": 12, "icon": 30, "progress": 18, "file": 4, "file_offset": 28},
            "Grande": {"scale": 100, "height": 50, "font": 13, "icon": 35, "progress": 18, "file": 4, "file_offset": 40}
        }
        mode = size_modes.get(saved_mode, size_modes["Normal"])

        # Posiciones y tamaños - asegurar que no se superpongan con el texto
        progress_width = int(width * self.progress_width_percentage)
        
        # Calcular el ancho del texto del volumen para evitar superposición
        volume_width = self.volume_label.sizeHint().width() + 10  # Añadir margen
        
        # Asegurar que la barra de progreso no se superponga con el nombre del volumen
        progress_x = max((width - progress_width) // 2, volume_width + 10)
        progress_y = mode["file_offset"] - self.progress_height - 1

        file_width = int(width * self.file_width_percentage)
        
        # Centrar la barra de archivos con respecto a la barra de progreso principal
        file_x = progress_x + (progress_width - file_width) // 2
        file_y = mode["file_offset"]

        # Posicionar barras
        self.progress_bar.setGeometry(progress_x, progress_y, progress_width, self.progress_height)
        self.file_progress_bar.setGeometry(file_x, file_y, file_width, self.file_progress_height)

        # Guardar rectángulos individuales para los fondos
        self.bg_rect_main = QRect(progress_x, progress_y, progress_width, self.progress_height)
        self.bg_rect_file = QRect(file_x, file_y, file_width, self.file_progress_height)

        # Forzar actualización
        self.progress_bar.update()
        self.file_progress_bar.update()
        self.update()  # Actualizar todo el widget

    def update_progress_bar_color(self, color):
        """Actualiza el color de ambas barras de progreso"""
        self.progress_bar.setBaseColor(color)
        self.file_progress_bar.setBaseColor(color)
        self.resizeEvent(None)

def get_drive_space(drive):
    free_bytes = ctypes.c_ulonglong(0)
    total_bytes = ctypes.c_ulonglong(0)
    total_free_bytes = ctypes.c_ulonglong(0)
    ctypes.windll.kernel32.GetDiskFreeSpaceExW(ctypes.c_wchar_p(drive), ctypes.byref(free_bytes), ctypes.byref(total_bytes), ctypes.byref(total_free_bytes))
    return free_bytes.value, total_bytes.value
class FreeSpaceWorkerSignals(QObject):
    free_space_updated = pyqtSignal(str, str)

class FreeSpaceTask(QRunnable):
    def __init__(self, drive_letter):
        super().__init__()
        self.drive_letter = drive_letter
        self.signals = FreeSpaceWorkerSignals()
    def run(self):
        try:
            free_space, total_space = get_drive_space(self.drive_letter)
            free_space_gb = free_space / (1024 ** 3)
            if free_space_gb >= 1024:
                free_space_tb = free_space_gb / 1024
                size_str = f"{free_space_tb:.2f} TB"
            else:
                size_str = f"{free_space_gb:.2f} GB"
            self.signals.free_space_updated.emit(self.drive_letter, size_str)
        except Exception as e:
            print(f"Error in FreeSpaceTask for drive {self.drive_letter}: {e}")

def get_drive_letter(path):
    return path[:2] if path and len(path) >= 2 and path[1] == ':' else None

class GlobalEventFilter(QObject):
    f2_pressed = pyqtSignal()
    delete_pressed = pyqtSignal()
    f3_pressed = pyqtSignal()  # Nueva señal para F3
    f4_pressed = pyqtSignal()  # Señal para F4
    
    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
    
    def eventFilter(self, obj, event):
        if event.type() == QEvent.Type.KeyPress:
            if event.key() == Qt.Key.Key_F2:
                self.f2_pressed.emit()
                return True
            elif event.key() == Qt.Key.Key_Delete:
                self.delete_pressed.emit()
                return True
            elif event.key() == Qt.Key.Key_F3:
                self.f3_pressed.emit()
                return True
            elif event.key() == Qt.Key.Key_F4:
                self.f4_pressed.emit()
                return True
            # Añadir manejo de tecla Backspace aquí
            elif (event.key() == Qt.Key.Key_Backspace and 
                self.main_window and 
                hasattr(self.main_window, 'file_tree_widget') and 
                self.main_window.file_tree_widget.isVisible() and
                # Verificar si hay un diálogo de renombrado activo
                not (hasattr(self.main_window, 'rename_dialog') and 
                    self.main_window.rename_dialog.isVisible())):
                self.main_window.go_back()
                return True
        return super().eventFilter(obj, event)

class MainWindow(QMainWindow):
    update_file_progress_signal = pyqtSignal(str, int)
    update_speed_signal = pyqtSignal()
    update_progress_signal = pyqtSignal(str, int)
    update_queue_progress_signal = pyqtSignal(int)  # Nueva señal para actualizar la barra de progreso de la cola
    update_total_size_signal = pyqtSignal(str, int)
    eject_drive_signal = pyqtSignal(str)
    file_copied_signal = pyqtSignal(str)
    copy_finished_signal = pyqtSignal(str)
    def __init__(self, parent=None):
        super().__init__(parent)
        import ctypes
        myappid = 'ZETACOPY.ZETACOPY.1.0'  # Formato arbitrario
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
        import os
        from PyQt6.QtGui import QIcon
        icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'ZETACOPY.ico')
        app_icon = QIcon(icon_path)
        QApplication.instance().setWindowIcon(app_icon)
        self.setWindowIcon(app_icon)
        self.worker = Worker()  
        self.worker.start()
        
        from EXPLORADOR import ExplorerLogic
        self.explorer_logic = ExplorerLogic(self)
        self.show_drive_contents = self.explorer_logic.show_drive_contents
        
        self.progress_bar_color = "#0078d7"
        self.global_event_filter = GlobalEventFilter(self)  # Pasa self como main_window
        QApplication.instance().installEventFilter(self.global_event_filter)
        self.installEventFilter(self.global_event_filter)  # Instalar en la ventana principal también
        self.global_event_filter.f2_pressed.connect(self.show_rename_dialog)
        self.global_event_filter.f3_pressed.connect(self.edit_temporary_name)
        self.global_event_filter.delete_pressed.connect(self.handle_delete)
        self.global_event_filter.f4_pressed.connect(self.map_port)
        self.check_license()
        self.paint_mutex = QMutex()
        self.disks_to_eject_after_copy = {}
        self.setGeometry(700, 100, 600, 600)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)  # Sin bordes
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)  # Hacer el fondo de la ventana transparente
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd)
        central_widget = QWidget(self)
        self.setCentralWidget(central_widget)
        self.main_layout = QVBoxLayout(central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        self.top_widget = QWidget(self) # Top widget
        self.top_widget.setFixedHeight(80)  # Aumentamos la altura para acomodar el botón REPARAR
        self.main_layout.addWidget(self.top_widget, alignment=Qt.AlignmentFlag.AlignTop)
        self.volume_name_label = QLabel(self)
        self.volume_name_label.setStyleSheet("color: white; font-size: 12px; margin-top: 5px;")
        self.volume_name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.volume_name_label.hide()  # Inicialmente oculto
        top_layout = QVBoxLayout(self.top_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)
        top_layout.setSpacing(0)
        background_widget = QWidget(self.top_widget)
        background_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 40);
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 30);
                margin: 3px;
            }
        """)
        background_widget.setAutoFillBackground(True)
        background_widget.lower()  # Mover al fondo
        def resizeEvent(event):
            background_widget.setGeometry(2, 2, self.top_widget.width() - 4, self.top_widget.height() - 0)
        self.top_widget.resizeEvent = resizeEvent
        self.botones = Botones(self)
        self.botones.mostrar_signal.connect(self.actualizar_vista_discos)
        self.botones.cola_signal.connect(self.ventana_cola)
        self.CLOSE = self.botones.CLOSE
        self.MAXIMIZAR = self.botones.MAXIMIZAR
        self.MINIMIZAR = self.botones.MINIMIZAR
        self.MINIMIZAR = self.botones.MINIMIZAR
        self.REPARAR = self.botones.REPARAR
        self.MOSTRAR = self.botones.MOSTRAR
        self.REINDEXAR = self.botones.REINDEXAR

        upper_row = QHBoxLayout() # Fila superior con los elementos existentes
        upper_row.setContentsMargins(15, 5, 5, 5)  # Aumentar margen izquierdo de 5 a 15
        upper_row.setSpacing(8)  # Aumentar espaciado entre elementos de 5 a 8
        app_version = "1.0.5(e)"

        icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'ZETACOPY.ico')
        icon_pixmap = QPixmap(icon_path)
        icon_pixmap = icon_pixmap.scaled(25, 25, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        icon_label = QLabel(self)
        icon_label.setFixedSize(25, 25)  # Fijamos el tamaño del label
        icon_label.setPixmap(icon_pixmap)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # Centramos el icono en el label
        
        # Agregar efecto de sombra al icono
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(8)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 160))
        icon_label.setGraphicsEffect(shadow)
        
        self.text_label = QLabel(f"Zetacopy v{app_version}", self)
        self.text_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        self.separator_label = QLabel("| ", self)
        self.separator_label.setStyleSheet("color: white; font-size: 14px; font-weight: bold;")
        self.date_time_label = QLabel(self)
        self.date_time_label.setStyleSheet("color: white; font-size: 14px; font-weight: bold;")
        self.date_time_label.setMouseTracking(True)
        clock_icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'HORA.png')
        clock_icon_pixmap = QPixmap(clock_icon_path).scaled(16, 16, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        self.clock_icon_label = QLabel(self)
        self.clock_icon_label.setPixmap(clock_icon_pixmap)
        window_controls = QHBoxLayout()
        window_controls.setSpacing(0)  # Espaciado mínimo entre los botones de control
        window_controls.setContentsMargins(0, 0, 0, 0)
        window_controls.addWidget(self.MINIMIZAR)
        window_controls.addWidget(self.MAXIMIZAR)
        window_controls.addWidget(self.CLOSE)
        upper_row.addWidget(icon_label, alignment=Qt.AlignmentFlag.AlignLeft)
        upper_row.addWidget(self.text_label, alignment=Qt.AlignmentFlag.AlignLeft)
        upper_row.addWidget(self.separator_label, alignment=Qt.AlignmentFlag.AlignLeft)
        upper_row.addWidget(self.clock_icon_label, alignment=Qt.AlignmentFlag.AlignLeft)  # Primero el icono
        upper_row.addWidget(self.date_time_label, alignment=Qt.AlignmentFlag.AlignLeft)   # Luego la hora
        self.ram_widget = RAMUsageWidget()
        self.ram_widget.setFixedHeight(32)  # Mismo alto que los otros elementos
        upper_row.addWidget(self.ram_widget, alignment=Qt.AlignmentFlag.AlignLeft)
        upper_row.addStretch()
        upper_row.addLayout(window_controls)  # Layout de controles con espaciado mínimo
        top_layout.addLayout(upper_row)
        lower_row = QHBoxLayout() # Fila inferior con el botón REPARAR
        lower_row.setContentsMargins(0, 0, 0, 0)
        
        # Agregar stretch al inicio para empujar los botones al centro
        lower_row.addStretch()
        
        # Agregar los botones al dock superior en el orden correcto
        lower_row.addWidget(self.REPARAR)
        lower_row.addWidget(self.MOSTRAR)
        lower_row.addWidget(self.REINDEXAR)
        lower_row.addWidget(self.botones.FILTER)  # Añadir el botón de filtro aquí, después de REINDEXAR
        
        # Agregar stretch al final para mantener los botones centrados
        lower_row.addStretch()
        top_layout.addLayout(lower_row)
        self.timer = QTimer(self) # Configurar el temporizador para actualizar la fecha y hora
        self.timer.timeout.connect(self.update_date_time)
        self.timer.start(1000)  # Actualizar cada segundo
        self.update_date_time() # Actualizar la fecha y hora inmediatamente
        self.nav_widget = QWidget()
        self.nav_widget.setFixedHeight(40)
        self.back_new_folder_layout = QHBoxLayout(self.nav_widget)
        self.back_new_folder_layout.setContentsMargins(0, 0, 0, 0)
        self.back_new_folder_layout.setSpacing(5)
        self.back_button = self.botones.BACK
        self.new_folder = self.botones.NEW_FOLDER
        self.new_files = self.botones.NEW_FILES 
        self.new_empaketado = self.botones.NEW_EMPAKETADO
        self.cola_button = self.botones.COLA
        self.back_new_folder_layout.addWidget(self.back_button)
        self.back_new_folder_layout.addWidget(self.new_folder)
        self.back_new_folder_layout.addWidget(self.new_files)
        self.back_new_folder_layout.addWidget(self.new_empaketado)
        self.back_new_folder_layout.addWidget(self.botones.CLEAR)  # Agregar CLEAR después de empaketado
        self.back_new_folder_layout.addWidget(self.botones.RENAME)  # Agregar RENAME después de CLEAR
        self.back_new_folder_layout.addWidget(self.cola_button)
        self.back_new_folder_layout.addStretch()
        self.main_layout.addWidget(self.nav_widget)
        self.nav_widget.hide()
        self.back_button.hide()
        self.new_folder.hide()
        self.new_files.hide()
        self.cola_button.hide()
        self.new_empaketado.hide()
        self.size_info_label = QLabel(self)
        self.size_info_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: bold;  /* Añadido para texto en negrita */
                padding: 5px 10px;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 13px;
                margin: 2px;
            }
        """)
        self.size_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.size_info_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)  # Ajustar al contenido
        self.size_info_label.hide()
        self.main_layout.addWidget(self.size_info_label, alignment=Qt.AlignmentFlag.AlignHCenter)  # Centrar horizontalmente
        from Barras_Seleccion import BarraSeleccionDiscos
        self.list_widget = DraggableListWidget(self)
        self.list_widget = BarraSeleccionDiscos.configurar_lista_discos(self.list_widget)
        list_layout = QVBoxLayout(self.list_widget)
        list_layout.setContentsMargins(0, 0, 0, 0)
        list_layout.setSpacing(0)
        spacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        list_layout.addItem(spacer)
        self.queue_progress_bar = DragProgressBar(self)
        self.queue_progress_bar.setFixedHeight(22)
        self.queue_progress_bar.setFixedWidth(200)
        self.queue_progress_bar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.queue_progress_bar.setVisible(False)  # Asegurarnos que esté oculta al inicio
        list_layout.addWidget(self.queue_progress_bar, alignment=Qt.AlignmentFlag.AlignCenter)
        self.botones.setFixedHeight(50)
        self.main_layout.addWidget(self.list_widget)
        self.main_layout.addWidget(self.volume_name_label)
        self.main_layout.addWidget(self.botones, alignment=Qt.AlignmentFlag.AlignBottom)
        self.list_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.worker = Worker()
        self.worker.update_volumes.connect(self.update_volume_list)
        self.worker.disk_status_changed.connect(self.on_disk_status_changed)
        self.worker.start()
        self.file_queue = queue.Queue()
        self.files_in_queue = set()
        self.files_in_queue_by_drive = {}
        self.directory_queue = queue.Queue()
        self.total_size = 0
        self.total_sizes = {}
        self.copy_completed = {}
        self.queue_windows = {}
        self.queues = {}
        self.threads = {}
        self.eject_drive_signal.connect(self.expulsar_disco_automatico)
        self.config = load_config()
        self.pause_times = {}
        self.copy_speeds = {}
        self.copy_queue_active = {}
        self.final_space_text = {}
        self.queue_locks = {}
        self.update_threads = {}
        self.disks_space_final = {}
        self.queue_lock = QMutex()
        self.space_after_copy = 0
        self.thread_pool = QThreadPool()
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_all_free_spaces)
        self.timer.start(5000)
        self.processing_directories = set()
        self.copy_start_time = {}
        self.copy_threads = {}
        self.interrupted_files = {}
        self.mini_progress_bars = {}
        self.file_to_retry = {} 
        self.copy_paused_on_error = {} 
        self.clear_hidden_drives()
        self.disconnected_copying_disks = {}
        self.copy_bytes_copied = {}
        self.copy_paused_on_error = {}
        self.speed_update_timer = QTimer(self)
        self.speed_update_timer.timeout.connect(self.update_speed_display)
        self.speed_update_timer.start(100)
        self.size_grip = QSizeGrip(self)
        self.size_grip.setParent(self)
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        self.update_speed_signal.connect(self.update_speed_display)
        self.update_progress_signal.connect(self.update_progress_bar)
        self.update_file_progress_signal.connect(self.update_file_progress_bar)
        self.botones.NEW_EMPAKETADO.clicked.connect(self.create_empaketado)
        self.update_queue_progress_signal.connect(self.update_queue_progress_bar)  # Conectar la señal a la función de actualización
        self.copy_data_lock = Lock()
        self.update_timer = QTimer(self)
        QTimer.singleShot(500, self.check_pending_copies)
        self.update_timer.start(2000)  # Actualizar cada 2 segundos
        self.top_widget.installEventFilter(self)
        self.transparent_widget = QWidget(self)# Crear un widget transparente que cubra toda la ventana
        #self.transparent_widget.setStyleSheet("background-color: rgba(128, 128, 128, 0.4);")  GRIS TRANSPARENTE
        self.transparent_widget.setStyleSheet("background-color: rgba(0, 0, 0, 0.2);")  # Negro semitransparente
        self.transparent_widget.setGeometry(self.rect())
        self.transparent_widget.lower()  # Moverlo al fondo
        self.indexed_data = self.load_indexed_data()
        print(f"Datos indexados cargados: {self.indexed_data.keys()}")  # Depuración
        for i in range(1, 10):  # Del 1 al 9
            shortcut = QShortcut(QKeySequence(f"Ctrl+{i}"), self)
            shortcut.activated.connect(lambda i=i: self.queue_next_n_files(i, True))
            shortcut_no_ctrl = QShortcut(QKeySequence(str(i)), self)
            shortcut_no_ctrl.activated.connect(lambda i=i: self.queue_next_n_files(i, False))
        self.shortcut_0 = QShortcut(QKeySequence("Ctrl+0"), self)
        self.shortcut_0.activated.connect(lambda: self.queue_next_n_files(0, True))
        self.shortcut_0_no_ctrl = QShortcut(QKeySequence("0"), self)
        self.shortcut_0_no_ctrl.activated.connect(lambda: self.queue_next_n_files(0, False))
        config = load_config()
        progress_bar_color = config.get("progress_bar_color", "#0078d7")
        self.update_progress_bar_color(progress_bar_color)
        self.config_window = TransparentWindow(self)
        self.load_config_and_apply_mode()
        self.copy_finished_signal.connect(self.announce_copy_completion)
        self.current_copying_file = {}
        self.current_explorer_drive = None
        self.show_disk_lines = True
        self.setup_shortcut()
        self.apply_disk_lines_config()
        self.botones.reparar_signal.connect(self.iniciar_reparacion_discos)
        self.original_icons = {}  # Para almacenar los iconos originales
        self.disk_space_before_repair = {}
        self.discos_exentos = set()  # Inicializar como conjunto vacío
        self._last_price_check = {}  # Para controlar frecuencia de cálculos
        self.precios = self.cargar_precios()
        self.load_exempt_disks()
        self.discos_exentos = self.cargar_discos_exentos()
        self.window_resizer = WindowResizer(self) # Crear los size grips en las cuatro esquinas
        self.icon_manager = FileTypeIconManager()

        # Precargar la ventana de ajustes en segundo plano
        self.settings_window = None
        QTimer.singleShot(500, self.preload_settings_window)

        current_style = self.config.get('disk_style', 'Estilo 1')
        sizes = {
            "Estilo 1": {"height": 35, "font": 18},  # Tamaño original de la app
            "Estilo 2": {"height": 36, "font": 19},  # Ligeramente más grande
            "Estilo 3": {"height": 37, "font": 20},  # Un poco más grande
            "Estilo 4": {"height": 38, "font": 21}   # El más grande
        }
        config = sizes.get(current_style, sizes["Estilo 1"])
        self.update_disk_item_size(config["height"], config["font"])
        saved_scale = self.config.get('disk_scale', 100) # Aplicar el escalado guardado a los discos
        base_height = 35  # Altura base original
        base_font = 18    # Tamaño de fuente base original
        scale_factor = saved_scale / 100.0
        new_height = int(base_height * scale_factor)
        new_font = int(base_font * scale_factor)
        QTimer.singleShot(100, lambda: self.update_disk_item_size(new_height, new_font)) # Aplicar el escalado después de un breve retraso
        self.index_copy_handler = IndexCopyHandler(self)
        self.txt_creator = TxtFileCreator(self)
        self._pending_updates = {}
        self._update_workers = {}
        self.space_monitor = None  # Inicializar como None
        self.load_hidden_drives()
        # Inicializar TxtFileCreator
        self.txt_creator = TxtFileCreator(self)
        
        # Desconectar cualquier conexión existente para evitar duplicados
        try:
            self.txt_creator.txt_created.disconnect()
        except:
            pass
        
        # Conectar solo una vez la señal de archivo TXT creado
        self.txt_creator.txt_created.connect(self.add_new_txt_to_view)
        # No conectar a update_ui_with_new_file para evitar duplicados
    def show_tooltip(self, message, duration=3000):
        showTooltipAtWidget(message, self, duration)
    def announce_copy_completion(self, drive_letter):
        """Anuncia por voz que la copia ha finalizado para un disco específico"""
        try:
            # Añadir un control para evitar anuncios duplicados
            if not hasattr(self, 'announced_completions'):
                self.announced_completions = set()
            
            # Si ya se anunció para este disco recientemente, no repetir
            if drive_letter in self.announced_completions:
                print(f"Anuncio para {drive_letter} omitido (ya anunciado recientemente)")
                return
            
            # Marcar como anunciado
            self.announced_completions.add(drive_letter)
            
            # Programar la limpieza del registro después de un tiempo
            QTimer.singleShot(5000, lambda: self.announced_completions.discard(drive_letter))
            
            # Verificar si los anuncios de voz están activados en la configuración
            config = load_config()
            voice_enabled = config.get('voice_enabled', True)
            print(f"Estado de voz en configuración: {voice_enabled}")
            
            if not voice_enabled:
                print("Anuncios de voz desactivados, reproduciendo sonido de finalización")
                try:
                    from SONIDOS import sound_manager
                    sound_manager.play_sound('finish')
                    print("Sonido de finalización reproducido")
                    return
                except Exception as e:
                    print(f"Error reproduciendo sonido de finalización: {e}")
                    return

            # Obtener el nombre del volumen
            volume_name = "Disco"
            try:
                volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                if not volume_name:
                    volume_name = "Disco"
            except Exception as e:
                print(f"Error al obtener nombre del volumen: {e}")
            
            mensaje = f"{volume_name} ha finalizado la copia"
            print(f"Anunciando con PowerShell: {mensaje}")
            
            # Usar subprocess directamente sin QThread ni QBasicTimer
            import subprocess
            import threading
            
            def ejecutar_comando_voz():
                try:
                    # Comando PowerShell para reproducir voz
                    ps_command = f'Add-Type -AssemblyName System.Speech; $speak = New-Object System.Speech.Synthesis.SpeechSynthesizer; $speak.Rate = 0; $speak.Volume = 100; $speak.Speak("{mensaje}");'
                    
                    # Configurar para ocultar la ventana
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    
                    # Ejecutar el comando
                    subprocess.run(
                        ['powershell', '-Command', ps_command],
                        startupinfo=startupinfo,
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    print("Anuncio de voz completado correctamente")
                except Exception as e:
                    print(f"Error ejecutando comando de voz: {e}")
            
            # Usar threading estándar de Python en lugar de QThread
            t = threading.Thread(target=ejecutar_comando_voz)
            t.daemon = True  # Para que no bloquee al cerrar la aplicación
            t.start()
            print("Hilo de anuncio de voz iniciado")
            
        except Exception as e:
            print(f"Error general en announce_copy_completion: {e}")
            import traceback
            traceback.print_exc()
    
    @QtCore.pyqtSlot(str)
    def add_new_txt_to_view(self, txt_file_name):
        try:
            # Verificar si el archivo ya está en la vista
            for i in range(self.file_tree_widget.topLevelItemCount()):
                item = self.file_tree_widget.topLevelItem(i)
                if item.text(0) == txt_file_name:
                    print(f"El archivo {txt_file_name} ya está en la vista, no se agregará de nuevo")
                    return
            
            # Crear nuevo item
            new_item = QTreeWidgetItem([txt_file_name])
            
            # Configurar icono
            from CREAR import registro
            new_item.setIcon(0, registro(size=24))
            
            # Obtener y establecer el tamaño del archivo
            file_path = os.path.join(self.current_drive, txt_file_name)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                new_item.setText(1, self.format_size(size))
                # Establecer alineación a la derecha para la columna de tamaño
                new_item.setTextAlignment(1, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            
            # Configurar fuente en negrita
            font = new_item.font(0)
            font.setBold(True)
            new_item.setFont(0, font)
            new_item.setFont(1, font)
            
            # Hacer editable
            new_item.setFlags(new_item.flags() | Qt.ItemFlag.ItemIsEditable)
            
            # Añadir al árbol
            self.file_tree_widget.addTopLevelItem(new_item)
            
            # Seleccionar y hacer visible el nuevo item
            self.file_tree_widget.setCurrentItem(new_item)
            self.file_tree_widget.scrollToItem(new_item)
            
            print(f"Archivo TXT añadido a la vista: {txt_file_name}")
            
        except Exception as e:
            print(f"Error al añadir nuevo TXT a la vista: {e}")
    
    
    
    def check_pending_copies(self):
        """Muestra notificaciones en discos con copias pendientes"""
        try:
            pending_file = os.path.join(os.path.dirname(__file__), 'pending_copies.json')
            if os.path.exists(pending_file):
                with open(pending_file, 'r', encoding='utf-8') as f:
                    try:
                        pending_drives = json.load(f)
                    except json.JSONDecodeError:
                        print(f"Error: El archivo {pending_file} no es un JSON válido.")
                        return
                if not isinstance(pending_drives, dict):
                    print(f"Error: El contenido de {pending_file} no es un diccionario JSON.")
                    return
                for drive_key in pending_drives:
                    drive_data = pending_drives[drive_key]

                    # Verificación de tipo para evitar errores con valores no diccionario
                    if not isinstance(drive_data, dict):
                        print(f"Advertencia: Omitiendo clave '{drive_key}' porque su valor no es un diccionario (tipo: {type(drive_data)}).")
                        continue
                    drive_letter = None
                    if 'drive_letter' in drive_data:
                        drive_letter = drive_data['drive_letter']
                    else:
                        for char in drive_key:
                            if char + ":" in drive_key:
                                drive_letter = char
                                break
                    if not drive_letter:
                        print(f"No se pudo determinar la letra del disco para {drive_key}")
                        continue
                    if os.path.exists(f"{drive_letter}\\"):
                        queue = drive_data.get('queue', [])
                        files_count = len(queue)
                        if files_count > 0:
                            from NOTIFICACION import add_badge_to_disk
                            add_badge_to_disk(self, drive_letter, files_count)
                            print(f"Notificación añadida a {drive_letter}: {files_count} archivos")
        except Exception as e:
            print(f"Error al verificar copias pendientes: {e}")
            import traceback
            traceback.print_exc()
    
    def load_hidden_drives(self):
        """Carga la lista de discos ocultos desde config.json"""
        try:
            config = load_config()
            self.discos_ocultos = set(config.get('discos_ocultos', []))
            self.apply_hidden_drives()
        except Exception as e:
            print(f"Error cargando discos ocultos: {e}")
            self.discos_ocultos = set()
    
    def apply_hidden_drives(self):
        """Aplica el filtro de discos ocultos"""
        try:
            config = load_config()
            discos_ocultos = config.get('discos_ocultos', [])
            items_to_hide = []
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if item:
                    widget = self.list_widget.itemWidget(item)
                    if widget:
                        for label in widget.findChildren(QLabel):
                            if any(disco in label.text() for disco in discos_ocultos):
                                items_to_hide.append(item)
                                break
            for item in items_to_hide:
                item.setHidden(True)
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if item and item not in items_to_hide:
                    item.setHidden(False)
            self.list_widget.update()
            print(f"Vista actualizada - discos ocultos: {discos_ocultos}")
        except Exception as e:
            print(f"Error aplicando filtro de discos ocultos: {e}")
    
    def update_hidden_drives(self, discos_ocultos):
        """Actualiza la lista de discos ocultos y aplica los cambios"""
        self.discos_ocultos = set(discos_ocultos)
        self.apply_hidden_drives()
    
    def clear_hidden_drives(self):
        """Limpia el archivo hidden_drives.json al inicio de la aplicación"""
        try:
            if getattr(sys, 'frozen', False):
                exe_dir = os.path.dirname(sys.executable)
            else:
                exe_dir = os.path.dirname(os.path.abspath(__file__))
            hidden_drives_path = os.path.join(exe_dir, 'hidden_drives.json')
            with open(hidden_drives_path, 'w') as f:
                json.dump([], f)
            print(f"Archivo hidden_drives.json limpiado en: {hidden_drives_path}")
        except Exception as e:
            print(f"Error limpiando hidden_drives.json: {e}")
            import traceback
            traceback.print_exc()
    
    def load_exempt_disks(self):
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    if 'discos_exentos' in config:
                        self.discos_exentos = set(config['discos_exentos'])
                    else:
                        self.discos_exentos = set()
            else:
                self.discos_exentos = set()
        except Exception as e:
            print(f"Error al cargar discos exentos: {e}")
            self.discos_exentos = set()  # Asegurar que siempre sea un conjunto

    def cargar_discos_exentos(self):
        """Carga la lista de discos exentos desde el archivo"""
        try:
            project_dir = os.path.dirname(os.path.abspath(__file__))
            exentos_path = os.path.join(project_dir, "DISCOS_EXENTOS.txt")
            
            # Inicializar como conjunto vacío por defecto
            self.discos_exentos = set()
            if os.path.exists(exentos_path):
                with open(exentos_path, "r", encoding='utf-8') as f:
                    try:
                        data = json.load(f)
                        if isinstance(data, list):
                            self.discos_exentos = set(data)
                    except json.JSONDecodeError:
                        print("El archivo de discos exentos está mal formateado.")
                    except Exception as e:
                        print(f"Error al cargar discos exentos: {e}")
            print(f"Discos exentos cargados: {self.discos_exentos}")
            return self.discos_exentos
        except FileNotFoundError:
            print("No se encontró el archivo de discos exentos.")
            self.discos_exentos = set()  # Asegurar que sea un conjunto vacío
            return self.discos_exentos
        except Exception as e:
            print(f"Error al cargar discos exentos: {e}")
            self.discos_exentos = set()  # Asegurar que sea un conjunto vacío
            return self.discos_exentos
    
    def get_video_duration(self, file_path):
        """Obtiene la duración del video usando ffprobe"""
        try:
            print(f"\n=== Iniciando obtención de duración para: {file_path} ===")
            
            # Determinar la ruta base
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            print(f"Ruta base detectada: {base_path}")
            
            # Verificar la ruta de ffprobe
            ffprobe_path = os.path.join(base_path, 'ffprobe.exe')
            print(f"Buscando ffprobe.exe en: {ffprobe_path}")
            if not os.path.exists(ffprobe_path):
                print(f"ERROR: ffprobe.exe no encontrado en {ffprobe_path}")
                return None
            else:
                print("ffprobe.exe encontrado correctamente")
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            command = [
                ffprobe_path,
                '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                file_path
            ]
            print(f"Ejecutando comando: {' '.join(command)}")
            try:
                result = subprocess.run(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    startupinfo=startupinfo,
                    timeout=10
                )
                print(f"Código de salida: {result.returncode}")
                print(f"Salida estándar: {result.stdout}")
                print(f"Salida de error: {result.stderr}")
                if result.returncode == 0:
                    try:
                        duration_seconds = float(result.stdout.strip())
                        duration_minutes = duration_seconds / 60
                        print(f"Duración obtenida: {duration_minutes:.2f} minutos")
                        return duration_minutes
                    except ValueError as e:
                        print(f"Error convirtiendo duración a número: {e}")
                        return None
                else:
                    print(f"Error ejecutando ffprobe: {result.stderr}")
                    return None
            except subprocess.TimeoutExpired:
                print("ERROR: Timeout al ejecutar ffprobe")
                return None
            except Exception as e:
                print(f"Error ejecutando ffprobe: {e}")
                return None
        except Exception as e:
            print(f"Error general en get_video_duration: {e}")
            traceback.print_exc()
            return None
    
    def calcular_y_mostrar_precio(self, drive_letter, total_size_gb=None, duracion_min=None, total_files=None):
        """Calcula y muestra el precio basado en GB, duración o cantidad de ficheros según el modo configurado"""
        try:
            # Verificar modo de pago
            config = load_config()
            modo_pago = config.get('modo_pago', 'dispositivo')
            print(f"\n=== Iniciando cálculo de precio ===")
            print(f"Modo de pago: {modo_pago}")
            print(f"Drive: {drive_letter}")
            print(f"Duración: {duracion_min} min")
            print(f"Total archivos: {total_files}")
            print(f"Tamaño: {total_size_gb} GB")
            print(f"Precios configurados: {self.precios}")
            # Definir extensiones de video permitidas
            video_extensions = {
                '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', 
                '.m4v', '.mpg', '.mpeg', '.3gp', '.ts', '.mts', '.m2ts'
            }
            # Verificar disco exento
            if not drive_letter.endswith(':'):
                drive_letter += ':'
            volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
            disco_actual = f"{volume_name} ({drive_letter})"
            
            # Verificar si el disco está exento
            for disco_exento in self.discos_exentos:
                disco_exento_norm = disco_exento.lower().replace('(', '').replace(')', '').replace(':', '').strip()
                disco_actual_norm = disco_actual.lower().replace('(', '').replace(')', '').replace(':', '').strip()
                if disco_exento_norm == disco_actual_norm:
                    print(f"Disco {disco_actual} está exento de cobro")
                    return None

            # Verificar que tenemos precios cargados
            if not hasattr(self, 'precios') or not self.precios:
                self.precios = self.cargar_precios()
                if not self.precios:
                    print("Error: No hay precios cargados")
                    return None

            # Inicializar el diccionario de precios procesados si no existe
            if not hasattr(self, 'precios_procesados'):
                self.precios_procesados = {}

            # Cálculo por duración
            if modo_pago == "duracion":
                if duracion_min is None:
                    print("Error: No se proporcionó duración")
                    return None
                if not self.precios.get("por_duracion"):
                    print("Error: No hay precios configurados por duración")
                    return None

                print(f"Calculando precio para duración total: {duracion_min} minutos")
                duracion_min = int(duracion_min)  # Redondear a minutos enteros
                
                # Ordenar los precios por duración de mayor a menor
                duraciones_ordenadas = sorted([float(k) for k in self.precios["por_duracion"].keys()], reverse=True)
                precio_total = 0
                duracion_restante = duracion_min
                print("\n=== Desglose del cálculo de precio ===")
                for duracion in duraciones_ordenadas:
                    if duracion_restante <= 0:
                        break
                    precio_base = float(self.precios['por_duracion'][str(int(duracion))])
                    # Calcular cuántas veces cabe esta duración en la duración restante
                    multiplicador = duracion_restante // duracion
                    if multiplicador > 0:
                        precio_parcial = precio_base * multiplicador
                        precio_total += precio_parcial
                        duracion_restante -= duracion * multiplicador
                        print(f"+ ${precio_parcial:.2f} ({multiplicador} bloques de {duracion} min a ${precio_base:.2f})")
                
                # Si queda duración restante, usar el precio más bajo disponible
                if duracion_restante > 0 and duraciones_ordenadas:
                    precio_minimo = float(self.precios['por_duracion'][str(int(duraciones_ordenadas[-1]))])
                    precio_adicional = precio_minimo
                    precio_total += precio_adicional
                    print(f"+ ${precio_adicional:.2f} (bloque adicional para {duracion_restante} min restantes)")
                print(f"\nPrecio total calculado: ${precio_total:.2f} para {duracion_min} minutos")
                return precio_total

            # Cálculo por ficheros
            elif modo_pago == "ficheros":
                if not self.precios.get("por_ficheros"):
                    print("Error: No hay precios configurados por ficheros")
                    return None

                # Inicializar o recuperar el precio acumulado para este drive
                precio_total = self.precios_procesados.get(drive_letter, 0)
                print(f"Precio acumulado anterior: ${precio_total}")
                
                # Imprimir información de depuración
                print(f"Total de archivos pasado como parámetro: {total_files}")
                if drive_letter in self.queues:
                    cola_size = self.queues[drive_letter].qsize()
                    print(f"Tamaño actual de la cola: {cola_size}")
                else:
                    print("No hay cola para este disco")

                # Inicializar o recuperar el conjunto de archivos procesados
                if not hasattr(self, 'archivos_procesados'):
                    self.archivos_procesados = {}
                if drive_letter not in self.archivos_procesados:
                    self.archivos_procesados[drive_letter] = set()
                if drive_letter in self.queues:
                    print("\n=== Procesando archivos en cola ===")
                    for source, _ in list(self.queues[drive_letter].queue):
                        print(f"\nVerificando archivo: {source}")
                        # Eliminar la restricción de solo procesar archivos de video
                        # extension = os.path.splitext(source)[1].lower()
                        # if extension not in video_extensions:
                        #    print(f"Archivo ignorado (no es video): {source}")
                        #    continue

                        extension = os.path.splitext(source)[1].lower()
                        video_extensions = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg', '.3gp', '.ts']
                        if extension not in video_extensions:
                            print(f"Archivo ignorado (no es video): {source}")
                            continue
                            
                        print(f"\nVerificando archivo de video para cálculo de precio: {source}")
                        # Evitar procesar el mismo archivo más de una vez
                        if source in self.archivos_procesados[drive_letter]:
                            print(f"Archivo ya contabilizado anteriormente: {source}")
                            continue
                        try:
                            # Normalizar ruta del archivo usando os.path
                            source_path = os.path.normpath(source)
                            # Obtener el nombre del archivo y su directorio padre
                            parent_dir = os.path.basename(os.path.dirname(source_path))
                            file_name = os.path.basename(source_path)
                            
                            # Buscar coincidencia con los precios configurados
                            for clave, data in self.precios["por_ficheros"].items():
                                # Verificar si es el nuevo formato (diccionario)
                                if isinstance(data, dict):
                                    disco = data.get("disco", "")
                                    directorio = data.get("directorio", "")
                                    precio = data.get("precio", 0)
                                    
                                    # Si el disco coincide con el actual
                                    if disco.upper() == os.path.splitdrive(source_path)[0].upper():
                                        print("---- DEPURACIÓN ----")
                                        print(f"Disco configurado: {disco.upper()} | Disco actual: {drive_letter.upper()}")
                                        print(f"Directorio configurado (original): {directorio}")
                                        print(f"Directorio configurado (normalizado): {directorio.replace('\\', '/').upper()}")
                                        print(f"Ruta del archivo (normalizada): {source_path.replace('\\', '/').upper()}")
                                        print("--------------------")
                                        # Si hay un directorio específico, verificar si el archivo está en ese directorio
                                        if directorio:
                                            directorio_norm = directorio.replace('\\', '/').upper()
                                            source_path_norm = source_path.replace('\\', '/').upper()
                                            # Verificar si el directorio está en la ruta del archivo
                                            if (directorio_norm in source_path_norm or 
                                                directorio_norm.replace('/', '\\').upper() in source_path_norm.replace('/', '\\')):
                                                precio_total += float(precio)
                                                self.archivos_procesados[drive_letter].add(source)
                                                print(f"✓ Coincidencia encontrada en directorio {directorio}: +${precio}")
                                                break
                                        else:
                                            # Si no hay directorio específico, aplicar el precio a cualquier archivo en el disco
                                            precio_total += float(precio)
                                            self.archivos_procesados[drive_letter].add(source)
                                            print(f"✓ Coincidencia encontrada para todo el disco {disco}: +${precio}")
                                            break
                                # Compatibilidad con formato antiguo
                                else:
                                    carpeta_norm = clave.upper().strip()
                                    precio = float(data)
                                    
                                    # Verificar si la carpeta coincide con el directorio padre o está en la ruta completa
                                    if (carpeta_norm == parent_dir.upper() or 
                                        carpeta_norm in source_path.upper()):
                                        precio_total += precio
                                        self.archivos_procesados[drive_letter].add(source)
                                        print(f"✓ Coincidencia encontrada (formato antiguo): +${precio}")
                                        break
                        except Exception as e:
                            print(f"Error procesando ruta {source}: {str(e)}")
                            continue

                if precio_total == 0:
                    print("No hay coincidencias de precios para los archivos en la cola, no se calculará precio.")
                    return None
                self.precios_procesados[drive_letter] = precio_total
                print(f"\nPrecio total acumulado: ${precio_total}")
                return precio_total if precio_total > 0 else None

            # Cálculo por GB (modo dispositivo)
            else:
                if total_size_gb is None:
                    print("Error: No se proporcionó tamaño en GB")
                    return None
                if not self.precios.get("por_gb"):
                    print("Error: No hay precios configurados por GB")
                    return None
                rangos_ordenados = sorted(self.precios["por_gb"].items(), key=lambda x: float(x[0]))
                for tamano, precio in rangos_ordenados:
                    if float(total_size_gb) <= float(tamano):
                        print(f"Precio encontrado: ${precio} para {total_size_gb} GB (rango <= {tamano})")
                        return float(precio)
                if rangos_ordenados:
                    ultimo_precio = float(rangos_ordenados[-1][1])
                    print(f"Usando precio máximo: ${ultimo_precio}")
                    return ultimo_precio
            print("No se pudo determinar un precio")
            return None
        except Exception as e:
            print(f"Error al calcular precio: {str(e)}")
            traceback.print_exc()
            return None
    
    def cargar_precios(self):
        """Carga los precios desde PRECIOS.txt"""
        try:
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            print(f"Ruta base detectada: {base_path}")
            precios_path = os.path.join(base_path, "PRECIOS.txt")
            print(f"Buscando PRECIOS.txt en: {precios_path}")

            # Verificar si existe el archivo
            if not os.path.exists(precios_path):
                print("PRECIOS.txt no encontrado")
                return {"por_gb": {}, "por_duracion": {}, "por_ficheros": {}}

            # Cargar precios desde archivo
            with open(precios_path, "r", encoding='utf-8') as f:
                try:
                    precios = json.load(f)
                    print("PRECIOS.txt cargado exitosamente")
                    print(f"Contenido raw de PRECIOS.txt: {precios}")  # Debug line

                    # Verificar y corregir estructura
                    if not isinstance(precios, dict):
                        print("Formato incorrecto en PRECIOS.txt")
                        return {"por_gb": {}, "por_duracion": {}, "por_ficheros": {}}

                    # Asegurar que existan todas las categorías
                    if "por_gb" not in precios:
                        precios["por_gb"] = {}
                    if "por_duracion" not in precios:
                        precios["por_duracion"] = {}
                    if "por_ficheros" not in precios:
                        precios["por_ficheros"] = {}

                    # Convertir todos los valores a float
                    for categoria in ["por_gb", "por_duracion"]:
                        precios[categoria] = {
                            str(k): float(v) 
                            for k, v in precios[categoria].items()
                        }

                    # Procesar valores de por_ficheros 
                    # Pueden ser floats simples (formato antiguo) o diccionarios (formato nuevo)
                    for k, v in list(precios["por_ficheros"].items()):
                        if isinstance(v, dict):
                            # Formato nuevo: asegurarse de que precio sea float
                            if "precio" in v and not isinstance(v["precio"], float):
                                v["precio"] = float(v["precio"])
                        else:
                            # Formato antiguo: convertir a float
                            precios["por_ficheros"][k] = float(v)
                    print(f"Precios procesados: {precios}")  # Debug line
                    return precios
                except json.JSONDecodeError as e:
                    print(f"Error al decodificar PRECIOS.txt: {e}")
                    return {"por_gb": {}, "por_duracion": {}, "por_ficheros": {}}
        except Exception as e:
            print(f"Error al cargar precios: {e}")
            traceback.print_exc()
            return {"por_gb": {}, "por_duracion": {}, "por_ficheros": {}}
    
    def calcular_precio(self, tamano_gb):
        if not self.precios:
            return None
        precio_aplicable = 0
        for tamano, precio in sorted(self.precios.items()):
            if tamano_gb <= tamano:
                precio_aplicable = precio
                break
        return precio_aplicable
    def show_cola_button(self):
        self.cola_button.show()
    def hide_cola_button(self):
        self.cola_button.hide()
    def actualizar_vista_discos(self):
        print("Actualizando vista de discos")
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if item.isHidden():
                item.setHidden(False)
        self.list_widget.update()
    
    def edit_temporary_name(self):
        if hasattr(self, 'rename_dialog') and self.rename_dialog.isVisible():
            self.rename_dialog.raise_()
            self.rename_dialog.activateWindow()
            return
        selected_items = self.list_widget.selectedItems()
        if selected_items:
            item = selected_items[0]
            widget = self.list_widget.itemWidget(item)
            if widget and hasattr(widget, 'volume_label'):
                current_text = widget.volume_label.text()
                drive_letter = current_text.split(' ')[-1].strip('()')
                current_name = current_text.split(f' ({drive_letter})')[0]
                current_alias = ""
                if '|' in current_name:
                    current_alias, current_name = current_name.split('|')
                    current_alias = current_alias.strip()
                    current_name = current_name.strip()
                temp_name_icon = create_temp_name_icon(size=30)
                
                # Pasar directamente el QIcon al diálogo
                self.rename_dialog = CustomRenameDialog(self, current_alias, temp_name_icon, is_volume=False, is_alias=True)
                self.rename_dialog.setWindowTitle("Alias Temporal")
                if self.rename_dialog.exec() == QDialog.DialogCode.Accepted:
                    new_alias = self.rename_dialog.get_new_name()
                    if new_alias:
                        new_text = f"{new_alias} | {current_name} ({drive_letter})"
                    else:
                        new_text = f"{current_name} ({drive_letter})"
                    if '|' in current_text and len(current_text.split('|')) > 2:
                        total_price_part = current_text.split('|')[-1]
                        new_text += f" {total_price_part}"
                    widget.volume_label.setText(new_text)
                    print(f"Alias temporal '{new_alias}' aplicado al disco {drive_letter}")
    
    def confirmar_reparacion_discos(self, discos):
        self.iniciar_reparacion_discos(discos)
    
    def iniciar_reparacion_discos(self, discos):
        print(f"Iniciando reparación para discos: {discos}")
        for drive_letter in discos:
            self.cambiar_icono_disco(drive_letter, create_repair_icon(size=32))
            self.disk_space_before_repair[drive_letter] = self.get_free_space(drive_letter)
        disks_info = [(self.get_volume_name(dl), dl) for dl in discos]
        repair_disks_in_background(self, disks_info)
        self.start_disk_access_monitoring(discos)
    
    def start_disk_access_monitoring(self, discos):
        self.disk_access_timer = QTimer(self)
        self.disk_access_timer.timeout.connect(lambda: self.check_disk_access(discos))
        self.disk_access_timer.start(2000)  # Verificar cada 2 segundos
    
    def check_disk_access(self, discos):
        for drive_letter in discos:
            if self.is_disk_accessible(drive_letter):
                self.finalizar_reparacion([drive_letter])
                self.disk_access_timer.stop()
    
    def is_disk_accessible(self, drive_letter):
        try:
            test_path = os.path.join(drive_letter, "test_access") # Intenta acceder a la raíz del disco
            with open(test_path, 'w') as f:
                f.write("test")
            os.remove(test_path)
            return True
        except Exception as e:
            print(f"Disco {drive_letter} inaccesible: {e}")
            return False
    
    def get_free_space(self, drive_letter):
        free_bytes = ctypes.c_ulonglong(0)
        ctypes.windll.kernel32.GetDiskFreeSpaceExW(ctypes.c_wchar_p(drive_letter), None, None, ctypes.byref(free_bytes))
        return free_bytes.value
    
    def cambiar_icono_disco(self, drive_letter, icon):
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if drive_letter in item.text():
                if drive_letter not in self.original_icons:
                    self.original_icons[drive_letter] = item.icon()
                if isinstance(icon, QIcon):
                    item.setIcon(icon)
                else:
                    item.setIcon(QIcon(icon))
                break
    
    def restaurar_icono_disco(self, drive_letter):
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if drive_letter in item.text() and drive_letter in self.original_icons:
                item.setIcon(self.original_icons[drive_letter])
                del self.original_icons[drive_letter]
                break
    
    def finalizar_reparacion(self, discos):
        for drive_letter in discos:
            self.restaurar_icono_disco(drive_letter)
        print(f"Reparación finalizada para discos: {discos}")
    
    def obtener_ruta_icono(self, nombre_icono):
        return os.path.join(os.path.dirname(__file__), 'iconos', nombre_icono)
 
    def is_copying_or_queued(self, drive_letter):
        is_copying = drive_letter in self.queues and self.queues[drive_letter].qsize() > 0
        has_pending_copy = drive_letter in self.copy_bytes_copied and self.copy_bytes_copied[drive_letter] < self.total_sizes.get(drive_letter, 0)
        return is_copying or has_pending_copy

    def get_free_space_str(self, drive_letter):
        free_space, _ = get_drive_space(drive_letter)
        return f" | {self.format_size(free_space)}"

    def get_queue_total_size_str(self, drive_letter):
        if drive_letter in self.total_sizes and self.total_sizes[drive_letter] > 0:
            return f"Cola: {self.format_size(self.total_sizes[drive_letter])}"
        else:
            return ""

    def setup_shortcut(self):
        self.toggle_lines_shortcut = QShortcut(QKeySequence(Qt.Key.Key_Insert), self)
        self.toggle_lines_shortcut.activated.connect(self.toggle_disk_lines)

    def toggle_disk_lines(self):
        self.config['show_disk_lines'] = not self.config.get('show_disk_lines', True)
        save_config(self.config)
        self.apply_disk_lines_config()
        print(f"Estado de líneas cambiado a: {self.config['show_disk_lines']}")

    def apply_disk_lines_config(self):
        show_lines = self.config.get('show_disk_lines', True)
        if hasattr(self, 'list_widget') and isinstance(self.list_widget, DraggableListWidget):
            self.list_widget.show_disk_lines = show_lines
            self.list_widget.update()

    def save_pending_copies(self):
        # Determinar la ruta del archivo
        if getattr(sys, 'frozen', False):
            base_path = os.path.dirname(sys.executable)
        else:
            base_path = os.path.dirname(__file__)
        save_path = os.path.join(base_path, 'pending_copies.json')
        
        # Cargar copias pendientes existentes si el archivo existe
        existing_pending_copies = {}
        if os.path.exists(save_path):
            try:
                with open(save_path, 'r', encoding='utf-8') as f:
                    existing_pending_copies = json.load(f)
                print(f"Copias pendientes existentes cargadas: {list(existing_pending_copies.keys())}")
            except Exception as e:
                print(f"Error al cargar copias pendientes existentes: {e}")
        
        # Actualizar con la información actual de las colas
        for drive_letter in self.queues.keys():
            if self.queues[drive_letter].qsize() > 0 or drive_letter in self.current_copying_file:
                # Obtener el nombre del volumen
                volume_name = ""
                try:
                    volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                except:
                    pass
                drive_key = f"{volume_name} ({drive_letter})" if volume_name else f"{drive_letter}"
                queue_items = []
                for src, dest in list(self.queues[drive_letter].queue):
                    # Extraer solo el nombre del archivo del path completo
                    file_name = os.path.basename(src)
                    queue_items.append({
                        "source": src,
                        "destination": dest,
                        "file_name": file_name
                    })
                current_file = None
                if drive_letter in self.current_copying_file and self.current_copying_file[drive_letter]:
                    src, dest = self.current_copying_file[drive_letter]
                    file_name = os.path.basename(src)
                    current_file = {
                        "source": src,
                        "destination": dest,
                        "file_name": os.path.basename(src)
                    }
                    queue_items.insert(0, current_file)
                existing_pending_copies[drive_key] = {
                    'queue': queue_items,
                    'total_size': self.total_sizes.get(drive_letter, 0),
                    'bytes_copied': self.copy_bytes_copied.get(drive_letter, 0),
                    'current_file': current_file,
                    'drive_letter': drive_letter,  # Guardar la letra del disco para referencia
                    'volume_name': volume_name 
                }
        print(f"Guardando copias pendientes para {len(existing_pending_copies)} discos: {list(existing_pending_copies.keys())}")
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(existing_pending_copies, f, indent=2, ensure_ascii=False)
        print(f"Copias pendientes guardadas en: {save_path}")

    def restore_pending_copies(self, drive_letter=None):
        """Restaura las copias pendientes usando el mismo flujo que un arrastre y elimina del pending los discos restaurados, evitando duplicados en la cola."""
        if getattr(sys, 'frozen', False):
            base_path = os.path.dirname(sys.executable)
        else:
            base_path = os.path.dirname(__file__)
        save_path = os.path.join(base_path, 'pending_copies.json')
        try:
            if not os.path.exists(save_path):
                self.list_widget.show_tooltip("NO HAY COPIAS PENDIENTES", duration=5000)
                return
            with open(save_path, 'r', encoding='utf-8') as f:
                pending_copies = json.load(f)
            total_files_restored = 0
            restored_info = []
            discos_restaurados = []
            # Usamos una copia de las claves para poder eliminar mientras iteramos
            for drive_key in list(pending_copies.keys()):
                data = pending_copies[drive_key]
                drive_letter_data = data.get('drive_letter')
                saved_volume_name = drive_key.split(f" ({drive_letter_data})")[0] if f" ({drive_letter_data})" in drive_key else ""
                if not drive_letter_data or not os.path.exists(f"{drive_letter_data}\\"):
                    continue
                try:
                    current_volume_name = win32api.GetVolumeInformation(f"{drive_letter_data}\\")[0]
                except:
                    current_volume_name = ""
                if current_volume_name != saved_volume_name:
                    continue
                # Si se especificó drive_letter, solo restaurar ese
                if drive_letter is not None and drive_letter_data != drive_letter:
                    continue
                queue_items = data['queue']
                processed_files = []

                # Obtener archivos ya en la cola
                archivos_en_cola = set()
                if hasattr(self, "queues") and drive_letter_data in self.queues:
                    archivos_en_cola = set((src, dst) for src, dst in list(self.queues[drive_letter_data].queue))
                for item in queue_items:
                    src = item['source']
                    dst = item['destination']
                    try:
                        size = os.path.getsize(src)
                    except Exception:
                        size = 0
                    # Solo agregar si no está ya en la cola
                    if (src, dst) not in archivos_en_cola:
                        processed_files.append((src, size, dst))
                if processed_files:  # Solo si hay nuevos archivos para agregar
                    self.list_widget.process_dropped_files(
                        processed_files=processed_files,
                        drive_letter=drive_letter_data,
                        shift_pressed=False,
                        current_path=f"{drive_letter_data}\\",
                        volume_name=current_volume_name,
                        dragging_folder=False
                    )
                    restored_info.append(f"{drive_letter_data}: {len(processed_files)} archivos")
                    total_files_restored += len(processed_files)
                    discos_restaurados.append(drive_key)  # Marcar para eliminar
            if total_files_restored > 0:
                message = "\n".join(restored_info)
                self.list_widget.show_tooltip(f"Copias restauradas:\n{message}", duration=5000)
            else:
                self.list_widget.show_tooltip("No hay copias pendientes para restaurar", duration=5000)
        except Exception as e:
            print(f"Error al restaurar copias: {e}")
            import traceback
            traceback.print_exc()
            self.list_widget.show_tooltip(f"Error al restaurar copias: {str(e)}", duration=5000)

    def format_size(self, size):
        """
        Formatea el tamaño usando la misma lógica que Windows Explorer con redondeo preciso
        """
        KB = 1024
        MB = KB * 1024
        GB = MB * 1024
        TB = GB * 1024
        if size < KB:
            return f"{size} B"
        elif size < MB:
            value = size / KB
            return f"{value:.2f} KB" 
        elif size < GB:
            value = size / MB
            return f"{value:.2f} MB"
        elif size < TB:
            value = size / GB
            return f"{value:.3f} GB" 
        else:
            value = size / TB
            return f"{value:.2f} TB"  

    def handle_delete(self):
        try:
            if hasattr(self, 'file_tree_widget') and self.file_tree_widget.isVisible():
                selected_items = self.file_tree_widget.selectedItems()
                if not selected_items:
                    return
                valid_items = []
                for item in selected_items:
                    try:
                        # Comprobar si el objeto QTreeWidgetItem sigue siendo válido
                        if item and not sip.isdeleted(item):
                            # Intentar acceder a sus propiedades para verificar validez
                            _ = item.text(0)
                            valid_items.append(item)
                    except (RuntimeError, AttributeError):
                        # Si hay error al acceder al item, lo ignoramos
                        continue
                    
                # Si no hay elementos válidos, salir
                if not valid_items:
                    return
                    
                # Reemplazar los elementos seleccionados con los válidos
                # Esto evita modificar la función confirm_delete_selected_items
                self.file_tree_widget.clearSelection()
                for item in valid_items:
                    item.setSelected(True)
                    
                # Proceder con el borrado usando la función original
                from BORRADO import confirm_delete_selected_items
                confirm_delete_selected_items(self)
            elif self.list_widget.isVisible():
                selected_items = self.list_widget.selectedItems()
                if selected_items:
                    drive_letter = selected_items[0].text().split(' ')[-1].strip('()')
                    if drive_letter in self.queue_windows:
                        queue_window = self.queue_windows[drive_letter]
                        # Guardar el espacio final actual antes del borrado
                        current_final = float(self.final_space_text[drive_letter].split()[0])
                        current_unit = self.final_space_text[drive_letter].split()[1]
                        
                        # Realizar el borrado
                        queue_window.delete_selected_files()
                        
                        # Actualizar el espacio final en la vista de discos
                        try:
                            if current_unit == 'TB':
                                multiplier = 1024 ** 4
                            else:  # GB
                                multiplier = 1024 ** 3
                            
                            # Recalcular el espacio final basado en el total actual
                            total_size = self.total_sizes.get(drive_letter, 0)
                            free_space, _ = self.get_drive_space(drive_letter)
                            new_final_bytes = free_space - total_size
                            if current_unit == 'TB':
                                new_final = new_final_bytes / (1024 ** 4)
                            else:
                                new_final = new_final_bytes / (1024 ** 3)
                                if new_final >= 1024:
                                    new_final = new_final / 1024
                                    current_unit = 'TB'
                            self.final_space_text[drive_letter] = f"{new_final:.2f} {current_unit}"
                            print(f"Espacio final actualizado en vista de discos: {self.final_space_text[drive_letter]}")
                            
                            # Actualizar la etiqueta del volumen
                            self._update_volume_label_after_delete(drive_letter)
                        except Exception as e:
                            print(f"Error actualizando espacio final en vista de discos: {e}")
                    else:
                        print(f"No hay ventana de cola abierta para el disco {drive_letter}")
                else:
                    print("No hay elementos seleccionados para eliminar de la cola.")
            else:
                print("No hay elementos seleccionados para eliminar.")
        except Exception as e:
            # Capturar cualquier error y registrarlo sin cerrar la aplicación
            print(f"Error en handle_delete: {e}")
            import traceback
            traceback.print_exc()
    
    def load_config_and_apply_mode(self):
        """Carga y aplica la configuración inicial"""
        config = load_config()
        
        # Cargar estado de voz
        voice_enabled = config.get('voice_enabled', True)  # Por defecto activado
        self.config['voice_enabled'] = voice_enabled
        print(f"Estado de voz cargado: {'activado' if voice_enabled else 'desactivado'}")
        
        progress_bar_color = config.get("progress_bar_color", "#0078d7")
        print(f"Cargando color de barra de progreso: {progress_bar_color}")
        self.update_progress_bar_color(progress_bar_color)
        
        # Cargar y aplicar el estado de ventana fijada
        window_topmost = config.get("window_topmost", False)
        if window_topmost:
            # Aplicar estado TOPMOST a la ventana principal
            hwnd = int(self.winId())
            win32gui.SetWindowPos(
                hwnd, 
                win32con.HWND_TOPMOST, 
                0, 0, 0, 0,
                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_NOACTIVATE
            )
            
            # Actualizar el icono del botón de fijar en el widget de RAM
            if hasattr(self, 'ram_usage_widget'):
                self.ram_usage_widget.is_pinned = True
                self.ram_usage_widget.pin_button.setIcon(QIcon(self.ram_usage_widget.unpin_icon))
                
                # Iniciar el temporizador para mantener la ventana en primer plano
                if not hasattr(self.ram_usage_widget, '_topmost_timer'):
                    self.ram_usage_widget._topmost_timer = QTimer(self.ram_usage_widget)
                    self.ram_usage_widget._topmost_timer.timeout.connect(
                        lambda: self.ram_usage_widget._ensure_topmost(hwnd)
                    )
                    self.ram_usage_widget._topmost_timer.setInterval(500)
                self.ram_usage_widget._topmost_timer.start()
        if hasattr(self, 'show_disk_lines'):
            self.show_disk_lines = config.get('show_disk_lines', True)
        if hasattr(self, 'size_mode'):
            self.size_mode = config.get('size_mode', 'Normal')
        self.config = config
        print("Configuración cargada y aplicada exitosamente")
    
    def save_config(config):
        """Guarda la configuración preservando todos los valores"""
        try:
            config_path = get_config_path()
            
            # Cargar configuración existente si existe
            existing_config = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
                    
            # Preservar discos_ocultos si existen
            if 'discos_ocultos' in existing_config:
                config['discos_ocultos'] = existing_config['discos_ocultos']
                
            # Guardar configuración actualizada
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
                print(f"Configuración guardada en: {config_path}")
                print(f"Contenido: {config}")
                
        except Exception as e:
            print(f"Error guardando configuración: {e}")
            import traceback
            traceback.print_exc()

    def load_config():
        """Carga la configuración desde la ubicación correcta"""
        try:
            config_path = get_config_path()
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    print(f"Configuración cargada desde: {config_path}")
                    return config
        except Exception as e:
            print(f"Error cargando configuración: {e}")
        return {}

    def show_queue_window(self, selected_drive):
        queue_window = ColaWindow(self.files_in_queue, selected_drive, self)
        queue_window.delete_files_signal.connect(self.handle_delete_files)
        queue_window.show()
        self.update_total_size_signal.connect(self.update_total_size)
    
    def update_total_size_from_thread(self, drive_letter, size_change):
        QMetaObject.invokeMethod(self, "update_total_size",
                                 Qt.ConnectionType.QueuedConnection,
                                 Q_ARG(str, drive_letter),
                                 Q_ARG(int, size_change))
    
    def update_total_size(self, drive_letter, size_change):
        if drive_letter not in self.total_sizes:
            self.total_sizes[drive_letter] = 0
        old_size = self.total_sizes[drive_letter]
        self.total_sizes[drive_letter] = max(0, self.total_sizes[drive_letter] + size_change)
        print(f"Tamaño total actualizado para {drive_letter}: {old_size} -> {self.total_sizes[drive_letter]} bytes")
        self.update_size_label(drive_letter)
    
    def handle_delete_files(self, files_to_delete, drive):
        print(f"Manejando eliminación de archivos para {drive}")
        if drive not in self.queue_locks:
            self.queue_locks[drive] = Lock()
        with self.queue_locks[drive]:
            total_size_removed = 0
            files_removed = 0  # Contador de archivos eliminados
            
            # Procesar archivos a eliminar
            if drive in self.queues:
                queue_list = list(self.queues[drive].queue)
                for file_path, destination_path, file_size in files_to_delete:
                    if (file_path, destination_path) in self.files_in_queue:
                        self.files_in_queue.remove((file_path, destination_path))
                    try:
                        queue_list.remove((file_path, destination_path))
                        total_size_removed += file_size
                        files_removed += 1
                        print(f"Eliminado archivo: {file_path}, tamaño: {self.format_size(file_size)}")
                        
                        # Eliminar el archivo de archivos_procesados si existe
                        if hasattr(self, 'archivos_procesados') and drive in self.archivos_procesados:
                            if file_path in self.archivos_procesados[drive]:
                                self.archivos_procesados[drive].remove(file_path)
                                print(f"Archivo {file_path} eliminado de archivos_procesados")
                    except ValueError:
                        print(f"File {file_path} not found in queue for drive {drive}")
                
                # Reconstruir la cola
                self.queues[drive].queue.clear()
                for item in queue_list:
                    self.queues[drive].put(item)
                
                # Actualizar tamaños y espacio final
                if drive in self.total_sizes:
                    self.total_sizes[drive] = max(0, self.total_sizes[drive] - total_size_removed)
                    
                    # Verificar modo de pago y actualizar precio si es necesario
                    config = load_config()
                    modo_pago = config.get('modo_pago', 'dispositivo')
                    if modo_pago == "ficheros":
                        try:
                            # Cargar precios actualizados desde archivo
                            precios = self.cargar_precios()
                            # Obtener precio según archivos eliminados
                            precio_a_restar = 0
                            
                            # Buscar coincidencias en los archivos eliminados
                            for file_path, _, _ in files_to_delete:
                                source_path = os.path.normpath(file_path)
                                parent_dir = os.path.basename(os.path.dirname(source_path))
                                
                                # Buscar coincidencia con los precios configurados
                                for clave, data in precios["por_ficheros"].items():
                                    # Verificar si es el nuevo formato (diccionario)
                                    if isinstance(data, dict):
                                        disco = data.get("disco", "")
                                        directorio = data.get("directorio", "")
                                        precio = data.get("precio", 0)
                                        
                                        # Si el disco coincide con el actual
                                        if disco.upper() == os.path.splitdrive(source_path)[0].upper():
                                            # Si hay un directorio específico, verificar si el archivo está en ese directorio
                                            if directorio:
                                                directorio_norm = directorio.replace('\\', '/').upper()
                                                source_path_norm = source_path.replace('\\', '/').upper()
                                                
                                                if directorio_norm in source_path_norm:
                                                    precio_a_restar += float(precio)
                                                    break
                                            else:
                                                # Si no hay directorio específico, aplicar el precio a cualquier archivo en el disco
                                                precio_a_restar += float(precio)
                                                break
                                    # Compatibilidad con formato antiguo
                                    else:
                                        carpeta_norm = clave.upper().strip()
                                        precio = float(data)
                                        
                                        if (carpeta_norm == parent_dir.upper() or 
                                            carpeta_norm in source_path.upper()):
                                            precio_a_restar += precio
                                            break

                            # Si no se encontró ninguna coincidencia, usar un valor por defecto multiplicado por archivos
                            if precio_a_restar == 0:
                                precio_por_defecto = 2.0  # Valor por defecto si no hay coincidencias
                                precio_a_restar = precio_por_defecto * files_removed

                            if hasattr(self, 'precios_procesados') and drive in self.precios_procesados:
                                self.precios_procesados[drive] = max(0, self.precios_procesados[drive] - precio_a_restar)
                                print(f"Precio actualizado para {drive}: ${self.precios_procesados[drive]:.2f} (restado ${precio_a_restar:.2f})")
                        except Exception as e:
                            print(f"Error al actualizar precio por ficheros: {e}")
                    
                    try:
                        # Obtener espacio libre actual
                        free_space, _ = get_drive_space(drive)
                        
                        # Convertir el espacio final actual a bytes
                        current_final_parts = self.final_space_text[drive].split()
                        current_value = float(current_final_parts[0])
                        current_unit = current_final_parts[1]
                        
                        # Convertir a bytes según la unidad
                        multipliers = {
                            'B': 1,
                            'KB': 1024,
                            'MB': 1024 ** 2,
                            'GB': 1024 ** 3,
                            'TB': 1024 ** 4
                        }
                        current_final_bytes = current_value * multipliers[current_unit]
                        
                        # Sumar el espacio liberado
                        new_final_bytes = current_final_bytes + total_size_removed
                        
                        # Actualizar el espacio final con el nuevo valor formateado
                        self.final_space_text[drive] = self.format_size(new_final_bytes)
                        print(f"Espacio final actualizado: {self.final_space_text[drive]}")
                        print(f"Tamaño total removido: {self.format_size(total_size_removed)}")
                    except Exception as e:
                        print(f"Error actualizando espacio final: {e}")
                self._update_volume_label_after_delete(drive)
                self.update_speed_signal.emit()
                if drive in self.queue_windows:
                    self.queue_windows[drive].update_queue_view()
                self.save_pending_copies()

    def _update_volume_label_after_delete(self, drive):
        """Actualiza las etiquetas después de eliminar archivos de la cola"""
        try:
            # Obtener configuración actual
            config = load_config()
            modo_pago = config.get('modo_pago', 'dispositivo')
            
            # Actualizar la vista de discos
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if drive in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget is not None:
                        volume_label = widget.volume_label
                        try:
                            original_text = win32api.GetVolumeInformation(f"{drive}\\")[0]
                        except:
                            original_text = f"Disco ({drive})"
                        if self.total_sizes[drive] > 0:
                            total_size_str = self.format_size(self.total_sizes[drive])
                            
                            # Calcular precio según el modo
                            precio = None
                            if modo_pago == "duracion":
                                duracion_total = 0
                                # Recorrer archivos pendientes para calcular duración
                                if drive in self.queues:
                                    for source, _ in list(self.queues[drive].queue):
                                        if os.path.exists(source):
                                            duracion = self.get_video_duration(source)
                                            if duracion is not None:
                                                duracion_total += duracion
                                precio = self.calcular_y_mostrar_precio(drive, None, duracion_total)
                            elif modo_pago == "ficheros":
                                # Obtener el total de archivos en la cola
                                total_files = 0
                                if drive in self.queues:
                                    total_files = self.queues[drive].qsize()
                                precio = self.calcular_y_mostrar_precio(drive, None, None, total_files=total_files)
                            else:  # modo dispositivo
                                total_size_gb = self.total_sizes[drive] / (1024 ** 3)
                                precio = self.calcular_y_mostrar_precio(drive, total_size_gb)

                            # Verificar si el precio está oculto (borrado desde cola)
                            is_price_hidden = drive in self.queue_windows
                            
                            # Construir el texto de la etiqueta con salto de línea
                            label_text = f"{original_text} ({drive})\n[ 𝐓: {total_size_str}]"
                            if precio is not None:
                                if modo_pago == "duracion":
                                    label_text += f" (⏱️${precio:.2f})"
                                elif modo_pago == "ficheros":
                                    label_text += f" (📜${precio:.2f})"
                                else:
                                    label_text += f" (💰${precio:.2f})"
                            if not is_price_hidden:
                                final_space = self.final_space_text.get(drive, '')
                                if final_space:
                                    label_text += f" [ 𝐅: {final_space}]"
                            volume_label.setText(label_text)
                        else:
                            # Si no hay archivos en cola, mostrar solo el nombre original
                            volume_label.setText(f"{original_text} ({drive})")
                        break

                # Actualizar el header del explorador si estamos en la vista de show_drive
                if hasattr(self, 'current_explorer_drive') and self.current_explorer_drive == drive:
                    volume_name = self.get_volume_name(drive)
                    drive_info_text = f"{volume_name} ({drive})"
                    if self.total_sizes[drive] > 0:
                        total_size_str = self.format_size(self.total_sizes[drive])
                        
                        # Calcular precio para el header según el modo
                        precio = None
                        if modo_pago == "duracion":
                            duracion_total = 0
                            if drive in self.queues:
                                for source, _ in list(self.queues[drive].queue):
                                    if os.path.exists(source):
                                        duracion = self.get_video_duration(source)
                                        if duracion is not None:
                                            duracion_total += duracion
                            precio = self.calcular_y_mostrar_precio(drive, None, duracion_total)
                        elif modo_pago == "ficheros":
                            # Obtener el total de archivos en cola
                            total_files = 0
                            if drive in self.queues:
                                total_files = self.queues[drive].qsize()
                            precio = self.calcular_y_mostrar_precio(drive, None, None, total_files=total_files)
                        else:  # modo dispositivo
                            total_size_gb = self.total_sizes[drive] / (1024 ** 3)
                            precio = self.calcular_y_mostrar_precio(drive, total_size_gb)

                        final_space = self.final_space_text.get(drive, '')
                        final_space_text = f" [ 𝐅: {final_space}]" if final_space else ""
                        
                        # Construir el texto del header con la información en la segunda línea
                        drive_info_text += f" | 𝐓: {total_size_str}"
                        if precio is not None:
                            if modo_pago == "duracion":
                                drive_info_text += f" (⏱️${precio:.2f})"
                            elif modo_pago == "ficheros":
                                drive_info_text += f" (📜${precio:.2f})"
                            else:
                                drive_info_text += f" (💰${precio:.2f})"
                        if final_space:
                            drive_info_text += f"{final_space_text}"
                    if hasattr(self, 'drive_info_label'):
                        self.drive_info_label.setText(drive_info_text)
        except Exception as e:
            print(f"Error actualizando etiquetas después de borrar: {e}")
            traceback.print_exc()

    def delete_files_in_thread(self, files_to_delete, drive):
        try:
            formatted_files = []
            for file_path in files_to_delete:
                try:
                    if os.path.exists(file_path):
                        if os.path.isfile(file_path):
                            file_size = os.path.getsize(file_path)
                        elif os.path.isdir(file_path):
                            file_size = sum(
                                os.path.getsize(os.path.join(root, file))
                                for root, _, files in os.walk(file_path)
                                for file in files
                            )
                        else:
                            file_size = 0
                        formatted_files.append((file_path, file_path, file_size))
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                        print(f"Archivo {file_path} eliminado.")
                except Exception as e:
                    print(f"Error al borrar el archivo {file_path}: {e}")
            self.handle_delete_files(formatted_files, drive)
        finally:
            if drive in self.threads:
                thread = self.threads[drive]
                if thread.is_alive():
                    thread.paused = False
                    if drive in self.pause_times:
                        pause_duration = time.time() - self.pause_times.pop(drive, time.time())
                        self.copy_start_time[drive] += pause_duration
                    print(f"Copia en {drive} reanudada después del borrado.")
                    self.update_speed_display()
                    if hasattr(self, 'current_explorer_drive') and self.current_explorer_drive == drive:
                        self.show_drive_contents(drive)
        
    def start_delete_thread(self, files_to_delete, drive):
         thread = threading.Thread(target=self.delete_files_in_thread, args=(files_to_delete, drive))
         thread.start()

    def closeEvent(self, event):
        """Evento de cierre mejorado: garantiza la detención segura de hilos, timers y recursos."""
        try:
            # 1. Guardar configuración
            config = load_config()
            if hasattr(self, 'tipo_pago_combo'):
                config['modo_pago'] = ["dispositivo", "duracion", "ficheros"][self.tipo_pago_combo.currentIndex()]
                config.setdefault('discos_ocultos', [])
                save_config(config)
                print("Configuración guardada al cerrar")

            # 2. Detener timers (siempre en el hilo principal)
            def _stop_all_timers():
                for timer in [getattr(self, name, None) for name in ['timer', 'speed_update_timer', 'update_timer']]:
                    if timer: 
                        timer.stop()
                for timer in self.findChildren(QTimer):
                    try: 
                        timer.stop()
                    except Exception as e:
                        print(f"Error al detener timer: {str(e)}")

            if threading.current_thread() is threading.main_thread():
                _stop_all_timers()
            else:
                QMetaObject.invokeMethod(self, "_stop_all_timers", Qt.ConnectionType.BlockingQueuedConnection)

            # 3. Detener hilos (QThread y threading.Thread)
            def _stop_threads():
                # QThreads
                if hasattr(self, 'worker'):
                    self.worker.quit()
                    if not self.worker.wait(2000):  # 2 segundos de espera
                        self.worker.terminate()
                        print("¡Advertencia! QThread no respondió y fue terminado.")

                # ThreadPool
                if hasattr(self, 'thread_pool'):
                    self.thread_pool.clear()  # Limpiar tareas pendientes
                    self.thread_pool.waitForDone(2000)  # 2 segundos

                # Threads de copia
                if hasattr(self, 'threads'):
                    for drive, thread in list(self.threads.items()):
                        if thread.is_alive():
                            thread.do_run = False  # Señal de parada
                            if not thread.join(3.0):  # 3 segundos de espera
                                print(f"¡Advertencia! Hilo de {drive} no respondió")
                                # Forzar terminación si es necesario
                                if hasattr(thread, 'terminate'):
                                    thread.terminate()

            _stop_threads()

            # 4. Limpieza de recursos (archivos temporales, conexiones, etc.)
            def _cleanup_resources():
                try:
                    if hasattr(self, 'cleanup_before_exit'):
                        self.cleanup_before_exit()
                    # Cerrar archivos abiertos
                    if hasattr(self, 'open_files'):
                        for file in self.open_files:
                            try:
                                file.close()
                            except Exception as e:
                                print(f"Error al cerrar archivo: {str(e)}")
                except Exception as e:
                    print(f"Error durante la limpieza de recursos: {str(e)}")
            _cleanup_resources()
            event.accept()
        except Exception as e:
            print(f"Error crítico durante el cierre: {str(e)}")
            traceback.print_exc()
            event.accept()

    def cleanup_and_quit(self):
        """Limpia los archivos temporales y cierra la aplicación"""
        try:
            # Limpiar directorio temporal usando la ruta de ProgramData
            temp_dir = os.path.join('C:\\', 'ProgramData', '.zetacopy_temp')
            if os.path.exists(temp_dir):
                try:
                    # Cambiar permisos para permitir eliminación
                    for root, dirs, files in os.walk(temp_dir):
                        for d in dirs:
                            try:
                                dir_path = os.path.join(root, d)
                                os.chmod(dir_path, 0o777)
                            except:
                                pass
                        for f in files:
                            try:
                                file_path = os.path.join(root, f)
                                os.chmod(file_path, 0o777)
                                os.unlink(file_path)
                            except:
                                pass
                    # Intentar eliminar el directorio completo
                    shutil.rmtree(temp_dir, ignore_errors=True)
                except Exception as e:
                    print(f"Error limpiando directorio temporal: {e}")
        finally:
            # Forzar el cierre de la aplicación
            QApplication.quit()
            # Forzar la terminación del proceso
            os._exit(0)

    def get_all_items(self):
        return [self.list_widget.item(i) for i in range(self.list_widget.count())]
    
    def load_indexed_data(self):
        indexed_data = {}
        try:
            with open("INDEXADO.txt", "r", encoding='utf-8') as f:
                content = f.read()
            current_drive = None
            for line in content.split('\n'):
                if line.startswith("Disco:"):
                    current_drive = line.split("(")[-1].strip(")")
                    indexed_data[current_drive] = []
                elif current_drive and line.strip() and not line.startswith("="):
                    indexed_data[current_drive].append(line.strip())
        except FileNotFoundError:
            print("No se encontró el archivo INDEXADO.txt.")
        except Exception as e:
            print(f"Error al cargar los datos indexados: {str(e)}")
        return indexed_data

    def get_drive_letter(path):
        """Extrae la letra del disco de una ruta."""
        if path and len(path) >= 2 and path[1] == ':':
            return path[0].upper() + ':'
        return None

    def get_file_extension(self, filename):
        """Obtiene la extensión de un archivo"""
        last_dot_pos = filename.rfind('.')
        if last_dot_pos != -1:
            return filename[last_dot_pos:].lower()
        return ""

    def queue_next_n_files(self, n=0, ctrl_pressed=False, selected_files=None):
        from TOOLTIP_APARIENCIA import showTooltipAtWidget, showTooltipAtCursor
        
        print(f"Iniciando queue_next_n_files para {'todos los archivos' if n == 0 else f'{n} archivos'}")
        print(f"Control presionado: {ctrl_pressed}")

        # Si se proporcionaron archivos específicos desde el indexado
        if selected_files is not None:
            destination_drive = get_drive_letter(self.current_drive)
            current_directory = self.current_drive
            self.initialize_copy_structures(destination_drive)
            total_size_added = 0
            files_added = 0
            files_skipped = 0
            for file_info in selected_files:
                source_path = file_info['source']
                dest_filename = file_info['dest_filename']
                
                # Determinar si el archivo ya tiene extensión .txt
                if dest_filename.lower().endswith('.txt'):
                    # Verificar si tiene una extensión reconocida antes del .txt
                    name_without_txt = dest_filename[:-4]
                    last_dot_pos = name_without_txt.rfind('.')
                    if last_dot_pos != -1:
                        possible_ext = name_without_txt[last_dot_pos:].lower()
                        common_extensions = [
                            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.mpg', '.mpeg',
                            '.mp3', '.wav', '.ogg', '.flac', '.aac', '.wma',
                            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp',
                            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                            '.zip', '.rar', '.7z', '.tar', '.gz'
                        ]
                        if any(possible_ext == ext for ext in common_extensions):
                            # Usar el nombre sin .txt
                            dest_filename = name_without_txt
                            print(f"Usando nombre sin .txt: {dest_filename}")
                destination_path = os.path.join(current_directory, dest_filename)
                print(f"Procesando: {source_path} -> {destination_path}")
                
                # NUEVA VERIFICACIÓN: Comprobar si el archivo ya existe con el mismo tamaño
                if os.path.exists(destination_path) and os.path.exists(source_path):
                    try:
                        source_size = os.path.getsize(source_path)
                        dest_size = os.path.getsize(destination_path)
                        if source_size == dest_size:
                            print(f"Archivo ya existe con el mismo tamaño: {destination_path}")
                            files_skipped += 1
                            continue  # Saltar este archivo
                    except Exception as e:
                        print(f"Error verificando tamaño: {e}")
                if os.path.exists(source_path):
                    file_size = os.path.getsize(source_path)
                    _, total, free = shutil.disk_usage(destination_drive)
                    free_space = free - 1024 * 1024 * 100  # Dejar 100 MB de margen
                    if total_size_added + file_size > free_space:
                        print(f"No hay suficiente espacio para agregar {dest_filename}")
                        break
                    queue_key = (source_path, destination_path)
                    if queue_key not in self.files_in_queue:
                        self.queues[destination_drive].put(queue_key)
                        self.files_in_queue.add(queue_key)
                        total_size_added += file_size
                        files_added += 1
                else:
                    print(f"Advertencia: El archivo {source_path} no existe.")
            if files_added > 0:
                self.update_total_size(destination_drive, total_size_added)
                print(f"Tamaño total añadido a {destination_drive}: {total_size_added / (1024 * 1024 * 1024):.2f} GB")
                self.start_copy_thread(destination_drive)
                self.update_progress_signal.emit(destination_drive, 0)
                self.update_file_progress_signal.emit(destination_drive, 0)
                self.update_speed_signal.emit()
                
                # Usar showTooltipAtWidget en lugar de QToolTip.showText
                tooltip_text = f"SE HAN AGREGADO {files_added} ARCHIVOS A LA COLA DE COPIA"
                if files_skipped > 0:
                    tooltip_text += f" (SE OMITIERON {files_skipped} ARCHIVOS QUE YA EXISTEN)"
                showTooltipAtWidget(tooltip_text, self, 3000)
            else:
                print("NO SE AGREGARON ARCHIVOS NUEVOS")
                tooltip_text = "NO SE AGREGARON ARCHIVOS NUEVOS"
                if files_skipped > 0:
                    tooltip_text += f" (TODOS LOS ARCHIVOS YA EXISTEN)"
                showTooltipAtWidget(tooltip_text, self, 3000)
            return
        
        # Verificar si estamos en la vista de explorador
        if not hasattr(self, 'file_tree_widget') or not hasattr(self, 'current_explorer_drive'):
            tooltip_text = "Esta función solo está disponible en la vista de explorador"
            showTooltipAtWidget(tooltip_text, self, 3000)
            return
        
        if not self.license_active:
            print("Licencia no activa")
            tooltip_text = "Por favor, active su licencia para usar esta función"
            showTooltipAtWidget(tooltip_text, self, 3000)
            return
        
        destination_drive = get_drive_letter(self.current_drive)
        print(f"Unidad de destino: {destination_drive}")
        if not destination_drive:
            print("Destino no válido")
            tooltip_text = "Por favor, seleccione un disco de destino válido"
            showTooltipAtWidget(tooltip_text, self, 3000)
            return
        
        try:
            if os.path.exists(destination_drive):
                _, total, free = shutil.disk_usage(destination_drive)
            else:
                print(f"La unidad {destination_drive} no está accesible")
                return
        except OSError as e:
            print(f"Error al acceder a la unidad {destination_drive}: {e}")
            return
        
        free_space = free - 1024 * 1024 * 100  # Dejar 100 MB de margen
        print(f"Espacio libre en destino: {free_space / (1024 * 1024 * 1024):.2f} GB")
        
        selected_items = self.file_tree_widget.selectedItems()
        if not selected_items:
            print("No hay ítems seleccionados")
            tooltip_text = "Selección no válida. Por favor, seleccione al menos un archivo."
            showTooltipAtWidget(tooltip_text, self, 3000)
            return
        current_directory = self.current_drive
        print(f"Directorio actual: {current_directory}")
        last_selected_file = selected_items[-1].text(0)
        print(f"Último archivo seleccionado: {last_selected_file}")
        
        # Verificar si el archivo seleccionado es un directorio
        is_dir = os.path.isdir(os.path.join(current_directory, last_selected_file))
        if is_dir:
            print(f"El elemento seleccionado es un directorio: {last_selected_file}")
            tooltip_text = "Por favor, seleccione un archivo (no una carpeta)"
            showTooltipAtWidget(tooltip_text, self, 3000)
            return
        
        # Borrar TODOS los archivos seleccionados si no se presionó Ctrl
        if not ctrl_pressed:
            print("Tecla Control NO presionada. Se borrarán TODOS los archivos seleccionados.")
            for item in selected_items:
                current_file_name_to_delete = item.text(0)
                selected_file_path = os.path.join(current_directory, current_file_name_to_delete)
                try:
                    os.remove(selected_file_path)
                    print(f"Archivo {selected_file_path} borrado.")
                except Exception as e:
                    print(f"Error al borrar el archivo {selected_file_path}: {e}")
        else:
            print("Tecla Control presionada. No se borrarán los archivos seleccionados.")

        # Buscar archivos en el indexado
        files_to_queue = []
        source_drive = None
        current_subdirectory = None
        
        # Verificar si el archivo ya es un .txt
        is_txt_file = last_selected_file.lower().endswith('.txt')
        
        # Si el archivo seleccionado es un .txt, buscar también sin la extensión .txt
        search_names = [last_selected_file]
        if is_txt_file and last_selected_file.lower().endswith('.txt'):
            name_without_txt = last_selected_file[:-4]
            search_names.append(name_without_txt)
        
        for drive, indexed_files in self.indexed_data.items():
            found = False
            for search_name in search_names:
                for file_path in indexed_files:
                    if search_name in file_path:
                        source_drive = drive
                        current_subdirectory = os.path.dirname(file_path)
                        start_index = indexed_files.index(file_path)
                        if n == 0:
                            files_to_queue = [f for f in indexed_files[start_index + 1:] if os.path.dirname(f) == current_subdirectory]
                        else:
                            files_to_queue = [f for f in indexed_files[start_index + 1:start_index + 1 + n] if os.path.dirname(f) == current_subdirectory]
                        found = True
                        break
                if found:
                    break
            if found:
                break
        
        files_to_queue = list(dict.fromkeys(files_to_queue))
        if not files_to_queue:
            print("No se encontraron archivos para agregar")
            tooltip_text = f"No se encontraron archivos para agregar después de {last_selected_file}."
            showTooltipAtWidget(tooltip_text, self, 3000)
            return
        
        # Obtener la extensión del archivo actual (sin .txt si es un archivo .txt)
        current_file = last_selected_file
        if is_txt_file and current_file.lower().endswith('.txt'):
            current_file = current_file[:-4]
        current_ext = self.get_file_extension(current_file)
        
        # Filtrar archivos por extensión
        files_to_process = []
        extension_dialog_shown = False
        for file_name in files_to_queue:
            next_ext = self.get_file_extension(file_name)
            if next_ext != current_ext and not extension_dialog_shown:
                dialog = ExtensionDiffDialog(
                    last_selected_file,
                    os.path.basename(file_name),
                    current_ext,
                    next_ext,
                    self
                )
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    files_to_process.append(file_name)
                    extension_dialog_shown = True  # Marcar que ya se mostró el diálogo
                else:
                    break
            else:
                files_to_process.append(file_name)
        
        # Usar files_to_process en lugar de files_to_queue
        files_to_queue = files_to_process
        
        if not files_to_queue:
            print("No se procesarán archivos debido a diferencia de extensión")
            tooltip_text = "No se procesarán archivos debido a diferencia de extensión"
            showTooltipAtWidget(tooltip_text, self, 3000)
            return
        
        # Guardar el estado actual de la vista antes de cualquier operación
        current_scroll_position = self.file_tree_widget.verticalScrollBar().value()
        selected_items = [item.text(0) for item in self.file_tree_widget.selectedItems()]
        
        # Desactivar actualizaciones visuales durante el proceso
        self.file_tree_widget.setUpdatesEnabled(False)
        print(f"Disco de origen: {source_drive}")
        print(f"Subdirectorio actual: {current_subdirectory}")
        self.initialize_copy_structures(destination_drive)
        total_size_added = 0
        files_added = 0
        files_skipped = 0  # Añadido contador de archivos omitidos
        
        # Procesar los archivos para ver si hay algo que copiar
        for file_name in files_to_queue:
            full_source_path = os.path.join(source_drive, file_name)
            relative_path = os.path.relpath(file_name, current_subdirectory)
            
            # Verificar si el archivo tiene extensión .txt y procesarlo
            base_name = os.path.basename(relative_path)
            if base_name.lower().endswith('.txt'):
                # Verificar si tiene una extensión reconocida antes del .txt
                name_without_txt = base_name[:-4]
                last_dot_pos = name_without_txt.rfind('.')
                if last_dot_pos != -1:
                    possible_ext = name_without_txt[last_dot_pos:].lower()
                    common_extensions = [
                        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.mpg', '.mpeg',
                        '.mp3', '.wav', '.ogg', '.flac', '.aac', '.wma',
                        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp',
                        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                        '.zip', '.rar', '.7z', '.tar', '.gz'
                    ]
                    if any(possible_ext == ext for ext in common_extensions):
                        # Usar el nombre sin .txt
                        relative_path = os.path.join(os.path.dirname(relative_path), name_without_txt)
                        print(f"Usando nombre sin .txt: {relative_path}")
            
            destination_path = os.path.join(current_directory, relative_path)
            print(f"Procesando: {full_source_path} -> {destination_path}")
            
            # NUEVA VERIFICACIÓN: Comprobar si el archivo ya existe con el mismo tamaño
            if os.path.exists(destination_path) and os.path.exists(full_source_path):
                try:
                    source_size = os.path.getsize(full_source_path)
                    dest_size = os.path.getsize(destination_path)
                    if source_size == dest_size:
                        print(f"Archivo ya existe con el mismo tamaño: {destination_path}")
                        files_skipped += 1
                        continue  # Saltar este archivo
                except Exception as e:
                    print(f"Error verificando tamaño: {e}")
            
            if os.path.exists(full_source_path):
                file_size = os.path.getsize(full_source_path)
                if total_size_added + file_size > free_space:
                    print(f"No hay suficiente espacio para agregar {file_name}")
                    break
                
                queue_key = (full_source_path, destination_path)
                if queue_key not in self.files_in_queue:
                    self.queues[destination_drive].put(queue_key)
                    self.files_in_queue.add(queue_key)
                    total_size_added += file_size
                    files_added += 1
                
                if n > 0 and files_added >= n:
                    break
            else:
                print(f"Advertencia: El archivo {full_source_path} no existe.")
        
        # Solo crear el archivo TXT si se agregaron archivos nuevos y no es un archivo TXT
        if files_added > 0 and not is_txt_file:
            # Verificar si ya existe el archivo TXT para evitar duplicados
            txt_file_name = f"{last_selected_file}.txt"
            txt_file_path = os.path.join(current_directory, txt_file_name)
            
            if not os.path.exists(txt_file_path):
                # Usar el creador de TXT buffered para mejor rendimiento y consistencia
                self.txt_creator.create_txt_file_buffered(last_selected_file)
                print(f"Archivo TXT creado para: {last_selected_file}")
            else:
                print(f"El archivo TXT ya existe: {txt_file_path}")
        
        # Actualizar la interfaz y mostrar mensaje de éxito si se agregaron archivos
        if files_added > 0:
            self.update_total_size(destination_drive, total_size_added)
            print(f"Tamaño total añadido a {destination_drive}: {total_size_added / (1024 * 1024 * 1024):.2f} GB")
            
            # Actualizar la apariencia de los elementos de la lista
            saved_mode = self.config.get('size_mode', 'Normal')
            mode = self.config_window.size_modes[saved_mode]
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if destination_drive in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget:
                        widget.setFixedHeight(mode["height"])
                        item.setSizeHint(QSize(item.sizeHint().width(), mode["height"]))
                        self.list_widget.setIconSize(QSize(mode["icon"], mode["icon"]))
                        progress_bar = widget.progress_bar
                        progress_bar.setFixedHeight(mode["progress"])
                        progress_bar.setGeometry(0, 0, widget.width(), mode["progress"])
                        progress_bar.show()
                        file_progress_bar = widget.file_progress_bar
                        file_progress_bar.setFixedHeight(mode["file"])
                        file_y = mode["file_offset"]
                        file_progress_bar.setGeometry(0, file_y, widget.width(), mode["file"])
                        file_progress_bar.show()
                        font = QFont()
                        font.setBold(True)
                        font.setPointSize(mode["font"])
                        style = f"color: white; font-size: {mode['font']}px;"
                        for label in [widget.volume_label, widget.size_label, widget.speed_label]:
                            label.setFont(font)
                            label.setStyleSheet(style)
                    break
            self.start_copy_thread(destination_drive)
            self.update_progress_signal.emit(destination_drive, 0)
            self.update_file_progress_signal.emit(destination_drive, 0)
            self.update_speed_signal.emit()
            tooltip_text = f"SE HAN AGREGADO {files_added} ARCHIVOS A LA COLA DE COPIA"
            if files_skipped > 0:
                tooltip_text += f" (SE OMITIERON {files_skipped} ARCHIVOS QUE YA EXISTEN)"
            showTooltipAtWidget(tooltip_text, self, 3000)
        else:
            print("NO SE AGREGARON ARCHIVOS NUEVOS")
            tooltip_text = "NO SE AGREGARON ARCHIVOS NUEVOS"
            if files_skipped > 0:
                tooltip_text += f" (TODOS LOS ARCHIVOS YA EXISTEN)"
            showTooltipAtWidget(tooltip_text, self, 3000)
        print(f"Archivos en cola para {destination_drive}: {self.queues[destination_drive].qsize()}")
        print(f"Tamaño total a copiar en {destination_drive}: {self.total_sizes[destination_drive] / (1024 * 1024 * 1024):.2f} GB")
        self.update_drive_contents_without_flicker(current_scroll_position, selected_items)
    
    def update_drive_contents_without_flicker(self, scroll_position=None, selected_items=None):
        """Actualiza el contenido del directorio actual sin parpadeo"""
        if not hasattr(self, 'file_tree_widget') or not self.file_tree_widget.isVisible():
            return
        try:
            self.file_tree_widget.setUpdatesEnabled(False)
            if scroll_position is None:
                scroll_position = self.file_tree_widget.verticalScrollBar().value()
            if selected_items is None:
                selected_items = [item.text(0) for item in self.file_tree_widget.selectedItems()]
            
            # Obtener la lista actual de elementos
            current_items = {}
            for i in range(self.file_tree_widget.topLevelItemCount()):
                item = self.file_tree_widget.topLevelItem(i)
                if item.text(0) not in ["─" * 50]:  # Ignorar separadores
                    current_items[item.text(0)] = item
            
            # Obtener la lista actualizada de archivos y carpetas
            updated_entries = []
            try:
                with os.scandir(self.current_drive) as scanner:
                    for entry in scanner:
                        try:
                            # Filtrar archivos del sistema y ocultos
                            if (entry.name.startswith('$') or 
                                entry.name.startswith('.') or
                                entry.name in ['System Volume Information', '$RECYCLE.BIN', 'Config.Msi', 'Recovery', '$WinREAgent'] or
                                bool(os.stat(entry.path).st_file_attributes & 0x2)):
                                continue
                            if not os.access(entry.path, os.R_OK):
                                continue
                            updated_entries.append(entry)
                        except (PermissionError, OSError):
                            continue
            except Exception as e:
                print(f"Error al escanear directorio: {e}")
            updated_entries.sort(key=lambda x: x.name.lower())
            for entry in updated_entries:
                if entry.name in current_items:
                    # Actualizar elemento existente
                    item = current_items[entry.name]
                    if not entry.is_dir():
                        try:
                            size = os.path.getsize(entry.path)
                            item.setText(1, self.format_size(size))
                        except OSError:
                            item.setText(1, "Error")
                else:
                    # Agregar nuevo elemento
                    item = QTreeWidgetItem([entry.name])
                    is_directory = entry.is_dir()
                    if not is_directory:
                        try:
                            size = os.path.getsize(entry.path)
                            item.setText(1, self.format_size(size))
                        except OSError:
                            item.setText(1, "Error")
                    else:
                        item.setText(1, "")  # Directorios sin tamaño
                    item.setTextAlignment(1, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                    item.setData(0, Qt.ItemDataRole.UserRole, is_directory)
                    item.setData(0, Qt.ItemDataRole.UserRole + 1, entry.path)
                    icon = self.icon_manager.get_icon_for_file(entry.path)
                    item.setIcon(0, icon)
                    font = item.font(0)
                    font.setBold(True)
                    item.setFont(0, font)
                    item.setFont(1, font)
                    item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                    self.file_tree_widget.addTopLevelItem(item)
            # Eliminar elementos que ya no existen
            updated_names = [entry.name for entry in updated_entries]
            for name, item in list(current_items.items()):
                if name not in updated_names:
                    index = self.file_tree_widget.indexOfTopLevelItem(item)
                    if index >= 0:
                        self.file_tree_widget.takeTopLevelItem(index)
            
            # Restaurar selección
            for i in range(self.file_tree_widget.topLevelItemCount()):
                item = self.file_tree_widget.topLevelItem(i)
                if item.text(0) in selected_items:
                    item.setSelected(True)
            QTimer.singleShot(10, lambda: self.file_tree_widget.verticalScrollBar().setValue(scroll_position))
            self.file_tree_widget.setUpdatesEnabled(True)
        except Exception as e:
            print(f"Error en update_drive_contents_without_flicker: {e}")
            import traceback
            traceback.print_exc()
            # Asegurar que las actualizaciones visuales se reactiven
            self.file_tree_widget.setUpdatesEnabled(True)

    def initialize_copy_structures(self, drive_letter):
        if drive_letter not in self.queues:
            self.queues[drive_letter] = queue.Queue()
        if drive_letter not in self.copy_start_time:
            self.copy_start_time[drive_letter] = time.time()
        if drive_letter not in self.copy_bytes_copied:
            self.copy_bytes_copied[drive_letter] = 0
        if drive_letter not in self.total_sizes:
            self.total_sizes[drive_letter] = 0

    def start_copy_thread(self, drive_letter):
        if drive_letter not in self.copy_start_time: # Asegúrate de que copy_start_time esté inicializado para el drive_letter
            self.copy_start_time[drive_letter] = time.time()  # Inicializa el tiempo de inicio
        if drive_letter not in self.threads or not self.threads[drive_letter].is_alive():
            thread = threading.Thread(target=self.process_file_queue, args=(drive_letter,))
            thread.do_run = True
            thread.start()
            self.threads[drive_letter] = thread
            print(f"Nuevo hilo iniciado para {drive_letter}")

    def update_progress_bar_color(self, color):
        """Actualiza el color de la barra de progreso"""
        self.config['progress_bar_color'] = color
        self.progress_bar_color = color
        save_config(self.config)
        self.update_all_progress_bars() # Actualizar barras de progreso en la lista
        
        # Actualizar barras mini
        for mini_bar in self.mini_progress_bars.values():
            mini_bar.set_color(color)

    def update_all_progress_bars(self):
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            widget = self.list_widget.itemWidget(item)
            if isinstance(widget, CustomListWidgetItem):
                widget.update_progress_bar_color(self.progress_bar_color)

    def update_date_time(self):
        current_datetime = QDateTime.currentDateTime()
        locale = QLocale(QLocale.Language.English, QLocale.Country.UnitedStates)
        time_string = locale.toString(current_datetime.time(), "hh:mm AP")
        self.date_time_label.setText(time_string)
        date_string = locale.toString(current_datetime.date(), "yyyy-MM-dd")
        full_datetime = f"{date_string} {time_string}"
        self.date_time_label.setToolTip(full_datetime)
    
    def set_rounded_corners(self, hwnd):
        DWMWA_WINDOW_CORNER_PREFERENCE = 33
        DWMWCP_ROUND = 2
        windll.dwmapi.DwmSetWindowAttribute(hwnd, DWMWA_WINDOW_CORNER_PREFERENCE, byref(c_int(DWMWCP_ROUND)), sizeof(c_int))
    
    def update_queue_progress_bar(self, value):
        self.queue_progress_bar.setValue(value)

    def create_empaketado(self):
        create_empaketado(self)
    
    def ocultar_icono_expulsion(self, item):
        widget = self.list_widget.itemWidget(item)
        widget.eject_icon_button.hide()

    def expulsar_disco(self):
        selected_items = self.list_widget.selectedItems()
        if not selected_items:
            print("No hay ningún disco seleccionado.")
            return
        for item in selected_items:
            drive_letter = item.text().split(' ')[-1].strip('()')
            print(f"Expulsar button clicked for drive {drive_letter}")

            # Verificar si el disco está marcado como 'No expulsar'
            try:
                config = load_config()
                discos_no_expulsar = config.get('discos_no_expulsar', [])
                volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                disco_actual = f"{volume_name} ({drive_letter})"
                if disco_actual in discos_no_expulsar:
                    print(f"El disco {disco_actual} está marcado como 'No expulsar'")
                    # Usar QToolTip en lugar de show_tooltip
                    center_pos = self.mapToGlobal(self.rect().center())
                    QToolTip.showText(center_pos, f"El disco {disco_actual} está marcado como 'No expulsar'", self)
                    QTimer.singleShot(3000, QToolTip.hideText)
                    continue
            except Exception as e:
                print(f"Error verificando discos_no_expulsar: {e}")

            if drive_letter in self.disks_to_eject_after_copy:
                print(f"Cancelando expulsión del disco {drive_letter}")
                del self.disks_to_eject_after_copy[drive_letter]
                widget = self.list_widget.itemWidget(item)
                if widget and hasattr(widget, 'eject_icon_button'):
                    widget.eject_icon_button.hide()
            elif self.is_copying(drive_letter):
                print(f"El disco {drive_letter} está copiando. Se expulsará al finalizar la copia.")
                widget = self.list_widget.itemWidget(item)
                if widget and hasattr(widget, 'eject_icon_button'):
                    icono_expulsar, _ = icono_expulsar_usb(20)
                    widget.eject_icon_button.setIcon(icono_expulsar)
                    shadow = QGraphicsDropShadowEffect()
                    shadow.setBlurRadius(8)
                    shadow.setXOffset(0)
                    shadow.setYOffset(2)
                    shadow.setColor(QColor(0, 0, 0, 120))
                    widget.eject_icon_button.setGraphicsEffect(shadow)
                    widget.eject_icon_button.show()
                    self.disks_to_eject_after_copy[drive_letter] = item
            else:
                widget = self.list_widget.itemWidget(item)
                if widget and hasattr(widget, 'eject_icon_button'):
                    icono_expulsar, _ = icono_expulsar_usb(20)
                    widget.eject_icon_button.setIcon(icono_expulsar)
                    shadow = QGraphicsDropShadowEffect()
                    shadow.setBlurRadius(8)
                    shadow.setXOffset(0)
                    shadow.setYOffset(2)
                    shadow.setColor(QColor(0, 0, 0, 120))
                    widget.eject_icon_button.setGraphicsEffect(shadow)
                    widget.eject_icon_button.show()
                QApplication.processEvents()
                task = EjectDriveTask(drive_letter, self)
                self.thread_pool.start(task)

    def eject_drive(self, drive_letter):
        try:
            removedrive_path = os.path.join(os.path.dirname(__file__), 'RemoveDrive.exe')
            
            # Ocultar botón de expulsión
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if hasattr(widget, 'eject_icon_button'):
                        widget.eject_icon_button.hide()
                    break
            
            # Configurar para ocultar la consola
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            # Primer intento de expulsión
            try:
                result = subprocess.run(
                    [removedrive_path, drive_letter, '-L', '-h', '-a', '-i', '-f', '-tm', '-w:1000'],
                    capture_output=True,
                    text=True,
                    timeout=15,
                    startupinfo=startupinfo  # Ocultar consola
                )
                if "success" in result.stdout.lower():
                    print(f"Disco {drive_letter} expulsado correctamente.")
                    self.update_volume_list(self.worker.get_connected_volumes())
                    self.remove_disk_from_view(drive_letter)
                    return  # Salir si fue exitoso
            except subprocess.TimeoutExpired:
                print(f"Primer intento de expulsión para {drive_letter} excedió el tiempo de espera")
            
            # Segundo intento más agresivo
            try:
                result = subprocess.run(
                    [removedrive_path, drive_letter, '-L', '-f', '-tm', '-i', '-d', '-vhd'],
                    capture_output=True,
                    text=True,
                    timeout=15,
                    startupinfo=startupinfo  # Ocultar consola
                )
                if "success" in result.stdout.lower():
                    print(f"Disco {drive_letter} expulsado correctamente en segundo intento")
                    self.update_volume_list(self.worker.get_connected_volumes())
                    self.remove_disk_from_view(drive_letter)
                    return  # Salir si fue exitoso
            except subprocess.TimeoutExpired:
                print(f"Segundo intento de expulsión para {drive_letter} excedió el tiempo de espera")
            
            # Si llegamos aquí, significa que fallaron ambos intentos
            print(f"No se pudo expulsar el disco {drive_letter} después de dos intentos")
            QMessageBox.warning(self, "Error", f"No se pudo expulsar el disco {drive_letter}.\nAsegúrate de que no hay archivos en uso.")
        except Exception as e:
            print(f"Error inesperado al expulsar el disco {drive_letter}: {e}")

    def is_copying(self, drive_letter):
        if drive_letter not in self.queues:
            return False
        return self.queues[drive_letter].qsize() > 0 or self.copy_bytes_copied.get(drive_letter, 0) < self.total_sizes.get(drive_letter, 0)
    
    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.window_resizer.update_positions()
        self.transparent_widget.setGeometry(self.rect())
        update_badge_positions(self)

    def create_new_folder(self):
        from CREAR_CARPETA import create_new_folder
        create_new_folder(self)
    def create_new_files(self):
        from CREAR_CARPETA import create_new_file
        create_new_file(self)
   
    def get_volume_name(self, drive_letter):
        """Obtiene el nombre del volumen para un disco específico"""
        try:
            # Asegurarnos de que drive_letter sea string
            drive_letter = str(drive_letter) if drive_letter is not None else ""
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if not item:
                    continue
                
                # Obtener el texto del item de forma segura
                item_text = item.text()
                if item_text is None:
                    continue
                    
                item_text = str(item_text)
                
                # Verificar si la letra del disco está en el texto del item
                if drive_letter and drive_letter in item_text:
                    widget = self.list_widget.itemWidget(item)
                    if not widget or not hasattr(widget, 'volume_label'):
                        continue
                        
                    # Obtener el texto del volume_label de forma segura
                    volume_label = widget.volume_label
                    if volume_label is None:
                        return f"Disco ({drive_letter})"
                        
                    volume_text = volume_label.text()
                    if volume_text is None:
                        return f"Disco ({drive_letter})"
                    
                    volume_text = str(volume_text)
                    
                    # Procesar el texto del volumen
                    if f" ({drive_letter})" in volume_text:
                        parts = volume_text.split(f" ({drive_letter})")
                        if parts and parts[0]:
                            return parts[0]
                    
                    # Si no tiene el formato esperado, devolver el texto completo
                    return volume_text.split('\n')[0] if '\n' in volume_text else volume_text
            return f"Disco ({drive_letter})"
        except Exception as e:
            print(f"Error al obtener el nombre del volumen: {str(e)}")
            import traceback
            traceback.print_exc()  # Esto imprimirá la traza completa del error
            return f"Disco ({drive_letter})"
    
    def get_current_progress(self, drive_letter):
        if drive_letter in self.total_sizes and drive_letter in self.copy_bytes_copied:
            total_size = self.total_sizes[drive_letter]
            copied_size = self.copy_bytes_copied[drive_letter]
            if total_size > 0:
                return int((copied_size / total_size) * 100)
        return 0

    def file_list_key_press_event(self, event):
        if event.key() == Qt.Key.Key_F2:
            selected_items = self.file_tree_widget.selectedItems()
            if len(selected_items) > 1:
                # Si hay múltiples archivos seleccionados, usar el diálogo de renombrado múltiple
                from RENOMBRADO_MULTIPLE import RenombrarMultipleDialog
                archivos_seleccionados = [item.text(0) for item in selected_items]
                dialog = RenombrarMultipleDialog(archivos_seleccionados, self)
                
                dialog.setModal(False)  # Hacer que el diálogo no sea modal
                dialog.show()
            elif len(selected_items) == 1:
                # Si solo hay un archivo seleccionado, usar el renombrado simple
                current_item = selected_items[0]
                self.file_tree_widget.editItem(current_item, 0)
        elif event.key() == Qt.Key.Key_Delete:
            confirm_delete_selected_items(self)
        # Detectar combinación Shift+Z para borrar archivos y crear TXT del último
        elif event.key() == Qt.Key.Key_Z and bool(QApplication.keyboardModifiers() & Qt.KeyboardModifier.ShiftModifier):
            selected_items = self.file_tree_widget.selectedItems()
            if selected_items:
                # Guardar referencia al último archivo seleccionado
                last_selected_file = selected_items[-1].text(0)
                
                # Invocar el diálogo de borrado
                from BORRADO import confirm_delete_selected_items
                confirm_delete_selected_items(self)
                
                # Crear el archivo TXT para el último archivo seleccionado
                if not last_selected_file.lower().endswith('.txt'):
                    # Usar el creador de TXT buffered para mejor rendimiento
                    self.txt_creator.create_txt_file_buffered(last_selected_file)
                    print(f"Archivo TXT creado para: {last_selected_file}")
        # Usar la tecla Backspace para volver atrás
        elif event.key() == Qt.Key.Key_Backspace:
            self.go_back()
        # Usar la tecla Enter para navegar a la carpeta seleccionada
        elif event.key() in (Qt.Key.Key_Enter, Qt.Key.Key_Return):
            selected_items = self.file_tree_widget.selectedItems()
            if len(selected_items) == 1:
                file_path = os.path.join(self.current_drive, selected_items[0].text(0))
                if os.path.isfile(file_path):
                    os.startfile(file_path)
                elif os.path.isdir(file_path):
                    self.show_drive_contents(file_path)
        else:
            super(QTreeWidget, self.file_tree_widget).keyPressEvent(event)
    
    def on_item_double_clicked(self, item, column):
        file_path = os.path.join(self.current_drive, item.text(0))
        if os.path.isfile(file_path):
            os.startfile(file_path)
        elif os.path.isdir(file_path):
            self.show_drive_contents(file_path)
    
    def get_file_size(self, path):  # ESTA FUNCION REVISARLA NO SE QUIEN LA LLAMA
        try:
            if os.path.isfile(path):
                size = os.path.getsize(path)
                for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
                    if size < 1024:
                        item = QTreeWidgetItem()
                        item.setTextAlignment(1, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)# Alinear a la derecha la columna de tamaño
                        return f"{size:.2f} {unit}"
                    size /= 1024
                return f"{size:.2f} PB"
            else:  
                return ""  
        except (OSError, PermissionError):
            return "0 B"

    def rename_file_or_folder(self, item, new_name):
        old_name = item.text(0)
        if new_name == old_name:
            print(f"No se realizó ningún cambio: '{old_name}'")
            return
        old_path = os.path.join(self.current_drive, old_name)
        new_path = os.path.join(self.current_drive, new_name)
        try:
            if os.path.exists(new_path) and old_path.lower() != new_path.lower():
                raise OSError("Ya existe un archivo o carpeta con ese nombre.")
            os.rename(old_path, new_path)
            if os.path.exists(new_path):
                item.setText(0, new_name)
                item.setData(0, Qt.ItemDataRole.UserRole, new_name)
                print(f"Renombrado exitoso: '{old_name}' a '{new_name}'")
            else:
                raise OSError("El archivo no se renombró correctamente.")
        except OSError as e:
            print(f"Error al renombrar el archivo: {e}")
            QMessageBox.warning(self, "Error", f"No se pudo renombrar el archivo: {e}")
            item.setText(0, old_name)
            item.setData(0, Qt.ItemDataRole.UserRole, old_name)

    def show_rename_dialog(self, folder_name=None):
        # Verificar si ya hay un diálogo abierto
        if hasattr(self, 'rename_dialog') and self.rename_dialog.isVisible():
            self.rename_dialog.raise_()
            self.rename_dialog.activateWindow()
            return

        # Si estamos en la vista del explorador
        if self.current_explorer_drive:
            if hasattr(self, 'file_tree_widget'):
                selected_items = self.file_tree_widget.selectedItems()
                if len(selected_items) > 1:
                    # Si hay múltiples archivos seleccionados, usar el diálogo de renombrado múltiple
                    from RENOMBRADO_MULTIPLE import RenombrarMultipleDialog
                    archivos_seleccionados = [item.text(0) for item in selected_items]
                    dialog = RenombrarMultipleDialog(archivos_seleccionados, self)
                    dialog.show()
                    return
                elif len(selected_items) == 1:
                    # Renombrado simple para un solo archivo
                    item = selected_items[0]
                    current_name = item.text(0)
                    is_file = '.' in current_name 
                    if is_file:
                        name, ext = os.path.splitext(current_name)
                    else:
                        name, ext = current_name, ''
                    self.rename_dialog = CustomRenameDialog(self, name)
                    self.rename_dialog.setWindowTitle("Renombrar Archivo/Carpeta")
                    if self.rename_dialog.exec() == QDialog.DialogCode.Accepted:
                        new_name = self.rename_dialog.get_new_name()
                        if new_name and new_name != name:
                            full_new_name = new_name + ext if is_file else new_name
                            self.rename_file_or_folder(item, full_new_name)
            else:
                print("Error: El explorador de archivos no está inicializado correctamente")
                return
        else:
            # Renombrado de discos/volúmenes
            selected_items = self.list_widget.selectedItems()
            if selected_items:
                item = selected_items[0]
                widget = self.list_widget.itemWidget(item)
                if widget and hasattr(widget, 'volume_label'):
                    current_text = widget.volume_label.text()
                    drive_letter = current_text.split(' ')[-1].strip('()')
                    current_name = folder_name if folder_name else current_text.split(f' ({drive_letter})')[0]
                    current_icon = item.icon()
                    icon_size = QSize(30, 30)
                    pixmap = current_icon.pixmap(icon_size)
                    temp_icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'temp_icon.png')
                    pixmap.save(temp_icon_path)
                    self.rename_dialog = CustomRenameDialog(self, current_name, temp_icon_path, is_volume=True, is_alias=False)
                    self.rename_dialog.setWindowTitle("Renombrar Volumen")
                    if self.rename_dialog.exec() == QDialog.DialogCode.Accepted:
                        new_name = self.rename_dialog.get_new_name()
                        if new_name and new_name != current_name:
                            self.rename_disk(item, new_name, drive_letter)
                    try:
                        if os.path.exists(temp_icon_path):
                            os.remove(temp_icon_path)
                    except Exception as e:
                        print(f"Error al eliminar icono temporal: {e}")
    
    def get_drive_type(self, drive_letter):
        """Determina el tipo de unidad basado en GetDriveType"""
        drive_type = ctypes.windll.kernel32.GetDriveTypeW(f"{drive_letter}\\")
        if drive_type == 2:  # DRIVE_REMOVABLE
            return "Unidad extraíble (USB)"
        elif drive_type == 3:  # DRIVE_FIXED
            if drive_letter.upper() == "C:":
                return "Disco del sistema"
            return "Disco duro interno"
        elif drive_type == 4:  # DRIVE_REMOTE
            return "Unidad de red"  # Cambiado para identificar específicamente unidades de red
        elif drive_type == 5:  # DRIVE_CDROM
            return "Unidad de CD/DVD"
        elif drive_type == 6:  # DRIVE_RAMDISK
            return "Disco RAM"
        else:
            return "Desconocido"

    def get_disk_icon_path(self, drive_letter):
        try:
            drive_type = self.worker.get_drive_type(f"{drive_letter}\\")
            if drive_letter.upper() == "C:":
                from CREAR import create_windows_icon
                return create_windows_icon()
            elif drive_type == "Unidad extraíble (USB)":
                from CREAR import create_usb_icon
                return create_usb_icon()
            elif drive_type == "Disco duro interno":
                from CREAR import create_internal_hdd_icon
                return create_internal_hdd_icon()
            elif drive_type == "Disco duro externo":
                from CREAR import create_external_hdd_icon
                return create_external_hdd_icon()
            elif drive_type == "Unidad de red" or drive_type == "red":  # Añadir esta condición
                from CREAR import icono_RED
                return icono_RED()
            else:
                return os.path.join(os.path.dirname(__file__), 'iconos', 'OTRAS.png')
        except Exception as e:
            print(f"Error obteniendo icono del disco: {e}")
            return os.path.join(os.path.dirname(__file__), 'iconos', 'OTRAS.png')
    
    def rename_disk(self, item, new_name, drive_letter):
        old_name = item.text()
        try:
            if self.set_volume_label(drive_letter, new_name): # Intentar renombrar el disco a nivel del sistema
                full_new_name = f"{new_name} ({drive_letter})"
                widget = self.list_widget.itemWidget(item)
                if widget and hasattr(widget, 'volume_label'):
                    widget.volume_label.setText(full_new_name)
                print(f"Disco renombrado: '{old_name}' a '{full_new_name}'")
                item.setText(full_new_name) # Actualizar el texto del item en la lista
                self.update_volume_list(self.worker.get_connected_volumes()) # Actualizar la vista del explorador
            else:
                raise Exception("No se pudo cambiar el nombre del volumen")
        except Exception as e:
            print(f"Error al renombrar el disco: {e}")
            QMessageBox.warning(self, "Error", f"No se pudo renombrar el disco: {e}")
            if widget and hasattr(widget, 'volume_label'): # Revertir cambios en la interfaz si el renombrado falla
                widget.volume_label.setText(old_name)

    def set_volume_label(self, drive_letter, new_label):
        if os.name == 'nt':  # Windows
            try:
                ctypes.windll.kernel32.SetVolumeLabelW(f"{drive_letter}\\", new_label)
                return True
            except Exception as e:
                print(f"Error al cambiar el nombre del volumen en Windows: {e}")
                return False
    
    def file_list_mouse_press_event(self, event): # MANEJA LOS EVENTOS DEL CLIC DERECHO EN LA VISTA SHOW_DRIVE_CONTENTS
        try:
            if event.button() == QtCore.Qt.MouseButton.BackButton:
                print("Botón lateral (Atrás) presionado en el miniexplorer")
                self.go_back()
                event.accept()
                return
            item = self.file_tree_widget.itemAt(event.pos())
            if not item:
                self.file_tree_widget.clearSelection()
                self.file_tree_widget.clearFocus()
                event.accept()
                return
            if event.button() == QtCore.Qt.MouseButton.MiddleButton:
                if item:
                    file_name = item.text(0)
                    file_path = os.path.join(self.current_drive, file_name)
                    if os.path.isfile(file_path):
                        # Usar la función play_drag_sound de CREAR_TXT.py
                        from CREAR_TXT import play_drag_sound
                        play_drag_sound(sound_type="txt")
                        
                        # Crear el archivo TXT
                        self.txt_creator.create_txt_file_buffered(file_name)
                event.accept()
                return
            if event.button() == QtCore.Qt.MouseButton.RightButton:
                if item:
                    file_name = item.text(0)
                    name_without_extension = os.path.splitext(file_name)[0]
                    QApplication.clipboard().setText(name_without_extension)
                    print(f"Nombre '{name_without_extension}' copiado al portapapeles.")
                    label = QLabel("NOMBRE COPIADO", self)
                    label.setStyleSheet("""
                        QLabel {
                            color: white;
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #666666, stop:1 #444444);
                            padding: 2px 5px;
                            font-weight: bold;
                            font-size: 14px;
                            border-radius: 10px;
                            border: 1px solid #888888;
                        }
                    """)

                    shadow = QGraphicsDropShadowEffect(self)
                    shadow.setBlurRadius(8)
                    shadow.setColor(QColor(0, 0, 0, 180))
                    shadow.setOffset(2, 2)
                    label.setGraphicsEffect(shadow)

                    label.adjustSize()
                    cursor_pos = event.globalPosition().toPoint()
                    local_pos = self.mapFromGlobal(cursor_pos)
                    label.move(local_pos.x() - label.width() // 2, local_pos.y() - label.height())
                    label.show()
                    QTimer.singleShot(1000, lambda: label.deleteLater()) # Simplemente ocultar y eliminar después de 1 segundo
                event.accept()
                return
            QtWidgets.QTreeWidget.mousePressEvent(self.file_tree_widget, event)
        except Exception as e:
            print(f"Error en file_list_mouse_press_event: {e}")
            QtWidgets.QTreeWidget.mousePressEvent(self.file_tree_widget, event)

    @QtCore.pyqtSlot(str)
    def update_ui_with_new_file(self, txt_file_name):
        """Delegamos la actualización de UI al TxtFileCreator"""
        # Verificar si el archivo ya está en la vista antes de delegarlo
        for i in range(self.file_tree_widget.topLevelItemCount()):
            item = self.file_tree_widget.topLevelItem(i)
            if item.text(0) == txt_file_name:
                print(f"El archivo {txt_file_name} ya está en la vista, no se agregará de nuevo")
                return
        
        # Si no está en la vista, delegamos al TxtFileCreator
        self.txt_creator.update_ui_with_new_file(txt_file_name)

    def setup_file_list_connections(self):
        try:
            self.file_tree_widget.itemChanged.disconnect()
        except TypeError:
            pass
        # Agregar conexión para la selección
        self.file_tree_widget.itemSelectionChanged.connect(self.update_size_info)

    def update_size_info(self):
        selected_items = self.file_tree_widget.selectedItems()
        if not selected_items:
            self.size_info_label.hide()
            return
        total_size = 0
        for item in selected_items:
            try:
                size_text = item.text(1)  # Obtener el texto de la columna de tamaño
                # Si es un directorio (sin tamaño), continuar
                if not size_text:
                    continue
                    
                # Convertir el texto del tamaño a bytes
                size_parts = size_text.split()
                if len(size_parts) == 2:
                    size_value = float(size_parts[0])
                    unit = size_parts[1]
                    
                    # Convertir a bytes según la unidad
                    multipliers = {
                        'B': 1,
                        'KB': 1024,
                        'MB': 1024 ** 2,
                        'GB': 1024 ** 3,
                        'TB': 1024 ** 4
                    }
                    if unit in multipliers:
                        total_size += size_value * multipliers[unit]
            except Exception as e:
                print(f"Error procesando tamaño: {e}")
                
        # Si hay tamaño total, mostrar el widget
        if total_size > 0:
            formatted_size = self.format_size(total_size)
            # Mostrar cantidad de archivos y tamaño total
            text = f"{len(selected_items)} | {formatted_size}"
            self.size_info_label.setText(text)
            self.size_info_label.adjustSize()  # Ajustar el tamaño al contenido
            self.size_info_label.show()
            
            # Aplicar estilo con padding
            self.size_info_label.setStyleSheet("""
                QLabel {
                    color: white;
                    border-radius: 11px;
                    font-size: 12px;
                    font-weight: bold;  /* Añadido para texto en negrita */
                    padding: 5px 10px;
                    background-color: rgba(255, 255, 255, 0.1);
                    margin: 2px;
                }
            """)
            
            # Centrar horizontalmente
            labelWidth = self.size_info_label.width()
            labelX = (self.width() - labelWidth) // 2
            # Posicionar cerca del borde inferior
            labelY = self.height() - self.size_info_label.height() - 10
            self.size_info_label.move(labelX, labelY)
        else:
            self.size_info_label.hide()
    
    def go_back(self):
        try:
            print("Iniciando go_back...")
            current_path = self.current_drive
            parent_path = os.path.dirname(current_path)
            print(f"Ruta actual: {current_path}, Ruta padre: {parent_path}")
            self.file_tree_widget.clear()
            if parent_path == self.current_drive or parent_path == '':
                print("Volviendo a vista de discos...")
                QApplication.processEvents()  
                self.file_tree_widget.clear()
                self.file_tree_widget.hide()
                self.drive_info_label.clear()
                self.drive_info_label.hide()
                self.list_widget.show()
                self.nav_widget.hide()
                self.back_button.hide()
                self.new_folder.hide()
                self.new_empaketado.hide()
                self.cola_button.hide()
                self.botones.CLEAR.hide()
                self.botones.RENAME.hide()
                self.size_info_label.hide()
                self.current_explorer_drive = None
                self.current_drive = None
                for mini_bar in self.mini_progress_bars.values():
                    mini_bar.hide()
                self.volume_name_label.hide()
                QApplication.processEvents()  
                self.main_layout.update()
                print("Vista de discos restaurada")
            else:
                print(f"Navegando a directorio padre: {parent_path}")
                self.show_drive_contents(parent_path)
            self.update_current_directory_state()
            print("go_back completado exitosamente")
        except Exception as e:
            print(f"Error crítico en go_back: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def update_current_directory_state(self):
        if self.current_drive:
            drive_letter = self.current_drive[:2]
            self.indexed_files = self.indexed_data.get(drive_letter, [])
            print(f"Actualizando estado para {self.current_drive}")
            print(f"Archivos indexados: {len(self.indexed_files)}")
        else:
            self.indexed_files = []
    
    def update_license_status(self, status):
        self.license_active = status
        print(f"Estado de licencia actualizado: {self.license_active}")
    
    def check_license(self):
        try:
            if getattr(sys, 'frozen', False):
                exe_path = os.path.dirname(sys.executable)
            else:
                exe_path = os.path.dirname(os.path.abspath(__file__))
            license_path = os.path.join(exe_path, "license.dat")
            if not os.path.exists(license_path):
                print("No se encontró archivo de licencia")
                self.license_active = False
                self.disable_main_window()
                return False
            with open(license_path, "rb") as license_file:
                combined_data = license_file.read()
                key, encrypted = combined_data.split(b'|', 1)
                fernet = Fernet(key)
                decrypted = fernet.decrypt(encrypted).decode()
                
                # Verificar hardware primero
                hw_info, date_str = decrypted.split('||')
                parts = hw_info.split('|')
                
                # Obtener información actual del hardware
                serial_number, current_model, manufacturer = get_motherboard_info()
                current_disk_serial = get_disk_serial_number("C:")
                
                # Verificar coincidencia de hardware
                stored_model = parts[0]
                stored_disk_serial = parts[1] if len(parts) >= 2 else None
                
                # Verificar coincidencia de modelo
                model_match = current_model == stored_model
                
                # Verificar coincidencia de disco si está presente
                disk_match = False
                if stored_disk_serial and current_disk_serial:
                    disk_match = current_disk_serial == stored_disk_serial
                
                # La licencia es válida SOLO si coinciden AMBOS: el modelo Y el serial del disco
                hardware_match = model_match and disk_match
                if not hardware_match:
                    print("Hardware no coincide. Eliminando licencia.")
                    if not model_match:
                        print("El modelo de placa base no coincide.")
                    if not disk_match:
                        print("El serial del disco C: no coincide.")
                    os.remove(license_path)
                    self.license_active = False
                    self.disable_main_window()
                    return False
                
                # Si el hardware coincide, verificar fecha
                expiration_date = datetime.fromisoformat(date_str)
                if datetime.now() < expiration_date:
                    print("Licencia válida hasta:", expiration_date)
                    self.license_active = True
                    self.enable_main_window()
                    return True
                else:
                    print("La licencia ha expirado. Eliminando archivo.")
                    os.remove(license_path)
                    self.license_active = False
                    self.disable_main_window()
                    return False
        except Exception as e:
            print("Error al verificar la licencia:", str(e))
            import traceback
            traceback.print_exc()
            if os.path.exists(license_path):
                os.remove(license_path)
            self.license_active = False
            self.disable_main_window()
            return False

    def enable_main_window(self):
        """Habilita la ventana principal"""
        if hasattr(self, 'list_widget'):
            self.list_widget.setEnabled(True)
        print("Ventana principal habilitada")

    def disable_main_window(self):
        """Deshabilita solo la navegación dentro de los discos cuando la licencia es inválida"""
        if hasattr(self, 'file_tree_widget'):
            self.file_tree_widget.setEnabled(False)
        # No deshabilitamos el list_widget para que los discos sigan siendo visibles y seleccionables
        print("Navegación dentro de los discos deshabilitada")

    def on_file_item_double_clicked(self, item):
        if not self.license_active:
            self.check_license()  # Verificar la licencia nuevamente
        if not self.license_active:
            QMessageBox.warning(self, "Licencia no activa", "Por favor, active su licencia para usar esta función.")
            return
        try:
            path = os.path.join(self.current_drive, item.text(0))
            print(f"Elemento doble clic: {path} - Es directorio: {os.path.isdir(path)}")
            if os.path.isdir(path):
                self.file_tree_widget.clear() # Limpiar la vista antes de navegar al nuevo directorio
                QApplication.processEvents()
            else:
                print(f"Archivo seleccionado: {path}")
        except Exception as e:
            print(f"Error al hacer doble clic en el elemento {item.text(0)}: {e}")

    def open_settings(self):
        self.settings_window = TransparentWindow(self)
        self.settings_window.show()

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Enter:
            if hasattr(self, 'list_widget') and self.list_widget.isVisible():
                self.ventana_cola()
    
    def update_progress_bar(self, drive_letter, progress):
        """Actualiza la barra de progreso para un disco específico"""
        try:
            # Verificar si estamos en proceso de finalización de copia
            is_finishing = hasattr(self, '_finishing_copy') and drive_letter in self._finishing_copy
            
            # Evitar retrocesos en la barra de progreso solo cuando estamos finalizando
            if is_finishing and hasattr(self, 'max_progress_values') and drive_letter in self.max_progress_values:
                if progress < self.max_progress_values[drive_letter] and progress < 100:
                    # Solo permitir retrocesos si es para reiniciar la barra (progress = 0)
                    if progress != 0:
                        print(f"Evitando retroceso de barra durante finalización: {progress} < {self.max_progress_values[drive_letter]}")
                        return
                else:
                    self.max_progress_values[drive_letter] = max(progress, self.max_progress_values[drive_letter])
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget and hasattr(widget, 'progress_bar'):
                        # Evitar actualizaciones innecesarias si el valor no cambia
                        if widget.progress_bar.value() == progress:
                            return
                        
                        # Obtener el modo actual para configuraciones correctas
                        saved_mode = self.config.get('size_mode', 'Normal')
                        size_modes = {
                            "Normal": {"scale": 100, "height": 40, "font": 12, "icon": 30, "progress": 18, "file": 4, "file_offset": 28},
                            "Grande": {"scale": 100, "height": 50, "font": 13, "icon": 35, "progress": 18, "file": 4, "file_offset": 40}
                        }
                        mode = size_modes.get(saved_mode, size_modes["Normal"])
                        
                        # Asegurar que la barra mantiene su posición centrada
                        width = widget.width()
                        progress_width = int(width * 0.2)  # 20% del ancho total
                        x_pos = (width - progress_width) // 2
                        
                        # Posicionar la barra principal justo encima de la barra de archivos con 1px de separación
                        progress_y = mode["file_offset"] - mode["progress"] - 1
                        
                        # Actualizar geometría con la altura correcta según el modo
                        widget.progress_bar.setGeometry(x_pos, progress_y, progress_width, mode["progress"])
                        
                        # Animación suave para cambios grandes (cuando se añaden archivos)
                        current_value = widget.progress_bar.value()
                        if current_value > progress and (current_value - progress) > 10 and not is_finishing:
                            # Crear animación para transición suave
                            animation = QPropertyAnimation(widget.progress_bar, b"value")
                            animation.setDuration(300)  # 300ms de duración
                            animation.setStartValue(current_value)
                            animation.setEndValue(progress)
                            animation.start()
                        else:
                            # Actualizar directamente para cambios pequeños o incrementos
                            QMetaObject.invokeMethod(
                                widget.progress_bar,
                                'setValue',
                                Qt.ConnectionType.QueuedConnection, 
                                Q_ARG(int, progress)
                            )
                        widget.progress_bar.update()
                        if not hasattr(self, '_last_process_events') or time.time() - self._last_process_events > 0.1:
                            QApplication.processEvents()
                            self._last_process_events = time.time()
                    break
        except Exception as e:
            print(f"Error actualizando barra de progreso: {e}")

    def refresh_progress_bars(self):
        """Actualiza visualmente todas las barras de progreso de manera segura"""
        try:
            for i in range(self.list_widget.count()):
                widget = self.list_widget.itemWidget(self.list_widget.item(i))
                if widget and hasattr(widget, 'progress_bar'):
                    widget.progress_bar.update()
        except Exception as e:
            print(f"Error en refresh_progress_bars: {e}")

    def update_file_progress_bar(self, drive_letter, file_progress):
        """Actualiza la barra de progreso de archivo de manera segura"""
        try:
            # Si estamos deteniendo este hilo, ignorar actualizaciones de progreso
            if hasattr(self, 'stopping_threads') and drive_letter in self.stopping_threads:
                print(f"Ignorando actualización de progreso de archivo para {drive_letter} porque se está deteniendo")
                return
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget and hasattr(widget, 'file_progress_bar'):
                        # Obtener el modo actual para el offset correcto
                        saved_mode = self.config.get('size_mode', 'Normal')
                        size_modes = {
                            "Normal": {"scale": 100, "height": 40, "font": 12, "icon": 30, "progress": 18, "file": 4, "file_offset": 28},
                            "Grande": {"scale": 100, "height": 50, "font": 13, "icon": 35, "progress": 18, "file": 4, "file_offset": 40}
                        }
                        mode = size_modes.get(saved_mode, size_modes["Normal"])
                        
                        # Asegurar que la barra mantiene su posición centrada
                        width = widget.width()
                        progress_width = int(width * 0.1)
                        x_pos = (width - progress_width) // 2
                        file_y = mode["file_offset"]
                        widget.file_progress_bar.setGeometry(x_pos, file_y, progress_width, widget.file_progress_height)
                        QMetaObject.invokeMethod(
                            widget.file_progress_bar,
                            'setValue',
                            Qt.ConnectionType.QueuedConnection,
                            Q_ARG(int, file_progress)
                        )
                        if file_progress == 100:
                            QTimer.singleShot(1000, lambda: self.reset_file_progress_bar(widget.file_progress_bar))
                    break
        except Exception as e:
            print(f"Error actualizando barra de progreso de archivo: {e}")

    def reset_file_progress_bar(self, progress_bar):
        """Resetea la barra de progreso de archivo de manera segura"""
        if not sip.isdeleted(progress_bar):
            QMetaObject.invokeMethod(
                progress_bar,
                'setValue',
                Qt.ConnectionType.QueuedConnection,
                Q_ARG(int, 0)
            )

    def _do_update_progress(self, drive_letter, progress):
        """Actualiza el progreso de manera segura a través de un timer"""
        try:
            # Si estamos deteniendo este hilo, ignorar actualizaciones de progreso
            if hasattr(self, 'stopping_threads') and drive_letter in self.stopping_threads:
                print(f"Ignorando actualización de progreso pendiente para {drive_letter} porque se está deteniendo")
                return
            if not hasattr(self, '_pending_updates'):
                self._pending_updates = {}
            self._pending_updates[drive_letter] = progress
            if not hasattr(self, '_update_timer'):
                self._update_timer = QTimer()
                self._update_timer.timeout.connect(self._process_pending_updates)
                self._update_timer.start(500)
        except Exception as e:
            print(f"Error en _do_update_progress: {e}")

    def hide_progress_and_reset_label(self, drive_letter):
        try:
            # Marcar que estamos finalizando la copia para este disco
            if not hasattr(self, '_finishing_copy'):
                self._finishing_copy = set()
            self._finishing_copy.add(drive_letter)
            
            # Asegurar que la barra llegue al 100% antes de ocultarse
            if hasattr(self, 'max_progress_values'):
                self.max_progress_values[drive_letter] = 100
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if item and drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget:
                        try:
                            if hasattr(widget, 'progress_bar'):
                                widget.progress_bar.setValue(100)
                                widget.progress_bar.update()
                                QApplication.processEvents()
                                # Pequeña pausa para que se vea el 100%
                                QTimer.singleShot(300, lambda pb=widget.progress_bar: pb.hide())
                            if hasattr(widget, 'file_progress_bar'):
                                widget.file_progress_bar.setValue(100)
                                widget.file_progress_bar.update()
                                QApplication.processEvents()
                                QTimer.singleShot(300, lambda pb=widget.file_progress_bar: pb.hide())
                            
                            # Limpiar la etiqueta de velocidad
                            if hasattr(widget, 'speed_label'):
                                widget.speed_label.setText("")
                            
                            # Actualizar la etiqueta de volumen después de ocultar las barras
                            if hasattr(widget, 'volume_label'):
                                try:
                                    if os.path.exists(f"{drive_letter}\\"):
                                        original_text = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                                        widget.volume_label.setText(f"{original_text} ({drive_letter})")
                                    else:
                                        widget.volume_label.setText(f"Disco ({drive_letter})")
                                except Exception as e:
                                    print(f"Error al obtener información del volumen para {drive_letter}: {e}")
                                    widget.volume_label.setText(f"Disco ({drive_letter})")
                            
                            # Forzar actualización del widget
                            widget.update()
                            QApplication.processEvents()
                            
                            # Limpiar el estado de finalización después de un tiempo
                            QTimer.singleShot(500, lambda dl=drive_letter: self._remove_finishing_state(dl))
                        except Exception as e:
                            print(f"Error al resetear etiquetas para {drive_letter}: {e}")
                    break
        except Exception as e:
            print(f"Error en hide_progress_and_reset_label: {e}")
            
    def _remove_finishing_state(self, drive_letter): # VERIFICA DE ULTIMO SI LA BARRA DE PROGRESO NO SE RESTABLECIO Y LA RESTABLECE
        """Elimina el estado de finalización para un disco y asegura que las barras estén ocultas"""
        try:
            if hasattr(self, '_finishing_copy') and drive_letter in self._finishing_copy:
                self._finishing_copy.remove(drive_letter)
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if item and drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget:
                        # Forzar ocultamiento de barras de progreso
                        if hasattr(widget, 'progress_bar') and widget.progress_bar.isVisible():
                            print(f"Forzando ocultamiento de barra principal para {drive_letter}")
                            widget.progress_bar.hide()
                        if hasattr(widget, 'file_progress_bar') and widget.file_progress_bar.isVisible():
                            print(f"Forzando ocultamiento de barra de archivo para {drive_letter}")
                            widget.file_progress_bar.hide()
                        widget.update()
                    break
        except Exception as e:
            print(f"Error en _remove_finishing_state: {e}")

    def hide_progress_and_total(self, progress_bar, total_label):
        if progress_bar:
            progress_bar.setVisible(False)
        if total_label:
            total_label.setVisible(False)

    def reset_size_label(self, drive_letter):
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if drive_letter in item.text():
                widget = self.list_widget.itemWidget(item)
                if widget is not None:
                    size_label = widget.findChild(QLabel, "size_label")
                    if size_label:
                        free_space, _ = get_drive_space(drive_letter)
                        free_space_str = self.format_size(free_space)
                        size_label.setText(free_space_str)
                    volume_label = widget.volume_label
                    if volume_label:
                        current_text = volume_label.text()
                        if not current_text.endswith(f"({drive_letter})"):
                            original_text = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                            volume_label.setText(f"{original_text} ({drive_letter})")
                break

    def ventana_cola(self):
        selected_items = self.list_widget.selectedItems()
        if not selected_items:
            print("No hay discos seleccionados.")
            return
        for selected_item in selected_items:
            selected_drive = selected_item.text().split(' ')[-1].strip('()')
            if selected_drive not in self.queue_windows:
                new_queue_window = ColaWindow(self.files_in_queue, selected_drive, self)
                new_queue_window.delete_files_signal.connect(self.handle_delete_files)
                self.queue_windows[selected_drive] = new_queue_window
            queue_window = self.queue_windows[selected_drive]
            queue_window.update_queue_view()
            queue_window.show()
            queue_window.raise_()
            queue_window.activateWindow()

    def get_ordered_queue(self, drive_letter):
        if drive_letter not in self.queues:
            return OrderedDict()
        files_for_drive = [item for item in self.files_in_queue if item[1].startswith(drive_letter)]
        sorted_files = sorted(files_for_drive, key=lambda x: os.path.getctime(x[0]))
        ordered_queue = OrderedDict()
        for file in sorted_files:
            ordered_queue[file] = None
        return ordered_queue
    
    def on_disk_status_changed(self, drive_letter, connected):
        """Maneja los cambios de estado de los discos (conectado/desconectado)."""
        try:
            if not hasattr(self, 'current_copying_file'):
                self.current_copying_file = {}
            if not hasattr(self, 'interrupted_files'):
                self.interrupted_files = {}
            if not hasattr(self, 'disconnected_drives'):
                self.disconnected_drives = set()

            # Verificar si el disco tiene archivos en cola o está copiando
            has_pending_operations = (\
                drive_letter in self.queues and not self.queues[drive_letter].empty() or\
                drive_letter in self.current_copying_file and self.current_copying_file[drive_letter] is not None or\
                drive_letter in self.interrupted_files or\
                drive_letter in self.file_to_retry # Verificar si hay un archivo para reintentar
            )
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if item is not None and drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    volume_label = widget.findChildren(QLabel)[0]
                    speed_label = widget.findChildren(QLabel)[2]
                    if connected:
                        # Si el disco se vuelve a conectar
                        current_style = volume_label.styleSheet()
                        volume_label.setStyleSheet(current_style.replace("red", "white"))
                        print(f"DISCO CONECTADO: {drive_letter}")
                        
                        # Quitar de la lista de discos desconectados
                        if hasattr(self, 'disconnected_drives') and drive_letter in self.disconnected_drives:
                            self.disconnected_drives.remove(drive_letter)
                           
                        # Si estaba pausado por error (desconexión), reanudar
                        if self.copy_paused_on_error.get(drive_letter, False):
                            self.copy_paused_on_error[drive_letter] = False
                            print(f"Copia reanudada para {drive_letter} tras reconexión.")
                            # Si no hay un hilo activo, iniciarlo
                            if drive_letter not in self.threads or not self.threads[drive_letter].is_alive():
                                thread = threading.Thread(target=self.process_file_queue, args=(drive_letter,))
                                thread.do_run = True
                                thread.start()
                                self.threads[drive_letter] = thread
                                print(f"Nuevo hilo de copia iniciado para {drive_letter} tras reanudación.")
                        elif drive_letter in self.queues and not self.queues[drive_letter].empty():
                            if drive_letter not in self.threads or not self.threads[drive_letter].is_alive():
                                thread = threading.Thread(target=self.process_file_queue, args=(drive_letter,))
                                thread.do_run = True
                                thread.start()
                                self.threads[drive_letter] = thread
                                print(f"Nuevo hilo de copia iniciado para {drive_letter}")
                    else:
                        # Si se desconecta durante una copia o con operaciones pendientes
                        if has_pending_operations:
                            self.copy_paused_on_error[drive_letter] = True # Pausar la copia
                            self.disconnected_drives.add(drive_letter)
                            print(f"Disco {drive_letter} marcado como desconectado y copia pausada.")
                            current_style = volume_label.styleSheet()
                            volume_label.setStyleSheet(current_style.replace("white", "red")) # Poner en rojo
                            speed_label.setText("Desconectado")
                            print(f"DISCO DESCONECTADO: {drive_letter}")
                            
                            self.disconnected_copying_disks[drive_letter] = (\
                                self.get_volume_name(drive_letter),\
                                "Removable",\
                                drive_letter\
                            )
                        else:
                            # Si no hay operaciones pendientes, limpiar todo
                            if drive_letter in self.queues:
                                del self.queues[drive_letter]
                            if drive_letter in self.current_copying_file:
                                del self.current_copying_file[drive_letter]
                            if drive_letter in self.total_sizes:
                                del self.total_sizes[drive_letter]
                            if drive_letter in self.threads:
                                # Asegúrate de que el hilo se pueda detener limpiamente si estaba activo
                                if hasattr(self.threads[drive_letter], 'do_run'):
                                    self.threads[drive_letter].do_run = False
                                if self.threads[drive_letter].is_alive():
                                    self.threads[drive_letter].join(timeout=1) # Esperar un poco
                                del self.threads[drive_letter]
                            current_style = volume_label.styleSheet()
                            volume_label.setStyleSheet(current_style.replace("white", "red"))
                            speed_label.setText("Desconectado")
                            print(f"DISCO DESCONECTADO (sin operaciones pendientes): {drive_letter}")
                    break
        except Exception as e:
            print(f"Error en on_disk_status_changed: {e}")
            import traceback
            traceback.print_exc()
    
    def update_all_free_spaces(self):
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            drive_letter = item.text().split(' ')[-1].strip('()')
            if not self.queues.get(drive_letter, queue.Queue()).qsize() and not self.processing_directories:
                task = FreeSpaceTask(drive_letter)
                task.signals.free_space_updated.connect(self.on_free_space_updated)
                self.thread_pool.start(task)

    def on_free_space_updated(self, drive_letter, size_str):
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if drive_letter in item.text():
                widget = self.list_widget.itemWidget(item)
                if widget is not None:
                    labels = widget.findChildren(QLabel)
                    if len(labels) > 1:
                        size_label = labels[1]
                        size_label.setText(size_str)
                    else:
                        print(f"No se encontraron suficientes etiquetas para el disco {drive_letter}")
                else:
                    print(f"No se encontró el widget para el disco {drive_letter}")
                break
        else:
            print(f"No se encontr   el elemento de lista para el disco {drive_letter}")

    def update_volume_list(self, volumes):
        try:
            # Guardar discos seleccionados antes de modificar la lista
            seleccionados = set()
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if item.isSelected():
                    drive_letter = item.text().split(' ')[-1].strip('()')
                    seleccionados.add(drive_letter)
            current_filter = self.botones.current_filter if hasattr(self.botones, 'current_filter') else 'all'
            config = load_config()
            config_hidden_drives = config.get('discos_ocultos', [])
            if not hasattr(self, 'usb_history'):
                self.usb_history = []
            volumes_filtered = []
            for volume in volumes:
                volume_name, drive_type, drive_letter = volume
                disk_info = f"{volume_name} ({drive_letter})"
                if disk_info in config_hidden_drives:
                    print(f"Ignorando disco oculto en config: {disk_info}")
                    continue
                volumes_filtered.append(volume)
            for drive_letter, disk_info in list(self.disconnected_copying_disks.items()):
                is_actively_copying = (
                    (drive_letter in self.threads and self.threads[drive_letter].is_alive()) or
                    (drive_letter in self.queues and not self.queues[drive_letter].empty()) or
                    (drive_letter in self.current_copying_file) or
                    (drive_letter in self.interrupted_files)
                )
                if not is_actively_copying:
                    print(f"Eliminando disco inactivo {drive_letter} de disconnected_copying_disks")
                    del self.disconnected_copying_disks[drive_letter]
                    continue
                if not any(vol[2] == drive_letter for vol in volumes_filtered):
                    print(f"Añadiendo disco desconectado activo durante copia: {drive_letter}")
                    volumes_filtered.append(disk_info)
            copying_drives = set()
            for drive_letter in self.threads:
                if self.threads[drive_letter].is_alive():
                    copying_drives.add(drive_letter)
            copying_labels = {}
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                drive_letter = item.text().split(' ')[-1].strip('()')
                if drive_letter in copying_drives:
                    widget = self.list_widget.itemWidget(item)
                    if widget and hasattr(widget, 'volume_label'):
                        copying_labels[drive_letter] = widget.volume_label.text()
            
            # Limpiar la lista actual excepto los que están copiando
            items_to_remove = []
            for i in range(self.list_widget.count() - 1, -1, -1):
                item = self.list_widget.item(i)
                drive_letter = item.text().split(' ')[-1].strip('()')
                if drive_letter not in copying_drives and drive_letter not in self.disconnected_copying_disks:
                    items_to_remove.append(i)
            for i in sorted(items_to_remove, reverse=True):
                self.list_widget.takeItem(i)
            
            # Configuración de estilo - CORREGIDO: Usar valores del modo directamente
            saved_mode = self.config.get('size_mode', 'Normal')
            size_modes = {
                "Normal": {"scale": 100, "height": 40, "font": 12, "icon": 30, "progress": 18, "file": 4, "file_offset": 28},
                "Grande": {"scale": 100, "height": 50, "font": 13, "icon": 35, "progress": 18, "file": 4, "file_offset": 40}
            }
            mode = size_modes.get(saved_mode, size_modes["Normal"])
            
            # Usar los valores del modo en lugar de calcularlos
            current_height = mode["height"]
            current_font = mode["font"]
            bold_font = QFont()
            bold_font.setBold(True)
            bold_font.setPointSize(current_font)
            style = f"color: white; font-size: {current_font}px;"
            current_icon_size = mode["icon"]
            self.list_widget.setIconSize(QSize(current_icon_size, current_icon_size))

            def get_volume_priority(volume):
                _, drive_type, drive_letter = volume
                if drive_letter.upper() == "C:":
                    priority = 0
                elif "interno" in drive_type.lower():
                    priority = 1
                elif "externo" in drive_type.lower():
                    priority = 2
                elif "USB" in drive_type.lower():
                    priority = 3
                    if drive_letter not in self.usb_history:
                        self.usb_history.append(drive_letter)
                else:
                    priority = 4
                if priority == 3:
                    letter_value = self.usb_history.index(drive_letter)
                else:
                    letter_value = ord(drive_letter[0].upper())
                return (priority, letter_value)
            sorted_volumes = sorted(volumes_filtered, key=get_volume_priority)
            
            # Limpiar el historial USB para mantener solo las unidades conectadas
            connected_usb_letters = [vol[2] for vol in volumes_filtered if "USB" in vol[1]]
            self.usb_history = [letter for letter in self.usb_history if letter in connected_usb_letters]
            
            # Agregar volúmenes no ocultos
            for volume in sorted_volumes:
                volume_name, drive_type, drive_letter = volume
                exists = False
                for i in range(self.list_widget.count()):
                    item = self.list_widget.item(i)
                    if drive_letter in item.text():
                        exists = True
                        # Si está copiando, restaurar la etiqueta original
                        if drive_letter in copying_drives and drive_letter in copying_labels:
                            widget = self.list_widget.itemWidget(item)
                            if widget and hasattr(widget, 'volume_label'):
                                widget.volume_label.setText(copying_labels[drive_letter])
                                # Ocultar la etiqueta de tamaño para evitar duplicación
                                if hasattr(widget, 'size_label') and drive_letter in self.total_sizes and self.total_sizes[drive_letter] > 0:
                                    widget.size_label.setVisible(False)
                        break
                if not exists:
                    # Agregar nuevo volumen
                    priority = get_volume_priority(volume)
                    insert_position = 0
                    for i in range(self.list_widget.count()):
                        item = self.list_widget.item(i)
                        current_drive_type = item.text().split(' - ')[1].split(' (')[0] if ' - ' in item.text() else ""
                        current_drive_letter = item.text().split(' ')[-1].strip('()')
                        current_priority = get_volume_priority(('', current_drive_type, current_drive_letter))
                        if current_priority > priority:
                            break
                        insert_position = i + 1
                    item = QListWidgetItem(f"{volume_name} - {drive_type} ({drive_letter})")
                    item.setSizeHint(QSize(0, current_height))
                    
                    # Si el disco está copiando, usar la etiqueta guardada
                    if drive_letter in copying_drives and drive_letter in copying_labels:
                        display_name = copying_labels[drive_letter]
                    else:
                        display_name = f"{volume_name} ({drive_letter})"
                    custom_widget = CustomListWidgetItem(display_name, 
                                                       get_drive_space(drive_letter)[0] / (1024 ** 3), 
                                                       self)
                    custom_widget.setFixedHeight(current_height)
                    for label in [custom_widget.volume_label, custom_widget.size_label, custom_widget.speed_label]:
                        label.setFont(bold_font)
                        label.setStyleSheet(style)
                    saved_mode = self.config.get('size_mode', 'Normal')
                    size_modes = {
                        "Normal": {"scale": 100, "height": 40, "font": 12, "icon": 30, "progress": 18, "file": 4, "file_offset": 28},
                        "Grande": {"scale": 100, "height": 50, "font": 13, "icon": 35, "progress": 18, "file": 4, "file_offset": 40}
                    }
                    mode = size_modes.get(saved_mode, size_modes["Normal"])
                    
                    progress_bar = custom_widget.progress_bar
                    progress_bar.setFixedHeight(mode["progress"])
                    progress_bar.setGeometry(0, 0, custom_widget.width(), mode["progress"])
                    
                    file_progress_bar = custom_widget.file_progress_bar
                    file_progress_bar.setFixedHeight(mode["file"])
                    file_progress_bar.setGeometry(0, mode["file_offset"], custom_widget.width(), mode["file"])
                    
                    # Determinar el icono a usar
                    if drive_letter.upper() == "C:":
                        from CREAR import create_windows_icon
                        icon_path = create_windows_icon()
                    elif "interno" in drive_type:
                        from CREAR import create_internal_hdd_icon
                        icon_path = create_internal_hdd_icon()
                    elif "externo" in drive_type:
                        from CREAR import create_external_hdd_icon
                        icon_path = create_external_hdd_icon()
                    elif "USB" in drive_type:
                        from CREAR import create_usb_icon
                        icon_path = create_usb_icon()
                    elif "red" in drive_type.lower():  # Añadido para unidades de red
                        from CREAR import icono_RED
                        icon_path = icono_RED()
                    else:
                        icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'OTRAS.png')
                    item.setIcon(QIcon(icon_path))
                    self.list_widget.insertItem(insert_position, item)
                    self.list_widget.setItemWidget(item, custom_widget)
                    
                    # Si está copiando, configurar las barras de progreso y ocultar etiqueta de tamaño
                    if drive_letter in copying_drives:
                        progress_bar.setVisible(True)
                        file_progress_bar.setVisible(True)
                        if drive_letter in self.total_sizes and self.total_sizes[drive_letter] > 0:
                            progress = int((self.copy_bytes_copied.get(drive_letter, 0) / self.total_sizes[drive_letter]) * 100)
                            progress_bar.setValue(progress)
                            # Ocultar la etiqueta de tamaño para evitar duplicación
                            custom_widget.size_label.setVisible(False)
            
            # Al final de la actualización, volver a aplicar el filtro actual
            if current_filter != 'all':
                self.botones.apply_disk_filter(current_filter)
            # Restaurar selección después de poblar la lista
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                drive_letter = item.text().split(' ')[-1].strip('()')
                if drive_letter in seleccionados:
                    item.setSelected(True)
            self.list_widget.update()
            print(f"Lista de volúmenes actualizada. Filtro actual: {current_filter}")
            print(f"Discos ocultos (config): {config_hidden_drives}")
        except Exception as e:
            print(f"Error actualizando lista de volúmenes: {e}")
            import traceback
            traceback.print_exc()

   
    def stop_selected_thread(self, drive_letter):
        _hide_active_tooltip() # Oculta cualquier tooltip activo inmediatamente
        (f"Intentando detener el hilo para el disco {drive_letter}.")
        print(f"Intentando detener el hilo para el disco {drive_letter}.")
        # Marcar que estamos deteniendo este hilo para evitar actualizaciones de progreso
        if not hasattr(self, 'stopping_threads'):
            self.stopping_threads = set()
        self.stopping_threads.add(drive_letter)
        current_style = self.config.get('disk_style', 'Estilo 1')
        sizes = {
            "Estilo 1": {"height": 35, "font": 12},
            "Estilo 2": {"height": 36, "font": 19},
            "Estilo 3": {"height": 37, "font": 20},
            "Estilo 4": {"height": 38, "font": 21}
        }
        current_config = sizes.get(current_style, sizes["Estilo 1"])
        self.update_speed_display()
        try:
            # Eliminar todas las referencias de archivos en cola para este disco
            self.files_in_queue = {item for item in self.files_in_queue if not item[1].startswith(drive_letter)}
            if hasattr(self, 'files_in_queue_by_drive') and drive_letter in self.files_in_queue_by_drive:
                del self.files_in_queue_by_drive[drive_letter]
            print(f"Referencias de archivos en cola eliminadas para {drive_letter}")
        except Exception as e:
            print(f"Error al limpiar referencias de archivos en cola para {drive_letter}: {e}")

        # Limpiar referencias de archivos en cola para este disco
        try:
            if drive_letter in self.files_in_queue_by_drive:
                self.files_in_queue.difference_update(self.files_in_queue_by_drive[drive_letter])
                del self.files_in_queue_by_drive[drive_letter]
                print(f"Referencias de archivos en cola eliminadas para {drive_letter}")
            self.files_in_queue = {item for item in self.files_in_queue if not isinstance(item, tuple) or item[1][0] != drive_letter}
        except Exception as e:
            print(f"Error al limpiar referencias de archivos en cola para {drive_letter}: {e}")
        if drive_letter in self.threads:
            thread = self.threads[drive_letter]
            if thread.is_alive():
                if hasattr(thread, 'paused') and thread.paused:
                    thread.paused = False
                    self.pause_times.pop(drive_letter, None)
                    print(f"Pausa quitada para el disco {drive_letter}")
                print(f"Solicitando interrupción para el hilo del disco {drive_letter}")
                thread.do_run = False

                # Mejorar el manejo de archivos en uso
                if drive_letter in self.current_copying_file and self.current_copying_file[drive_letter]:
                    src_path, dst_path = self.current_copying_file[drive_letter]
                    print(f"Forzando cierre de archivos para: {src_path} -> {dst_path}")
                    self.current_copying_file[drive_letter] = None
                    try:
                        import gc
                        gc.collect()
                        
                        # Si el archivo destino existe pero está incompleto, intentar eliminarlo
                        if os.path.exists(dst_path):
                            try:
                                # Intentar cerrar cualquier manejador abierto
                                import win32file
                                win32file.CloseHandle(win32file.CreateFile(
                                    dst_path, 
                                    win32file.GENERIC_READ, 
                                    0, None, 
                                    win32file.OPEN_EXISTING, 
                                    win32file.FILE_FLAG_DELETE_ON_CLOSE, 
                                    0
                                ))
                                print(f"Archivo destino cerrado forzosamente: {dst_path}")
                            except:
                                pass
                    except:
                        pass
                    time.sleep(1.0)  # Aumentar el tiempo de espera a 1 segundo
                thread.join(timeout=2)
            
            # Limpiar todas las estructuras de datos
            self.threads.pop(drive_letter, None)
            self.queues[drive_letter].queue.clear()
            self.files_in_queue = {item for item in self.files_in_queue if item[1][0] != drive_letter}
            self.total_sizes[drive_letter] = 0
            self.copy_bytes_copied[drive_letter] = 0
            self.copy_start_time.pop(drive_letter, None)
            self.pause_times.pop(drive_letter, None)
            if not hasattr(self, 'closing_app') or not self.closing_app:
                try:
                    self.remove_disk_from_pending_copies(drive_letter)
                except Exception as e:
                    print(f"Error al eliminar disco de copias pendientes: {e}")
            
            # Limpiar el espacio final calculado cuando se cancela la copia
            if drive_letter in self.disks_space_final:
                logging.info(f"Limpiando espacio final calculado para {drive_letter} después de cancelar")
                del self.disks_space_final[drive_letter]
            
            # Emitir señales para actualizar la UI a 0%
            self.update_progress_signal.emit(drive_letter, 0)
            self.update_file_progress_signal.emit(drive_letter, 0)
            self.update_queue_progress_signal.emit(0)
            
            # Actualizar la UI directamente con prioridad
            for i in range(self.list_widget.count()):
                list_item = self.list_widget.item(i)
                if drive_letter in list_item.text():
                    widget = self.list_widget.itemWidget(list_item)
                    if widget:
                        # Resetear las barras de progreso a 0% y mantenerlas visibles
                        progress_bar = widget.findChildren(QProgressBar)[0]
                        progress_bar.setValue(0)
                        progress_bar.setVisible(True)
                        progress_bar.update()
                        progress_bar.repaint()
                        QApplication.processEvents()  # Forzar procesamiento de eventos
                        file_progress_bar = widget.findChildren(QProgressBar)[1]
                        file_progress_bar.setValue(0)
                        file_progress_bar.setVisible(True)
                        file_progress_bar.update()
                        file_progress_bar.repaint()
                        QApplication.processEvents()  # Forzar procesamiento de eventos
                        speed_label = widget.findChildren(QLabel)[2]
                        speed_label.setText("")
                    break
            # Actualizar ventana de cola si existe
            if drive_letter in self.queue_windows:
                self.queue_windows[drive_letter].update_queue_view()
            print(f"Copia detenida para el disco {drive_letter}")
            
            # Quitar la marca de detención para permitir futuras actualizaciones
            if hasattr(self, 'stopping_threads') and drive_letter in self.stopping_threads:
                self.stopping_threads.remove(drive_letter)
        else:
            print(f"No se encontró ningún hilo para el disco {drive_letter}.")
            
            # Quitar la marca de detención en caso de error
            if hasattr(self, 'stopping_threads') and drive_letter in self.stopping_threads:
                self.stopping_threads.remove(drive_letter)

        # Forzar actualización de UI para total, precio y espacio disponible
        try:
            self.update_total_size(drive_letter, 0, force_recalculate=True)
            self.update_ui_after_copy(drive_letter)
            self.update_progress_signal.emit(drive_letter, 0)
            self.update_file_progress_signal.emit(drive_letter, 0)
            self.update_queue_progress_signal.emit(0)
           
            # Actualizar directamente las barras de progreso para mostrar 0% y asegurar que permanezcan visibles
            for i in range(self.list_widget.count()):
                list_item = self.list_widget.item(i)
                if drive_letter in list_item.text():
                    widget = self.list_widget.itemWidget(list_item)
                    if widget:
                        progress_bars = widget.findChildren(QProgressBar)
                        if progress_bars:
                            progress_bar = progress_bars[0]
                            progress_bar.setValue(0)
                            progress_bar.setVisible(True)
                            progress_bar.update()
                            progress_bar.repaint()
                        if len(progress_bars) > 1:
                            file_progress_bar = progress_bars[1]
                            file_progress_bar.setValue(0)
                            file_progress_bar.setVisible(True)
                            file_progress_bar.update()
                            file_progress_bar.repaint()
                        labels = widget.findChildren(QLabel)
                        if len(labels) > 2:
                            speed_label = labels[2]
                            speed_label.setText("")
                        print(f"Barras de progreso forzadas a 0% y visibles para {drive_letter}")
                    break
            if hasattr(self, 'current_explorer_drive') and self.current_explorer_drive == drive_letter:
                if hasattr(self, 'file_tree_widget') and self.file_tree_widget.isVisible():
                    volume_name = self.get_volume_name(drive_letter)
                    free_space, total_space = get_drive_space(drive_letter)
                    free_space_str = self.format_size(free_space)
                    drive_info_text = f"{volume_name} ({drive_letter}) | {free_space_str}"
                    self.drive_info_label.setText(drive_info_text)
                    # También ocultar la barra de progreso mini si existe
                    if drive_letter in self.mini_progress_bars:
                        self.mini_progress_bars[drive_letter].hide()
                    # Ocultar la etiqueta de velocidad si existe
                    if hasattr(self, 'explorer_speed_labels') and drive_letter in self.explorer_speed_labels:
                        self.explorer_speed_labels[drive_letter].hide()
                self.update_all_free_spaces()
            if hasattr(self, 'mini_progress_bars') and drive_letter in self.mini_progress_bars:
                mini_bar = self.mini_progress_bars[drive_letter]
                mini_bar.setValue(0)
                mini_bar.setVisible(False)
                mini_bar.setStyleSheet("")  # Limpiar cualquier estilo de fondo
                self.reset_file_progress_bar(mini_bar)
                # Desactivar el dibujo del fondo gris semitransparente
                if hasattr(mini_bar, 'setDrawBackground'):
                    mini_bar.setDrawBackground(False)
                    print(f"Fondo de la barra mini desactivado para {drive_letter}")
                mini_bar.update()
                mini_bar.repaint()
                print(f"Barra mini actualizada a 0% para {drive_letter}")
                
        except Exception as e:
            print(f"Error al forzar actualización de UI para {drive_letter}: {e}")

    def process_file_queue(self, drive_letter):
        """Processes the file queue for a specific disk using separate threads for reading and writing,
        con una caché de RAM dedicada por dispositivo."""
        try:
            self.set_thread_priority()

            # Initialize copy tracking variables
            if not hasattr(self, 'copy_start_time'):
                self.copy_start_time = {}
            if not hasattr(self, 'copy_bytes_copied'):
                self.copy_bytes_copied = {}
            if not hasattr(self, 'last_save_time'):
                self.last_save_time = {}

            self.copy_start_time[drive_letter] = time.time()
            self.copy_bytes_copied[drive_letter] = 0
            self.last_save_time[drive_letter] = time.time()

            # Add flag to prevent progress bar setbacks
            self.queue_lock.lock()
            try:
                if not hasattr(self, 'max_progress_values'):
                    self.max_progress_values = {}
                self.max_progress_values[drive_letter] = 0

                if drive_letter not in self.copy_bytes_copied:
                    self.copy_bytes_copied[drive_letter] = 0
                if drive_letter not in self.total_sizes:
                    self.total_sizes[drive_letter] = 0
                if drive_letter not in self.copy_start_time:
                    self.copy_start_time[drive_letter] = time.time()
            finally:
                self.queue_lock.unlock()

            # Set thread priority
            try:
                handle = win32api.GetCurrentThread()
                win32process.SetThreadPriority(handle, win32process.THREAD_PRIORITY_BELOW_NORMAL)
                print("Thread priority set to below normal.")
            except Exception as e:
                print(f"Could not set thread priority: {e}")

            # Hide available space and show progress bars when copying starts
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget:
                        widget.progress_bar.setValue(0)
                        widget.progress_bar.setVisible(True)
                        widget.progress_bar.show()
                        widget.progress_bar.update()
                        widget.file_progress_bar.setValue(0)
                        widget.file_progress_bar.setVisible(True)
                        widget.file_progress_bar.show()
                        widget.file_progress_bar.update()
                        if hasattr(widget, 'size_label'):
                            widget.size_label.hide()
                        saved_mode = self.config.get('size_mode', 'Normal')
                        mode = self.config_window.size_modes[saved_mode]
                        widget.progress_bar.setFixedHeight(mode["progress"])
                        widget.progress_bar.setGeometry(0, 0, widget.width(), mode["progress"])
                        widget.file_progress_bar.setFixedHeight(mode["file"])
                        file_y = mode["file_offset"]
                        widget.file_progress_bar.setGeometry(0, file_y, widget.width(), mode["file"])
                        widget.update()
                    break
            t = threading.current_thread()
            t.paused = False
            current_file_path = None
            destination_path = None
            
            # Contador de errores para seguimiento
            error_count = 0
            total_files = self.queues[drive_letter].qsize() if drive_letter in self.queues else 0
            while getattr(t, "do_run", True):
                # Pausar si el disco está marcado para reconexión
                while self.copy_paused_on_error.get(drive_letter, False):
                    print(f"Disco {drive_letter} desconectado. Pausando copia hasta reconexión...")
                    time.sleep(1) # Esperar 1 segundo antes de re-verificar
                    # Si el hilo es detenido durante la pausa
                    if not getattr(t, "do_run", True):
                        print(f"Hilo de copia para {drive_letter} detenido durante pausa por desconexión.")
                        return # Salir completamente de la función (el archivo se queda en current_copying_file)
                while getattr(t, "paused", False): # Esto es para la pausa manual del usuario, si existe
                    time.sleep(0.1)
                    if not getattr(t, "do_run", True):
                        print(f"Hilo de copia para {drive_letter} detenido durante pausa.")
                        break
                if not getattr(t, "do_run", True):
                    print(f"Detectada solicitud de detención antes de iniciar copia de {current_file_path}")
                    if current_file_path and destination_path:
                        self._cleanup_current_file(drive_letter, current_file_path, destination_path)
                        current_file_path = None
                        destination_path = None
                    break

                # Obtener un archivo de la cola solo si no hay uno en progreso (ej. después de una reconexión)
                if current_file_path is None: 
                    self.queue_lock.lock()
                    try:
                        try:
                            # Intenta obtener el siguiente archivo de la cola
                            current_file_path, destination_path = self.queues[drive_letter].get(timeout=0.5)
                            self.current_copying_file[drive_letter] = (current_file_path, destination_path)
                        except queue.Empty:
                            if not getattr(t, "do_run", True):
                                print(f"Hilo de copia para {drive_letter} detenido limpiamente (cola vacía).")
                                break
                            continue # Cola vacía, sigue esperando archivos si el hilo no ha sido detenido
                    finally:
                        self.queue_lock.unlock()

                # Verificar si el archivo fuente existe
                if not os.path.isfile(current_file_path):
                    print(f"Source file not found or is not a file: {current_file_path}. Skipping.")
                    # Si el archivo no existe, lo tratamos como un error "regular" y pasamos al siguiente.
                    self._handle_file_error(drive_letter, current_file_path, destination_path)
                    current_file_path = None
                    destination_path = None
                    error_count += 1
                    continue
                file_size = os.path.getsize(current_file_path)

                # Asegurarse de que el directorio de destino exista
                dest_dir = os.path.dirname(destination_path)
                if not os.path.exists(dest_dir):
                    try:
                        os.makedirs(dest_dir, exist_ok=True)
                        print(f"Created directory: {dest_dir}")
                    except Exception as e:
                        print(f"Error creating directory {dest_dir}: {e}")
                        # Antes de llamar a _handle_file_error, verificar si el disco está inaccesible
                        if not self.is_disk_accessible(drive_letter):
                            print(f"Disco {drive_letter} inaccesible durante la creación de directorio. Pausando.")
                            self.copy_paused_on_error[drive_letter] = True # Pausar por desconexión
                            continue # Volver al inicio del bucle para que entre en la pausa
                        else:
                            # Es un error de creación de directorio, pero el disco está accesible. Tratar como error regular.
                            self._handle_file_error(drive_letter, current_file_path, destination_path)
                            current_file_path = None
                            destination_path = None
                            error_count += 1
                            continue

                # Copiar archivo
                success = self._copy_file_with_separate_threads(
                    current_file_path, destination_path, file_size, drive_letter, t
                )
                if not success:
                    print(f"Copy failed or interrupted for {current_file_path}.")
                    if not self.is_disk_accessible(drive_letter):
                        print(f"Disco {drive_letter} inaccesible. El archivo {current_file_path} se mantiene en progreso y se pausa la copia.")
                        self.copy_paused_on_error[drive_letter] = True # Pausar por desconexión
                        continue
                    else:
                        # Si el disco está accesible, fue un error de copia "regular"
                        print(f"Error de copia para {current_file_path}. Disco {drive_letter} accesible. Limpiando.")
                        self._handle_file_error(drive_letter, current_file_path, destination_path) # Mark as error and clean up
                        current_file_path = None
                        destination_path = None
                        error_count += 1
                        if not getattr(t, "do_run", True): # If thread was stopped, break loop
                            break
                        continue # If it was an error, move to the next file in the queue
                self._cleanup_current_file(drive_letter, current_file_path, destination_path)
                current_file_path = None
                destination_path = None
                if self.queues[drive_letter].empty():
                    self._finalize_copy_queue(drive_letter)
                    break
            if drive_letter in self.queues and self.queues[drive_letter].empty():
                QMetaObject.invokeMethod(
                    self,
                    'update_progress_signal',
                    Qt.ConnectionType.QueuedConnection,
                    Q_ARG(str, drive_letter),
                    Q_ARG(int, 100)
                )
                if hasattr(self, 'threads') and drive_letter in self.threads and self.threads[drive_letter].is_alive():
                    self._finalize_copy_queue(drive_letter)
            if error_count > 0:
                print(f"Copia finalizada con {error_count} errores de {total_files} archivos.")
                self.show_tooltip(f"Copia finalizada con {error_count} errores.", duration=5000)
        except InterruptedError:
            print(f"Copy interrupted for {drive_letter}")
        except Exception as e:
            print(f"Error in main copy thread: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self._cleanup_copy_thread(drive_letter, t)

    def _copy_file_with_separate_threads(self, src_path, dst_path, file_size, drive_letter, main_thread):
        """Copies a file using separate threads for reading and writing, with optimized RAM buffering."""
        src_handle = None
        dst_handle = None
        reader = None
        writer = None
        copy_error = threading.Event()
        copy_complete = threading.Event()
        stop_requested = threading.Event()  # Evento para coordinar la detención
        try:
            # Optimizamos el tamaño del chunk basado en el rendimiento típico de USB
            chunk_size = 8 * 1024 * 1024  # 8MB por chunk (mejor para USB)
            DEVICE_BUFFER_SIZE = 256 * 1024 * 1024  # 256MB buffer total
            
            # Reducimos el número de chunks para menor overhead
            num_chunks_in_buffer = max(4, DEVICE_BUFFER_SIZE // chunk_size)
            
            # Usamos una cola con un límite más bajo para mejor respuesta
            read_queue = queue.Queue(maxsize=num_chunks_in_buffer)

            bytes_copied = 0

            # Optimizaciones de bajo nivel para Windows
            try:
                # Configuración optimizada para lectura
                src_handle = win32file.CreateFile(
                    src_path,
                    win32con.GENERIC_READ,
                    win32con.FILE_SHARE_READ,
                    None,
                    win32con.OPEN_EXISTING,
                    win32con.FILE_FLAG_SEQUENTIAL_SCAN | win32con.FILE_FLAG_NO_BUFFERING,
                    None
                )

                # Configuración optimizada para escritura
                dst_handle = win32file.CreateFile(
                    dst_path,
                    win32con.GENERIC_WRITE,
                    0,
                    None,
                    win32con.CREATE_ALWAYS,
                    win32con.FILE_FLAG_SEQUENTIAL_SCAN | win32con.FILE_FLAG_WRITE_THROUGH,
                    None
                )

                # Establecer tamaño del buffer del sistema
                win32file.SetFilePointer(dst_handle, file_size, win32con.FILE_BEGIN)
                win32file.SetEndOfFile(dst_handle)
                win32file.SetFilePointer(dst_handle, 0, win32con.FILE_BEGIN)
            except Exception as e:
                print(f"Error opening files for copy: {e}")
                self.error_signal.emit(f"Error opening files to copy {os.path.basename(src_path)}: {e}")
                return False

            def reader_thread():
                """Reader thread optimizado para máximo rendimiento."""
                nonlocal bytes_copied
                try:
                    bytes_read_total = 0
                    read_buffer = win32file.AllocateReadBuffer(chunk_size)
                    while (bytes_read_total < file_size and 
                        getattr(main_thread, "do_run", True) and 
                        not copy_error.is_set() and 
                        not stop_requested.is_set()):
                        if getattr(main_thread, 'paused', False):
                            time.sleep(0.01)
                            continue
                        try:
                            error_code, data = win32file.ReadFile(src_handle, read_buffer)
                            if error_code != 0:
                                print(f"Read error: {error_code}")
                                copy_error.set()
                                break
                            if not data:
                                break

                            # Verificar si se solicitó detener antes de poner en la cola
                            if stop_requested.is_set():
                                break
                            data_view = memoryview(data)
                            read_queue.put(data_view, timeout=1.0)
                            bytes_read_total += len(data)
                        except queue.Full:
                            if not getattr(main_thread, "do_run", True) or stop_requested.is_set():
                                break
                            time.sleep(0.001)
                        except Exception as e:
                            print(f"Reader error: {e}")
                            copy_error.set()
                            break
                except Exception as e:
                    print(f"Fatal reader error: {e}")
                    copy_error.set()
                finally:
                    try:
                        read_queue.put(None, timeout=1.0)  # Señal de fin
                    except queue.Full:
                        pass
                    print("Reader thread finished")

            def writer_thread():
                """Writer thread optimizado para máximo rendimiento."""
                nonlocal bytes_copied
                bytes_accumulated = 0
                last_update = time.time()
                try:
                    while (getattr(main_thread, "do_run", True) and 
                        not copy_error.is_set() and 
                        not stop_requested.is_set()):
                        if getattr(main_thread, 'paused', False):
                            time.sleep(0.01)
                            continue
                        try:
                            # Timeout más corto para responder rápido a detenciones
                            data = read_queue.get(timeout=0.5)
                            if data is None:
                                break

                            # Verificar si se solicitó detener antes de escribir
                            if stop_requested.is_set():
                                break
                            error_code, bytes_written = win32file.WriteFile(dst_handle, data)
                            read_queue.task_done()
                            if error_code != 0:
                                print(f"Write error: {error_code}")
                                copy_error.set()
                                break
                            bytes_copied += bytes_written
                            bytes_accumulated += bytes_written
                            current_time = time.time()
                            if current_time - last_update >= 0.1:
                                # Verificar si drive_letter existe en las estructuras necesarias
                                if drive_letter not in self.total_sizes:
                                    self.total_sizes[drive_letter] = file_size
                                if drive_letter not in self.copy_bytes_copied:
                                    self.copy_bytes_copied[drive_letter] = 0
                                self._update_copy_progress(
                                    drive_letter,
                                    bytes_accumulated,
                                    bytes_copied,
                                    file_size
                                )
                                bytes_accumulated = 0
                                last_update = current_time
                        except queue.Empty:
                            if copy_error.is_set() or stop_requested.is_set():
                                break
                            continue

                    # Asegurar que los datos se escriban al disco antes de terminar
                    if not stop_requested.is_set() and bytes_copied > 0:
                        try:
                            win32file.FlushFileBuffers(dst_handle)
                        except Exception as e:
                            print(f"Error in flush: {e}")
                except Exception as e:
                    print(f"Writer error: {e}")
                    copy_error.set()
                finally:
                    print("Writer thread finished")
                    copy_complete.set()

            # Establecer prioridad de los hilos
            reader = threading.Thread(target=reader_thread, daemon=True, name="ReaderThread")
            writer = threading.Thread(target=writer_thread, daemon=True, name="WriterThread")
            
            reader.start()
            writer.start()

            # Verificar si se solicita detener la copia mientras los hilos están activos
            while reader.is_alive() or writer.is_alive():
                if not getattr(main_thread, "do_run", True):
                    print("Stop requested during copy, signaling threads to terminate")
                    stop_requested.set()
                    # Vaciar la cola para desbloquear el writer
                    try:
                        while not read_queue.empty():
                            read_queue.get_nowait()
                            read_queue.task_done()
                    except:
                        pass
                    # Añadir un elemento None para desbloquear el writer si está esperando
                    try:
                        read_queue.put(None, timeout=0.5)
                    except queue.Full:
                        pass
                    break
                time.sleep(0.1)  # Verificar periódicamente

            # Esperar a que terminen los hilos con un timeout más agresivo
            timeout_join = 3.0  # Aumentar timeout a 3 segundos
            if reader.is_alive():
                reader.join(timeout=timeout_join)
                # Si sigue vivo después del timeout, seguimos adelante
                if reader.is_alive():
                    print("Reader thread did not finish in time, continuing cleanup")
            
            if writer.is_alive():
                writer.join(timeout=timeout_join)
                # Si sigue vivo después del timeout, seguimos adelante
                if writer.is_alive():
                    print("Writer thread did not finish in time, continuing cleanup")

            # Verificar y finalizar la copia
            if copy_error.is_set() or stop_requested.is_set() or not getattr(main_thread, "do_run", True):
                # Solo limpiar si fue interrumpido y no está completo
                if bytes_copied < file_size:
                    print("Copy was interrupted by user")
                    # Cerrar los handles antes de intentar limpiar el archivo
                    if src_handle:
                        try:
                            win32file.CloseHandle(src_handle)
                            src_handle = None
                            print("Source file handle closed")
                        except Exception as e:
                            print(f"Error closing source handle: {e}")
                    if dst_handle:
                        try:
                            win32file.CloseHandle(dst_handle)
                            dst_handle = None
                            print("Destination file handle closed")
                        except Exception as e:
                            print(f"Error closing destination handle: {e}")
                    time.sleep(0.5)
                    # Ahora intentar limpiar el archivo incompleto
                    self._cleanup_incomplete_file(dst_path, src_path)
                return False
            return bytes_copied == file_size
        except Exception as e:
            print(f"Copy error: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            # Asegurar que los handles se cierren
            try:
                if src_handle:
                    win32file.CloseHandle(src_handle)
                    print("Source file handle closed in finally")
                if dst_handle:
                    win32file.CloseHandle(dst_handle)
                    print("Destination file handle closed in finally")
            except Exception as e:
                print(f"Error closing handles in finally: {e}")

    def _update_copy_progress(self, drive_letter, bytes_accumulated, bytes_copied, file_size):
        """Actualiza el progreso de la copia de manera eficiente."""
        self.queue_lock.lock()
        try:
            self.copy_bytes_copied[drive_letter] = min(
                self.total_sizes[drive_letter],
                self.copy_bytes_copied.get(drive_letter, 0) + bytes_accumulated
            )
            total_progress = int((self.copy_bytes_copied[drive_letter] / self.total_sizes[drive_letter]) * 100)
            file_progress = int((bytes_copied / file_size) * 100)
            QMetaObject.invokeMethod(
                self,
                'update_progress_signal',
                Qt.ConnectionType.QueuedConnection,
                Q_ARG(str, drive_letter),
                Q_ARG(int, total_progress)
            )
            QMetaObject.invokeMethod(
                self,
                'update_file_progress_signal',
                Qt.ConnectionType.QueuedConnection,
                Q_ARG(str, drive_letter),
                Q_ARG(int, file_progress)
            )
        finally:
            self.queue_lock.unlock()

    def _cleanup_incomplete_file(self, dst_path, src_path):
        """Limpia un archivo incompleto después de una interrupción"""
        try:
            # Forzar liberación de recursos antes de intentar eliminar
            import gc
            gc.collect()
            
            # Esperar un poco para que los handles se liberen
            time.sleep(0.5)
            
            if os.path.exists(dst_path):
                # Intentar cerrar cualquier handle abierto primero
                try:
                    import win32file, win32con, win32api
                    
                    # Intentar abrir y cerrar el archivo para liberar handles
                    try:
                        handle = win32file.CreateFile(
                            dst_path,
                            win32con.GENERIC_READ | win32con.GENERIC_WRITE,
                            0,  # Sin compartir
                            None,
                            win32con.OPEN_EXISTING,
                            win32con.FILE_ATTRIBUTE_NORMAL,
                            None
                        )
                        win32file.CloseHandle(handle)
                        print(f"Archivo {dst_path} cerrado correctamente")
                    except Exception as e:
                        print(f"No se pudo abrir/cerrar el archivo para liberar: {e}")
                    
                    # Intentar cambiar atributos del archivo para facilitar eliminación
                    try:
                        win32api.SetFileAttributes(dst_path, win32con.FILE_ATTRIBUTE_NORMAL)
                    except Exception as e:
                        print(f"No se pudieron cambiar atributos: {e}")
                except ImportError as e:
                    print(f"Error importando módulos Win32: {e}")
                except Exception as e:
                    print(f"Error preparando archivo para eliminación: {e}")
                    
                # Intentar eliminar con retries más agresivos
                max_retries = 15  # Aumentamos el número de intentos
                for attempt in range(1, max_retries + 1):
                    try:
                        os.remove(dst_path)
                        print(f"Incomplete/erroneous destination file deleted: {dst_path}")
                        break
                    except PermissionError:
                        if attempt < max_retries:
                            wait_time = 0.3  # Aumentamos el tiempo de espera
                            print(f"Attempt {attempt}: File {dst_path} is in use. Retrying in {wait_time}s...")
                            time.sleep(wait_time)
                            
                            # Cada 3 intentos, forzar recolección de basura
                            if attempt % 3 == 0:
                                gc.collect()
                        else:
                            print(f"Could not delete incomplete file {dst_path} after {max_retries} attempts.")
                            # Intentar método alternativo de eliminación más agresivo
                            try:
                                # Método 1: Usar cmd con /F
                                import subprocess
                                subprocess.run(["cmd", "/c", "del", "/f", "/q", dst_path], shell=True)
                                print(f"Attempted force delete via cmd for {dst_path}")
                                
                                # Método 2: Si sigue existiendo, intentar con DeleteFile de Win32
                                if os.path.exists(dst_path):
                                    try:
                                        import win32api
                                        win32api.DeleteFile(dst_path)
                                        print(f"Attempted delete via win32api for {dst_path}")
                                    except Exception as e:
                                        print(f"Error al eliminar con win32api: {e}")
                                        
                                # Método 3: Programar eliminación en el reinicio
                                if os.path.exists(dst_path):
                                    try:
                                        import win32file, win32con
                                        win32file.MoveFileEx(
                                            dst_path, 
                                            None, 
                                            win32con.MOVEFILE_DELAY_UNTIL_REBOOT
                                        )
                                        print(f"File {dst_path} scheduled for deletion on reboot")
                                    except Exception as e:
                                        print(f"Error al programar eliminación en reinicio: {e}")
                            except Exception as e:
                                print(f"All alternative deletion methods failed: {e}")
        except Exception as e:
            print(f"Error cleaning up incomplete file {dst_path}: {e}")

    def _handle_file_error(self, drive_letter, src_path, dst_path): # Se eliminó 'file_size' del parámetro
        """Handles regular file errors (disk is accessible), cleans up the destination file if it exists, and the queue."""
        self.queue_lock.lock()
        try:
            print(f"Error for file {src_path}. Disk {drive_letter} is accessible. Cleaning up.")
            # Ensure removal from files_in_queue and files_in_queue_by_drive so it's not retried.
            if (src_path, dst_path) in self.files_in_queue:
                self.files_in_queue.remove((src_path, dst_path))
            if hasattr(self, 'files_in_queue_by_drive') and drive_letter in self.files_in_queue_by_drive:
                if (src_path, dst_path) in self.files_in_queue_by_drive[drive_letter]:
                    self.files_in_queue_by_drive[drive_letter].remove((src_path, dst_path))

            # Remove from pending_files_by_drive
            if hasattr(self, 'pending_files_by_drive') and drive_letter in self.pending_files_by_drive:
                self.pending_files_by_drive[drive_letter] = [
                    (src, dst, size) for src, dst, size in self.pending_files_by_drive[drive_letter]
                    if src != src_path or dst != dst_path
                ]

            # Mark the task as done so the queue knows this item has been "processed" (even if it failed).
            self.queues[drive_letter].task_done()
            self.current_copying_file[drive_letter] = None

            # Attempt to delete the destination file if it exists, since it was an error
            if os.path.exists(dst_path):
                try:
                    os.remove(dst_path)
                    print(f"Incomplete/erroneous destination file deleted: {dst_path}")
                except Exception as e:
                    print(f"Error deleting erroneous destination file {dst_path}: {e}")
        finally:
            self.queue_lock.unlock()

    def _cleanup_current_file(self, drive_letter, src_path, dst_path):
        """Cleans up current file references after a successful copy."""
        self.queue_lock.lock()
        try:
            self.queues[drive_letter].task_done()
            if (src_path, dst_path) in self.files_in_queue:
                self.files_in_queue.remove((src_path, dst_path))
            if hasattr(self, 'files_in_queue_by_drive') and drive_letter in self.files_in_queue_by_drive:
                if (src_path, dst_path) in self.files_in_queue_by_drive[drive_letter]:
                    self.files_in_queue_by_drive[drive_letter].remove((src_path, dst_path))
            if hasattr(self, 'pending_files_by_drive') and drive_letter in self.pending_files_by_drive:
                self.pending_files_by_drive[drive_letter] = [
                    (src, dst, size) for src, dst, size in self.pending_files_by_drive[drive_letter]
                    if src != src_path or dst != dst_path
                ]
            self.current_copying_file[drive_letter] = None
        finally:
            self.queue_lock.unlock()

    def _finalize_copy_queue(self, drive_letter):
        """Finaliza la cola de copia y restablece los estados del disco."""
        try:
            self.update_ui_after_copy(drive_letter)
            if drive_letter in self.disks_space_final:
                del self.disks_space_final[drive_letter]
            if drive_letter in self.total_sizes:
                self.total_sizes[drive_letter] = 0
            if drive_letter in self.final_space_text:
                del self.final_space_text[drive_letter]
            if drive_letter in self.system_margins:
                del self.system_margins[drive_letter]
            if drive_letter in self.copy_bytes_copied:
                self.copy_bytes_copied[drive_letter] = 0
            if drive_letter in self.copy_threads:
                del self.copy_threads[drive_letter]
            if drive_letter in self.copy_queue_active:
                self.copy_queue_active[drive_letter] = False
            if drive_letter in self.copy_speeds:
                del self.copy_speeds[drive_letter]
            if drive_letter in self.copy_bytes_copied:
                self.copy_bytes_copied[drive_letter] = 0
            self.remove_disk_from_pending_copies(drive_letter)
            self._check_and_eject_drive(drive_letter)
        except Exception as e:
            print(f"Error en _finalize_copy_queue: {e}")
            traceback.print_exc()

    def _check_and_eject_drive(self, drive_letter):
        """Verifica y ejecuta la expulsión del disco si está programada"""
        if drive_letter in self.disks_to_eject_after_copy:
            print(f"Copy completed, scheduling eject for {drive_letter}")
            self.eject_drive_signal.emit(drive_letter)

    def _cleanup_copy_thread(self, drive_letter, t):
        """Cleans up the copy thread upon completion, regardless of success or error."""
        self.queue_lock.lock()
        try:
            if drive_letter in self.current_copying_file:
                del self.current_copying_file[drive_letter]
            final_progress = 0
            if drive_letter in self.total_sizes and self.total_sizes[drive_letter] > 0:
                final_progress = int((self.copy_bytes_copied.get(drive_letter, 0) / self.total_sizes[drive_letter]) * 100)
            if hasattr(self, 'current_explorer_drive') and self.current_explorer_drive == drive_letter:
                volume_name = self.get_volume_name(drive_letter)
                drive_info_text = f"{volume_name} ({drive_letter})"
                if hasattr(self, 'drive_info_label'):
                    self.drive_info_label.setText(drive_info_text)
            self.total_sizes[drive_letter] = 0
            self.copy_bytes_copied[drive_letter] = 0
            self.final_space_text.pop(drive_letter, None)
            if hasattr(self, 'original_total_sizes'):
                self.original_total_sizes[drive_letter] = 0
            if drive_letter in self.disks_to_eject_after_copy:
                print(f"Final eject check for {drive_letter}")
                self.eject_drive_signal.emit(drive_letter)
        finally:
            self.queue_lock.unlock()
        try:
            QMetaObject.invokeMethod(
                self,
                "update_ui_after_copy",
                Qt.ConnectionType.QueuedConnection,
                Q_ARG(str, drive_letter)
            )
            if drive_letter in self.copy_start_time:
                del self.copy_start_time[drive_letter]
            if drive_letter in self.copy_bytes_copied:
                self.copy_bytes_copied[drive_letter] = 0
            if drive_letter in self.total_sizes:
                self.total_sizes[drive_letter] = 0
            if (hasattr(self, 'current_explorer_drive') and
                    self.current_explorer_drive is not None and
                    drive_letter is not None and
                    self.current_explorer_drive == drive_letter):
                volume_name = self.get_volume_name(drive_letter)
                free_space, _ = get_drive_space(drive_letter)
                free_space_str = self.format_size(free_space)
                drive_info_text = f"{volume_name} ({drive_letter}) | {free_space_str}"
                if hasattr(self, 'drive_info_label'):
                    self.drive_info_label.setText(drive_info_text)
                    if drive_info_text:
                        self.drive_info_label.setText(drive_info_text)
        except Exception as e:
            print(f"Error updating final UI after copy: {e}")
            import traceback
            traceback.print_exc()
    
    @pyqtSlot(str)
    def update_ui_after_copy(self, drive_letter):
        try:
            if drive_letter in self.disks_space_final:
                del self.disks_space_final[drive_letter]
            if drive_letter in self.speed_tracking:
                del self.speed_tracking[drive_letter]
            if drive_letter in self.last_update:
                del self.last_update[drive_letter]
            if drive_letter in self.last_bytes_count:
                del self.last_bytes_count[drive_letter]
            if drive_letter in self.last_valid_speed:
                del self.last_valid_speed[drive_letter]
            if drive_letter in self.last_valid_time:
                del self.last_valid_time[drive_letter]
            item_widget = None
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if item and drive_letter in item.text():
                    item_widget = self.list_widget.itemWidget(item)
                    break
            if item_widget:
                # Actualizar barras de progreso
                if hasattr(item_widget, 'progress_bar'):
                    item_widget.progress_bar.setValue(0)  # Forzar 0%
                    item_widget.progress_bar.setStyleSheet("")  # Limpiar estilos
                if hasattr(item_widget, 'file_progress_bar'):
                    item_widget.file_progress_bar.setValue(0)
                    item_widget.file_progress_bar.hide()
                if hasattr(item_widget, 'speed_label'):
                    item_widget.speed_label.setText("")
                
                # Actualizar etiqueta de volumen
                if hasattr(item_widget, 'volume_label'):
                    try:
                        volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                        item_widget.volume_label.setText(f"{volume_name} ({drive_letter})")
                    except Exception:
                        item_widget.volume_label.setText(f"Disco ({drive_letter})")
                
                # Actualizar etiqueta de espacio libre
                if hasattr(item_widget, 'size_label'):
                    try:
                        free_space, _ = get_drive_space(drive_letter)  # Obtener espacio libre
                        item_widget.size_label.setText(self.format_size(free_space))  # Formatear y mostrar
                        item_widget.size_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                        item_widget.size_label.setVisible(True)
                    except Exception as e:
                        print(f"Error al actualizar espacio libre: {e}")
                item_widget.update()
            
            # Actualizar mini barra de progreso
            if hasattr(self, 'mini_progress_bars') and drive_letter in self.mini_progress_bars:
                mini_bar = self.mini_progress_bars[drive_letter]
                mini_bar.setValue(0)
                mini_bar.hide()
            
            # Actualizar etiqueta de velocidad del explorador
            if hasattr(self, 'explorer_speed_labels') and drive_letter in self.explorer_speed_labels:
                speed_label = self.explorer_speed_labels[drive_letter]
                speed_label.setText("")
                speed_label.hide()
            QApplication.processEvents()
            QTimer.singleShot(500, lambda: self.announce_copy_completion(drive_letter))
        except Exception as e:
            print(f"Error en update_ui_after_copy: {e}")
            import traceback
            traceback.print_exc()

    def remove_disk_from_pending_copies(self, drive_letter): # Elimina un disco del archivo de copias pendientes cuando se completa la cola
        try:
            import json
            import os
            pending_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'pending_copies.json')
            if not os.path.exists(pending_file):
                return
            try:
                with open(pending_file, 'r', encoding='utf-8', errors='ignore') as f:
                    data = json.load(f)
            except json.JSONDecodeError:
                print(f"Error al leer el archivo de copias pendientes. Creando uno nuevo.")
                data = {}
            if isinstance(data, dict) and 'discos' in data:
                discos_actualizados = []
                for disco in data.get('discos', []):
                    if f"({drive_letter})" not in disco:
                        discos_actualizados.append(disco)
                data['discos'] = discos_actualizados
                data['timestamp'] = time.time()
                if not discos_actualizados:
                    try:
                        os.remove(pending_file)
                        print("Archivo de copias pendientes eliminado (no quedan copias pendientes)")
                    except Exception as e:
                        print(f"Error al eliminar archivo de copias pendientes: {e}")
                    return
            else:
                discos_a_eliminar = []
                for disco_key in list(data.keys()):
                    disco_drive = data[disco_key].get('drive_letter', '')
                    if not disco_drive:
                        if f"({drive_letter})" in disco_key or f"({drive_letter.upper()})" in disco_key:
                            discos_a_eliminar.append(disco_key)
                    elif disco_drive == drive_letter or disco_drive == f"{drive_letter}":
                        discos_a_eliminar.append(disco_key)
                for disco in discos_a_eliminar:
                    print(f"Eliminando disco {disco} del archivo de copias pendientes")
                    data.pop(disco, None)
                if not data:
                    try:
                        os.remove(pending_file)
                        print("Archivo de copias pendientes eliminado (no quedan copias pendientes)")
                    except Exception as e:
                        print(f"Error al eliminar archivo de copias pendientes: {e}")
                    return
            temp_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'pending_copies_temp.json')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            os.replace(temp_file, pending_file)
            print(f"Archivo de copias pendientes actualizado. Discos restantes: {len(data)}")
        except Exception as e:
            print(f"Error al eliminar disco de copias pendientes: {e}")
            import traceback
            traceback.print_exc()
    
    def set_thread_priority(self):
        """Establece la prioridad del hilo actual a un nivel normal"""
        try:
            import win32api
            import win32process
            import win32con
            win32process.SetThreadPriority(win32api.GetCurrentThread(), 
                                        win32con.THREAD_PRIORITY_NORMAL)
            print("Prioridad del hilo establecida a normal.")
        except Exception as e:
            print(f"No se pudo ajustar la prioridad del hilo: {e}")
    
    def expulsar_disco_automatico(self, drive_letter): # Ejecuta la expulsión automática del disco
        print(f"Ejecutando expulsión automática para el disco {drive_letter}")
        try:
            # Obtener la ruta base del ejecutable
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            removedrive_path = os.path.join(base_path, 'RemoveDrive.exe')
            if not os.path.exists(removedrive_path):
                print(f"Error: RemoveDrive.exe no encontrado en {removedrive_path}")
                return
            # Verificar operaciones pendientes
            if drive_letter in self.threads and self.threads[drive_letter].is_alive():
                print(f"Aún hay operaciones pendientes en {drive_letter}")
                return
            # Iniciar tarea de expulsión
            task = EjectDriveTask(drive_letter, self)
            self.thread_pool.start(task)
        except Exception as e:
            print(f"Error en expulsión automática: {e}")
            import traceback
            traceback.print_exc()
    
    @pyqtSlot(str)
    def remove_disk_from_view(self, drive_letter):
        try:
            if drive_letter in self.threads:
                thread = self.threads[drive_letter]
                if thread.is_alive():
                    thread.do_run = False
                    thread.join(timeout=1)
                self.threads.pop(drive_letter, None)
            
            # Limpiar datos de copia
            self.queues.pop(drive_letter, None)
            self.total_sizes.pop(drive_letter, None)
            self.copy_bytes_copied.pop(drive_letter, None)
            self.copy_start_time.pop(drive_letter, None)
            self.disks_to_eject_after_copy.pop(drive_letter, None)
            
            # CORRECCIÓN: Limpiar datos de velocidad
            if hasattr(self, 'speed_tracking') and drive_letter in self.speed_tracking:
                del self.speed_tracking[drive_letter]
            if hasattr(self, 'last_update') and drive_letter in self.last_update:
                del self.last_update[drive_letter]
            if hasattr(self, 'last_bytes_count') and drive_letter in self.last_bytes_count:
                del self.last_bytes_count[drive_letter]
            if hasattr(self, 'last_valid_speed') and drive_letter in self.last_valid_speed:
                del self.last_valid_speed[drive_letter]
            if hasattr(self, '_last_speed_update') and drive_letter in self._last_speed_update:
                del self._last_speed_update[drive_letter]
            if hasattr(self, 'speed_window') and drive_letter in self.speed_window:
                del self.speed_window[drive_letter]
            if hasattr(self, '_update_workers') and drive_letter in self._update_workers:
                self._update_workers[drive_letter].stop()
                del self._update_workers[drive_letter]
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if item is not None and drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    self.list_widget.takeItem(i)
                    if widget:
                        if hasattr(widget, 'progress_bar'):
                            widget.progress_bar.setParent(None)
                        if hasattr(widget, 'file_progress_bar'):
                            widget.file_progress_bar.setParent(None)
                        widget.setParent(None)  # Desasociar el widget del item
                        widget.deleteLater()
                    break
            QTimer.singleShot(100, lambda: self.list_widget.update())
            print(f"Disco {drive_letter} eliminado de la vista")
        except Exception as e:
            print(f"Error al eliminar disco de la vista: {e}")
            import traceback
            traceback.print_exc()
    
    def _process_pending_updates(self):
        """Procesa las actualizaciones pendientes de progreso"""
        if hasattr(self, '_pending_updates'):
            # Si estamos deteniendo algún hilo, ignorar actualizaciones para ese disco
            if hasattr(self, 'stopping_threads') and self.stopping_threads:
                for drive_letter in list(self.stopping_threads):
                    if drive_letter in self._pending_updates:
                        print(f"Ignorando actualización pendiente para {drive_letter} porque se está deteniendo")
                        self._pending_updates.pop(drive_letter, None)
                        
            if hasattr(self, '_update_workers'):
                for worker in self._update_workers.values():
                    worker.stop()
                self._update_workers.clear()
            updates = self._pending_updates.copy()
            self._pending_updates.clear()
            self._update_workers = {}
            for drive_letter, progress in updates.items():
                try:
                    worker = UpdateWorker(drive_letter, progress, self)
                    worker.update_ready.connect(self._update_ui_safely)
                    self._update_workers[drive_letter] = worker
                    worker.start()
                except Exception as e:
                    print(f"Error iniciando worker para {drive_letter}: {e}")

    def _update_ui_safely(self, drive_letter, data):
        """Actualiza la UI con los datos procesados por el worker"""
        try:
            # Obtener modo de pago actual
            config = load_config()
            modo_pago = config.get('modo_pago', 'dispositivo')
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget is not None:
                        saved_mode = self.config.get('size_mode', 'Normal')
                        mode = self.config_window.size_modes[saved_mode]
                        progress_bar = widget.progress_bar
                        volume_label = widget.volume_label
                        size_label = widget.findChildren(QLabel)[1]
                        if self.is_copying(drive_letter):
                            # Configurar visibilidad y estilos
                            progress_bar.setVisible(True)
                            size_label.setVisible(False)
                            style = f"color: white; font-size: {mode['font']}px;"
                            volume_label.setStyleSheet(style)
                            widget.size_label.setStyleSheet(style)
                            widget.speed_label.setStyleSheet(style)
                            
                            # Actualizar progreso
                            if data['total_size'] > 0:
                                real_progress = int((data['bytes_copied'] / data['total_size']) * 100)
                                progress_bar.setValue(real_progress)
                                
                                # Actualizar mini barra
                                if drive_letter in self.mini_progress_bars:
                                    mini_bar = self.mini_progress_bars[drive_letter]
                                    if drive_letter == self.current_explorer_drive:
                                        mini_bar.setValue(real_progress)
                                        mini_bar.show()
                                    else:
                                        mini_bar.hide()
                                
                                # Actualizar etiqueta de volumen
                                if volume_label:
                                    total_size_str = self.format_size(data['total_size'])
                                    
                                    # Calcular precio según el modo de pago
                                    precio = None
                                    if modo_pago == "duracion":
                                        duracion_total = 0
                                        if drive_letter in self.queues:
                                            for source, _ in list(self.queues[drive_letter].queue):
                                                if os.path.exists(source):
                                                    duracion = self.get_video_duration(source)
                                                    if duracion is not None:
                                                        duracion_total += duracion
                                        precio = self.calcular_y_mostrar_precio(drive_letter, None, duracion_total)
                                    elif modo_pago == "ficheros":
                                        total_files = 0
                                        if drive_letter in self.queues:
                                            total_files = self.queues[drive_letter].qsize()
                                        precio = self.calcular_y_mostrar_precio(drive_letter, None, None, total_files=total_files)
                                    else:  # modo dispositivo
                                        total_size_gb = data['total_size'] / (1024 ** 3)
                                        precio = self.calcular_y_mostrar_precio(drive_letter, total_size_gb)
                                    if precio is not None:
                                        if modo_pago == "duracion":
                                            volume_label.setText(
                                                f"{data['volume_info']} ({drive_letter})\n[ 𝐓: {total_size_str}] (⏱️${precio:.2f})"
                                            )
                                        elif modo_pago == "ficheros":
                                            volume_label.setText(
                                                f"{data['volume_info']} ({drive_letter})\n[ 𝐓: {total_size_str}] (📜${precio:.2f})"
                                            )
                                        else:
                                            volume_label.setText(
                                                f"{data['volume_info']} ({drive_letter})\n[ 𝐓: {total_size_str}] (💰${precio:.2f})"
                                            )
                                    else:
                                        volume_label.setText(
                                            f"{data['volume_info']} ({drive_letter})\n[ 𝐓: {total_size_str}]"
                                        )
                        else:
                            progress_bar.setVisible(False)
                            size_label.setVisible(True)
                            volume_label.setText(f"{data['volume_info']} ({drive_letter})")
                            size_label.setText(self.format_size(data['free_space']))
                            if drive_letter in self.mini_progress_bars:
                                self.mini_progress_bars[drive_letter].hide()
                        if data['progress'] >= 100:
                            QTimer.singleShot(1000, lambda: self.hide_progress_and_reset_label(drive_letter))
                    break

            # Limpiar worker
            if hasattr(self, '_update_workers') and drive_letter in self._update_workers:
                worker = self._update_workers[drive_letter]
                worker.stop()  # Detener el worker antes de eliminarlo
                worker.deleteLater()
                del self._update_workers[drive_letter]
        except Exception as e:
            print(f"Error actualizando UI para {drive_letter}: {e}")
            traceback.print_exc()
    
    def cleanup_after_stop(self, path):
        if path is None:
            print("No hay ruta para limpiar.")
            return
        if os.path.isfile(path):
            try:
                os.remove(path)
                print(f"Archivo parcial eliminado: {path}")
            except Exception as e:
                print(f"Error al eliminar el archivo {path}: {e}")
        elif os.path.isdir(path):
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                if os.path.isfile(item_path):
                    try:
                        os.remove(item_path)
                        print(f"Archivo parcial eliminado: {item_path}")
                    except Exception as e:
                        print(f"Error al eliminar el archivo {item_path}: {e}")
    
    @pyqtSlot()
    def update_speed_display(self):
        if not hasattr(self, '_last_speed_update'):
            self._last_speed_update = {}
        if not hasattr(self, 'speed_tracking'):
            self.speed_tracking = {}
            self.last_update = {}
            self.last_bytes_count = {}
            self.last_valid_speed = {}
            self.last_valid_time = {}
        if not hasattr(self, 'speed_window'):
            # Ventana deslizante para cálculo de velocidad
            self.speed_window = {}
            self.window_size = 5  # Tamaño de la ventana (5 segundos)
        if not hasattr(self, 'file_size_history'):
            # Historial de tamaños de archivos para detectar patrones
            self.file_size_history = {}
            self.is_small_files_mode = {}
            self.small_files_accumulator = {}
            self.stability_factor = {}
            self.last_stable_speed = {}
            self.speed_trend = {}
        if not hasattr(self, 'disk_speed_diagnostics'):
            # Nuevo: Sistema de diagnóstico de velocidad del disco
            self.disk_speed_diagnostics = {}
            self.raw_speed_samples = {}
            self.peak_speeds = {}
            self.speed_distribution = {}
            self.last_diagnostic_time = {}
        if not hasattr(self, 'speed_config'):
            self.speed_config = {
                'window_size': 5,           # Tamaño de la ventana deslizante (segundos)
                'stability_max': 5.0,       # Factor máximo de estabilidad
                'stability_min': 1.0,       # Factor mínimo de estabilidad
                'small_file_threshold': 1024*1024,  # 1MB
                'medium_file_threshold': 10*1024*1024,  # 10MB
                'large_file_threshold': 100*1024*1024,  # 100MB
                'smoothing_weight_base': 0.7,  # Peso base para suavizado
                'trend_samples': 5,         # Muestras para analizar tendencia
                'variation_threshold': 0.2  # Umbral de variación para estabilidad
            }
        if not hasattr(self, 'copy_bytes_copied'):
            self.copy_bytes_copied = {}
        current_time = time.time()
        try:
            with self.copy_data_lock:
                for i in range(self.list_widget.count()):
                    try:
                        item = self.list_widget.item(i)
                        drive_letter = item.text().split(' ')[-1].strip('()')
                        
                        # Solo actualizar la etiqueta del explorador si es el disco actual
                        if hasattr(self, 'explorer_speed_labels') and drive_letter in self.explorer_speed_labels and hasattr(self, 'current_explorer_drive') and drive_letter == self.current_explorer_drive:
                            explorer_label = self.explorer_speed_labels[drive_letter]
                            if self.is_copying(drive_letter) and drive_letter in self.last_valid_speed:
                                speed_value = self.last_valid_speed[drive_letter]
                                time_remaining = self.last_valid_time.get(drive_letter, "00:00:00")
                                if speed_value >= 1000:
                                    speed_str = f"{speed_value/1000:.1f} GB/s"
                                else:
                                    speed_str = f"{speed_value:.1f} MB/s"
                                explorer_label.setText(f"{speed_str} - {time_remaining}")
                                explorer_label.show()
                            else:
                                explorer_label.hide()
                        # Ocultar las etiquetas de otros discos
                        elif hasattr(self, 'explorer_speed_labels') and drive_letter in self.explorer_speed_labels:
                            self.explorer_speed_labels[drive_letter].hide()
                        if drive_letter not in self.speed_window:
                            self.speed_window[drive_letter] = []
                        if drive_letter not in self.file_size_history:
                            self.file_size_history[drive_letter] = []
                        if drive_letter not in self.is_small_files_mode:
                            self.is_small_files_mode[drive_letter] = False
                        if drive_letter not in self.small_files_accumulator:
                            self.small_files_accumulator[drive_letter] = {'bytes': 0, 'time': 0}
                        if drive_letter not in self.stability_factor:
                            self.stability_factor[drive_letter] = 1.0
                        if drive_letter not in self.last_stable_speed:
                            self.last_stable_speed[drive_letter] = 0.0
                        if drive_letter not in self.speed_trend:
                            self.speed_trend[drive_letter] = []
                        if drive_letter not in self.disk_speed_diagnostics:
                            self.disk_speed_diagnostics[drive_letter] = {
                                'raw_samples': [],
                                'peak_speed': 0.0,
                                'distribution': {
                                    'very_low': 0,    # 0-10 MB/s
                                    'low': 0,         # 10-30 MB/s
                                    'medium': 0,      # 30-60 MB/s
                                    'high': 0,        # 60-100 MB/s
                                    'very_high': 0    # >100 MB/s
                                },
                                'file_sizes': {
                                    'small': 0,       # <1MB
                                    'medium': 0,      # 1MB-10MB
                                    'large': 0,       # 10MB-100MB
                                    'very_large': 0   # >100MB
                                },
                                'last_diagnostic': 0
                            }
                            self.raw_speed_samples[drive_letter] = []
                            self.peak_speeds[drive_letter] = 0.0
                            self.speed_distribution[drive_letter] = {}
                            self.last_diagnostic_time[drive_letter] = 0
                        if drive_letter in self._last_speed_update:
                            if current_time - self._last_speed_update[drive_letter] < 1.0:
                                continue
                        self._last_speed_update[drive_letter] = current_time
                        if (drive_letter not in self.queues or 
                            self.queues[drive_letter].empty() and 
                            drive_letter not in self.current_copying_file):
                            continue
                        if drive_letter not in self.copy_bytes_copied:
                            self.copy_bytes_copied[drive_letter] = 0
                        widget = self.list_widget.itemWidget(item)
                        if widget is None:
                            continue
                        speed_label = widget.findChildren(QLabel)[2]
                        progress_bar = widget.progress_bar
                        final_space_str = self.final_space_text.get(drive_letter, '')
                        final_space_text = f" [ 𝐅: {final_space_str}]" if final_space_str else ""
                        if drive_letter in self.pause_times:
                            speed_label.setText(f"{final_space_text}\nPausado")
                            continue
                        if drive_letter not in self.speed_tracking:
                            self.speed_tracking[drive_letter] = []
                            self.last_update[drive_letter] = current_time
                            self.last_bytes_count[drive_letter] = self.copy_bytes_copied[drive_letter]
                            self.last_valid_speed[drive_letter] = 0.0
                            self.last_valid_time[drive_letter] = "00:00:00"
                        if drive_letter in self.copy_start_time and drive_letter in self.queues:
                            try:
                                current_bytes = self.copy_bytes_copied[drive_letter]
                                if current_bytes < 0:
                                    current_bytes = 0
                                    self.copy_bytes_copied[drive_letter] = 0
                                if drive_letter in self.last_update:
                                    time_delta = current_time - self.last_update[drive_letter]
                                    bytes_delta = current_bytes - self.last_bytes_count[drive_letter]
                                    if bytes_delta < 0:
                                        bytes_delta = 0
                                else:
                                    time_delta = 0
                                    bytes_delta = 0
                                self.last_update[drive_letter] = current_time
                                self.last_bytes_count[drive_letter] = current_bytes
                                current_file = None
                                if drive_letter in self.current_copying_file:
                                    current_file = self.current_copying_file[drive_letter]
                                if current_file and isinstance(current_file, tuple) and len(current_file) >= 1:
                                    try:
                                        current_file_path = current_file[0]
                                        if os.path.exists(current_file_path):
                                            current_file_size = os.path.getsize(current_file_path)
                                            self.file_size_history[drive_letter].append(current_file_size)
                                            if len(self.file_size_history[drive_letter]) > 10:
                                                self.file_size_history[drive_letter].pop(0)
                                            if len(self.file_size_history[drive_letter]) >= 3:
                                                small_files_count = sum(1 for size in self.file_size_history[drive_letter] 
                                                                    if size < self.speed_config['small_file_threshold'])
                                                self.is_small_files_mode[drive_letter] = small_files_count >= 3
                                            if current_file_size < self.speed_config['small_file_threshold']:
                                                self.stability_factor[drive_letter] = min(
                                                    self.speed_config['stability_max'], 
                                                    self.stability_factor[drive_letter] + 0.5
                                                )
                                                self.disk_speed_diagnostics[drive_letter]['file_sizes']['small'] += 1
                                            elif current_file_size < self.speed_config['medium_file_threshold']:
                                                self.stability_factor[drive_letter] = min(
                                                    3.0, 
                                                    self.stability_factor[drive_letter] + 0.2
                                                )
                                                self.disk_speed_diagnostics[drive_letter]['file_sizes']['medium'] += 1
                                            elif current_file_size < self.speed_config['large_file_threshold']:
                                                self.stability_factor[drive_letter] = max(
                                                    1.5, 
                                                    self.stability_factor[drive_letter] - 0.2
                                                )
                                                self.disk_speed_diagnostics[drive_letter]['file_sizes']['large'] += 1
                                            else:
                                                self.stability_factor[drive_letter] = self.speed_config['stability_min']
                                                self.disk_speed_diagnostics[drive_letter]['file_sizes']['very_large'] += 1
                                    except Exception as e:
                                        pass
                                if self.is_small_files_mode[drive_letter]:
                                    self.small_files_accumulator[drive_letter]['bytes'] += bytes_delta
                                    self.small_files_accumulator[drive_letter]['time'] += time_delta
                                    if self.small_files_accumulator[drive_letter]['time'] > 5:
                                        self.small_files_accumulator[drive_letter] = {'bytes': 0, 'time': 0}
                                if time_delta > 0:
                                    speed = bytes_delta / (time_delta * 1024 * 1024)
                                    if speed < 0 or speed > 1000:  # Límite superior razonable (1 GB/s)
                                        if self.last_valid_speed.get(drive_letter, 0) > 0:
                                            speed = self.last_valid_speed[drive_letter]
                                        else:
                                            speed = 0
                                    if speed > 0 and speed < 1000:  # Filtrar valores extremos
                                        self.raw_speed_samples[drive_letter].append(speed)
                                        if len(self.raw_speed_samples[drive_letter]) > 100:  # Mantener solo las últimas 100 muestras
                                            self.raw_speed_samples[drive_letter].pop(0)
                                        if speed > self.peak_speeds[drive_letter]:
                                            self.peak_speeds[drive_letter] = speed
                                        if speed < 10:
                                            self.disk_speed_diagnostics[drive_letter]['distribution']['very_low'] += 1
                                        elif speed < 30:
                                            self.disk_speed_diagnostics[drive_letter]['distribution']['low'] += 1
                                        elif speed < 60:
                                            self.disk_speed_diagnostics[drive_letter]['distribution']['medium'] += 1
                                        elif speed < 100:
                                            self.disk_speed_diagnostics[drive_letter]['distribution']['high'] += 1
                                        else:
                                            self.disk_speed_diagnostics[drive_letter]['distribution']['very_high'] += 1
                                    self.speed_window[drive_letter].append({'time': current_time, 'speed': speed, 'bytes': bytes_delta})
                                    while self.speed_window[drive_letter] and current_time - self.speed_window[drive_letter][0]['time'] > self.window_size:
                                        self.speed_window[drive_letter].pop(0)
                                    if self.speed_window[drive_letter]:
                                        total_bytes = sum(entry['bytes'] for entry in self.speed_window[drive_letter])
                                        total_time = current_time - self.speed_window[drive_letter][0]['time']
                                        if total_time > 0:
                                            window_speed = total_bytes / (total_time * 1024 * 1024)
                                            if speed < 5 and self.last_valid_speed.get(drive_letter, 0) > 30:
                                                smoothed_speed = self.last_valid_speed[drive_letter] * 0.8
                                            else:
                                                smoothed_speed = speed  # Usar la velocidad instantánea
                                            if smoothed_speed > 0:
                                                self.last_valid_speed[drive_letter] = smoothed_speed
                                            self.speed_trend[drive_letter].append(smoothed_speed)
                                            if len(self.speed_trend[drive_letter]) > 5:
                                                self.speed_trend[drive_letter].pop(0)
                                            if drive_letter not in self.last_diagnostic_time or current_time - self.last_diagnostic_time.get(drive_letter, 0) > 30:
                                                self.last_diagnostic_time[drive_letter] = current_time
                                                self.diagnose_disk_performance(drive_letter)
                                            self.speed_tracking[drive_letter].append(speed)
                                            if len(self.speed_tracking[drive_letter]) > 5:
                                                self.speed_tracking[drive_letter].pop(0)
                                            weights = [0.1, 0.15, 0.2, 0.25, 0.3]  # Pesos para hasta 5 muestras
                                            while len(weights) > len(self.speed_tracking[drive_letter]):
                                                weights.pop(0)
                                            total = sum(weights)
                                            weights = [w/total for w in weights]
                                            avg_speed = sum(s * w for s, w in zip(self.speed_tracking[drive_letter], weights))
                                            if smoothed_speed >= 1000:
                                                speed_str = f"{smoothed_speed/1000:.1f} GB/s"
                                            else:
                                                speed_str = f"{smoothed_speed:.1f} MB/s"
                                            total_size = self.total_sizes.get(drive_letter, 0)
                                            if total_size > 0:
                                                bytes_remaining = total_size - current_bytes
                                                if smoothed_speed > 0:
                                                    time_remaining = bytes_remaining / (smoothed_speed * 1024 * 1024)
                                                    if 'math' in globals() and time_remaining > 60:
                                                        time_remaining = math.ceil(time_remaining / 10) * 10
                                                    hours, remainder = divmod(time_remaining, 3600)
                                                    minutes, seconds = divmod(remainder, 60)
                                                    time_remaining_str = f"{int(hours):02}:{int(minutes):02}:{int(seconds):02}"
                                                    self.last_valid_time[drive_letter] = time_remaining_str
                                                else:
                                                    time_remaining_str = self.last_valid_time[drive_letter]
                                                percentage = min((current_bytes / total_size) * 100, 100)
                                                percentage_int = int(percentage)
                                                if hasattr(progress_bar, 'last_value') and progress_bar.last_value == percentage_int:
                                                    pass  
                                                else:
                                                    progress_bar.setValue(percentage_int)
                                                    progress_bar.last_value = percentage_int
                                                if drive_letter in self.mini_progress_bars and drive_letter == self.current_explorer_drive:
                                                    mini_bar = self.mini_progress_bars[drive_letter]
                                                    mini_bar.setValue(percentage_int)
                                                    mini_bar.show()
                                                if final_space_text:
                                                    speed_label.setText(f"{final_space_text} \n{speed_str} | {time_remaining_str}")
                                                else:
                                                    speed_label.setText(f"\n{speed_str} | {time_remaining_str}")
                                            else:
                                                if final_space_text:
                                                    speed_label.setText(f"{final_space_text} 0%\n{speed_str} (00:00:00)")
                                                else:
                                                    speed_label.setText(f"0%\n{speed_str} (00:00:00)")
                                    else:
                                        if final_space_text:
                                            speed_label.setText(f"{final_space_text} 0%\n{self.last_valid_speed[drive_letter]:.1f} MB/s (00:00:00)")
                                        else:
                                            speed_label.setText(f"0%\n{self.last_valid_speed[drive_letter]:.1f} MB/s (00:00:00)")
                                else:
                                    if drive_letter in self.last_valid_speed:
                                        speed_str = f"{self.last_valid_speed[drive_letter]:.1f} MB/s"
                                        if drive_letter in self.last_valid_time:
                                            time_remaining_str = self.last_valid_time[drive_letter]
                                        else:
                                            time_remaining_str = "00:00:00"
                                        total_size = self.total_sizes.get(drive_letter, 0)
                                        if total_size > 0:
                                            percentage = min((current_bytes / total_size) * 100, 100)
                                            percentage_int = int(percentage)
                                            if final_space_text:
                                                speed_label.setText(f"{final_space_text} \n{speed_str} | {time_remaining_str}")
                                            else:
                                                speed_label.setText(f" \n{speed_str} | {time_remaining_str}")
                            except Exception as e:
                                speed_label.setText(final_space_text if final_space_text else "")
                        else:
                            speed_label.setText(final_space_text if final_space_text else "")
                    except Exception as e:
                        continue
        except Exception as e:
            import traceback
            traceback.print_exc()

    def diagnose_disk_performance(self, drive_letter):
        """Realiza un diagnóstico completo del rendimiento del disco"""
        if drive_letter not in self.raw_speed_samples or not self.raw_speed_samples[drive_letter]:
            return
        samples = self.raw_speed_samples[drive_letter]
        sorted_samples = sorted(samples)
        if len(sorted_samples) > 20:
            trim_size = max(1, int(len(sorted_samples) * 0.1))
            trimmed_samples = sorted_samples[trim_size:-trim_size]
        else:
            trimmed_samples = sorted_samples
        avg_speed = sum(trimmed_samples) / len(trimmed_samples)
        median_speed = sorted_samples[len(sorted_samples)//2]
        p90_speed = sorted_samples[int(len(sorted_samples)*0.9)]
        peak_speed = self.peak_speeds[drive_letter]
        distribution = self.disk_speed_diagnostics[drive_letter]['distribution']
        total_samples = sum(distribution.values())
        problems = []
        if avg_speed < 10:  # Menos de 10 MB/s
            problems.append("Velocidad extremadamente baja")
        if p90_speed > 3 * median_speed:
            problems.append("Alta variabilidad en la velocidad")
        file_sizes = self.disk_speed_diagnostics[drive_letter]['file_sizes']
        total_files = sum(file_sizes.values())
        if total_files > 0 and file_sizes['small'] / total_files > 0.7:
            problems.append("Predominio de archivos pequeños (afecta rendimiento)")
        if total_samples > 0 and distribution['very_low'] > 0.3 * total_samples and distribution['high'] > 0.1 * total_samples:
            problems.append("Velocidad inconsistente (posible problema de fragmentación)")

    @pyqtSlot(str, int)
    def update_total_size(self, drive_letter, new_size, force_recalculate=False, total_files=None, duracion_min=None):
        """Actualiza el tamaño total de archivos a copiar para un disco"""
        try:
            config = load_config()
            modo_pago = config.get('modo_pago', 'dispositivo')
            if drive_letter not in self.total_sizes:
                self.total_sizes[drive_letter] = 0
            free_space, total_space = get_drive_space(drive_letter)
            is_indexing = hasattr(self, '_is_indexing') and self._is_indexing
            if self.total_sizes[drive_letter] < 0:
                print(f"Corrigiendo total_sizes negativo para {drive_letter}: {self.total_sizes[drive_letter]}")
                self.total_sizes[drive_letter] = 0
            is_copying_active = self.is_copying(drive_letter) or force_recalculate
            safety_margin = 0
            if not hasattr(self, 'system_margins'):
                self.system_margins = {}
            if drive_letter not in self.system_margins or force_recalculate:
                occupied_space = total_space - free_space
                system_margin = occupied_space - self.total_sizes.get(drive_letter, 0)
                if system_margin < 0:
                    system_margin = 0
                self.system_margins[drive_letter] = system_margin
                print(f"Margen del sistema calculado para {drive_letter}: {self.format_size(system_margin)}")
            if is_indexing:
                total_size = new_size
            else:
                total_size = self.total_sizes[drive_letter] + new_size
            if total_size < 0:
                print(f"Total calculado negativo para {drive_letter}: {total_size}, corrigiendo a 0")
                total_size = 0
            usable_space = total_space - self.system_margins[drive_letter]
            bytes_copied = self.copy_bytes_copied.get(drive_letter, 0)
            if new_size < 0 and not is_indexing:
                print(f"Eliminando archivo, tamaño: {self.format_size(abs(new_size))}")
                if bytes_copied > total_size:
                    print(f"Ajustando bytes_copied de {self.format_size(bytes_copied)} a {self.format_size(total_size)}")
                    bytes_copied = total_size
            bytes_copied = min(bytes_copied, total_size)
            bytes_pendientes = max(0, total_size - bytes_copied)
            if is_copying_active:
                espacio_final = max(0, usable_space - total_size)
                print(f"Copia en progreso o reinicio forzado - Usando espacio usable del disco para cálculo:")
                print(f"  - Tamaño total del disco: {self.format_size(total_space)}")
                print(f"  - Margen del sistema: {self.format_size(self.system_margins[drive_letter])}")
                print(f"  - Espacio usable del disco: {self.format_size(usable_space)}")
                print(f"  - Tamaño total a copiar: {self.format_size(total_size)}")
            else:
                espacio_final = max(0, free_space - bytes_pendientes)
                print(f"Sin copia en progreso - Usando espacio libre actual:")
                print(f"  - Espacio libre actual: {self.format_size(free_space)}")
                print(f"  - Bytes pendientes: {self.format_size(bytes_pendientes)}")
            espacio_final_mb = espacio_final / (1024 * 1024)
            import math
            espacio_final_mb_redondeado = round(espacio_final_mb * 10) / 10
            espacio_final = espacio_final_mb_redondeado * 1024 * 1024
            print(f"Actualización de tamaño total para {drive_letter}:")
            print(f"  - Espacio libre actual: {self.format_size(free_space)}")
            print(f"  - {'Nuevo tamaño total' if is_indexing else 'Tamaño a añadir'}: {self.format_size(new_size)}")
            print(f"  - Total acumulado anterior: {self.format_size(self.total_sizes[drive_letter])}")
            print(f"  - Nuevo total acumulado: {self.format_size(total_size)}")
            print(f"  - Bytes ya copiados: {self.format_size(bytes_copied)}")
            print(f"  - Bytes pendientes: {self.format_size(bytes_pendientes)}")
            print(f"  - Espacio final calculado: {self.format_size(espacio_final)}")
            print(f"  - Espacio final en MB (redondeado): {espacio_final_mb_redondeado} MB")
            final_space_str = self.format_size(espacio_final)
            self.total_sizes[drive_letter] = total_size
            self.final_space_text[drive_letter] = final_space_str
            if free_space < bytes_pendientes and not is_copying_active:
                print(f"ADVERTENCIA: No hay suficiente espacio en {drive_letter}")
                print(f"  - Espacio libre: {self.format_size(free_space)}")
                print(f"  - Espacio necesario: {self.format_size(bytes_pendientes)}")
                print(f"  - Faltante: {self.format_size(bytes_pendientes - free_space)}")
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget is not None:
                        volume_label = widget.volume_label
                        original_text = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                        total_size_str = self.format_size(self.total_sizes[drive_letter])
                        precio = None
                        if modo_pago == "duracion":
                            duracion_total = 0
                            if drive_letter in self.queues:
                                for source, _ in list(self.queues[drive_letter].queue):
                                    if os.path.exists(source):
                                        duracion = self.get_video_duration(source)
                                        if duracion is not None:
                                            duracion_total += duracion
                            precio = self.calcular_y_mostrar_precio(drive_letter, None, duracion_total)
                        elif modo_pago == "ficheros":
                            total_files = 0
                            if drive_letter in self.queues:
                                total_files = self.queues[drive_letter].qsize()
                            precio = self.calcular_y_mostrar_precio(drive_letter, None, None, total_files=total_files)
                        else: 
                            total_size_gb = self.total_sizes[drive_letter] / (1024 ** 3)
                            precio = self.calcular_y_mostrar_precio(drive_letter, total_size_gb)
                        if precio is not None:
                            if modo_pago == "duracion":
                                volume_label.setText(
                                    f"{original_text} ({drive_letter})\n[ 𝐓: {total_size_str}] (⏱️${precio:.2f})"
                                )
                            elif modo_pago == "ficheros":
                                volume_label.setText(
                                    f"{original_text} ({drive_letter})\n[ 𝐓: {total_size_str}] (📜${precio:.2f})"
                                )
                            else:
                                volume_label.setText(
                                    f"{original_text} ({drive_letter})\n[ 𝐓: {total_size_str}] (💰${precio:.2f})"
                                )
                        else:
                            volume_label.setText(f"{original_text} ({drive_letter})\n[ 𝐓: {total_size_str}]")

                        # Y en la sección del header del explorador:
                        if hasattr(self, 'current_explorer_drive') and self.current_explorer_drive == drive_letter:
                            volume_name = self.get_volume_name(drive_letter)
                            if self.total_sizes[drive_letter] > 0:
                                total_size_str = self.format_size(self.total_sizes[drive_letter])

                                # Calcular precio para el header según el modo
                                precio = None
                                if modo_pago == "duracion":
                                    duracion_total = 0
                                    if drive_letter in self.queues:
                                        for source, _ in list(self.queues[drive_letter].queue):
                                            if os.path.exists(source):
                                                duracion = self.get_video_duration(source)
                                                if duracion is not None:
                                                    duracion_total += duracion
                                    precio = self.calcular_y_mostrar_precio(drive_letter, None, duracion_total)
                                elif modo_pago == "ficheros":
                                    total_files = 0
                                    if drive_letter in self.queues:
                                        total_files = self.queues[drive_letter].qsize()
                                    precio = self.calcular_y_mostrar_precio(drive_letter, None, None, total_files=total_files)
                                else:
                                    total_size_gb = self.total_sizes[drive_letter] / (1024 ** 3)
                                    precio = self.calcular_y_mostrar_precio(drive_letter, total_size_gb)
                                if precio is not None:
                                    if modo_pago == "duracion":
                                        drive_info_text = f"{volume_name} ({drive_letter}) | 𝐓: {total_size_str} (⏱️${precio:.2f}) [ 𝐅: {final_space_str}]"
                                    elif modo_pago == "ficheros":
                                        drive_info_text = f"{volume_name} ({drive_letter}) | 𝐓: {total_size_str} (📜${precio:.2f}) [ 𝐅: {final_space_str}]"
                                    else:
                                        drive_info_text = f"{volume_name} ({drive_letter}) | 𝐓: {total_size_str} (💰${precio:.2f}) [ 𝐅: {final_space_str}]"
                                else:
                                    drive_info_text = f"{volume_name} ({drive_letter}) | 𝐓: {total_size_str} [ 𝐅: {final_space_str}]"
                        size_label = widget.findChildren(QLabel)[1]
                        size_label.setText(self.final_space_text[drive_letter])
                        break

            # Actualizar header del explorador
            if hasattr(self, 'current_explorer_drive') and self.current_explorer_drive == drive_letter:
                volume_name = self.get_volume_name(drive_letter)
                if self.total_sizes[drive_letter] > 0:
                    total_size_str = self.format_size(self.total_sizes[drive_letter])

                    # Calcular precio para el header según el modo
                    precio = None
                    if modo_pago == "duracion":
                        duracion_total = 0
                        if drive_letter in self.queues:
                            for source, _ in list(self.queues[drive_letter].queue):
                                if os.path.exists(source):
                                    duracion = self.get_video_duration(source)
                                    if duracion is not None:
                                        duracion_total += duracion
                        precio = self.calcular_y_mostrar_precio(drive_letter, None, duracion_total)
                    elif modo_pago == "ficheros":
                        total_files = 0
                        if drive_letter in self.queues:
                            total_files = self.queues[drive_letter].qsize()
                        precio = self.calcular_y_mostrar_precio(drive_letter, None, None, total_files=total_files)
                    else:  
                        total_size_gb = self.total_sizes[drive_letter] / (1024 ** 3)
                        precio = self.calcular_y_mostrar_precio(drive_letter, total_size_gb)
                    if precio is not None:
                        if modo_pago == "duracion":
                            drive_info_text = f"{volume_name} ({drive_letter}) | 𝐓: {total_size_str} (⏱️${precio:.2f}) [ 𝐅: {final_space_str}]"
                        elif modo_pago == "ficheros":
                            drive_info_text = f"{volume_name} ({drive_letter}) | 𝐓: {total_size_str} (📜${precio:.2f}) [ 𝐅: {final_space_str}]"
                        else:
                            drive_info_text = f"{volume_name} ({drive_letter}) | 𝐓: {total_size_str} (💰${precio:.2f}) [ 𝐅: {final_space_str}]"
                    else:
                        drive_info_text = f"{volume_name} ({drive_letter}) | 𝐓: {total_size_str} [ 𝐅: {final_space_str}]"
                else:
                    drive_info_text = f"{volume_name} ({drive_letter})"
                if hasattr(self, 'drive_info_label'):
                    self.drive_info_label.setText(drive_info_text)
            self.save_pending_copies()
        except Exception as e:
            print(f"Error en update_total_size: {e}")
            traceback.print_exc()
    
    def update_size_label(self, drive_letter):
        free_space, _ = get_drive_space(drive_letter)
        final_space = free_space - self.total_sizes.get(drive_letter, 0)
        final_space_str = self.format_size(final_space)
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if drive_letter in item.text():
                widget = self.list_widget.itemWidget(item)
                if widget is not None:
                    size_label = widget.findChildren(QLabel)[1]
                    size_label.setText(final_space_str)
                    print(f"Etiqueta de tamaño actualizada para {drive_letter} {final_space_str}")
                    self.final_space_text[drive_letter] = final_space_str
                    break
    
    def mousePressEvent(self, event):
        self.window_resizer.handle_mouse_press(event)
    def mouseMoveEvent(self, event):
        self.window_resizer.handle_mouse_move(event)
    def mouseReleaseEvent(self, event):
        self.window_resizer.handle_mouse_release(event)

    def update_disk_item_size(self, height, font_size):
        saved_mode = self.config.get('size_mode', 'Normal')
        mode = self.config_window.size_modes[saved_mode]
        self.list_widget.setIconSize(QSize(mode["icon"], mode["icon"]))
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            widget = self.list_widget.itemWidget(item)
            if widget:
                widget.setFixedHeight(mode["height"])
                item.setSizeHint(QSize(item.sizeHint().width(), mode["height"]))
                font = QFont()
                font.setBold(True)
                font.setPointSize(mode["font"])
                style = f"color: white; font-size: {mode['font']}px;"
                if hasattr(widget, 'volume_label'):
                    widget.volume_label.setFont(font)
                    widget.volume_label.setStyleSheet(style)
                if hasattr(widget, 'size_label'):
                    widget.size_label.setFont(font)
                    widget.size_label.setStyleSheet(style)
                if hasattr(widget, 'speed_label'):
                    widget.speed_label.setFont(font)
                    widget.speed_label.setStyleSheet(style)
                if hasattr(widget, 'progress_bar'):
                    progress_bar = widget.progress_bar
                    progress_bar.setFixedHeight(mode["progress"])
                    progress_bar.setGeometry(0, 0, widget.width(), mode["progress"])
                    if progress_bar.isVisible():
                        current_value = progress_bar.value()
                        progress_bar.setValue(0)  # Reset temporal
                        progress_bar.setValue(current_value)  # Restaurar valor
                if hasattr(widget, 'file_progress_bar'):
                    file_bar = widget.file_progress_bar
                    file_bar.setFixedHeight(mode["file"])
                    file_bar.setGeometry(0, mode["file_offset"], widget.width(), mode["file"])
                    if file_bar.isVisible():
                        current_value = file_bar.value()
                        file_bar.setValue(0)  # Reset temporal
                        file_bar.setValue(current_value)  # Restaurar valor
        self.list_widget.update()

    def configure_progress_bars(self, widget, drive_letter):
        """Configura las barras de progreso según el modo actual"""
        saved_mode = self.config.get('size_mode', 'Normal')
        mode = self.config_window.size_modes[saved_mode]
        progress_bar = widget.progress_bar
        progress_bar.setFixedHeight(mode["progress"])
        progress_bar.setGeometry(0, 0, widget.width(), mode["progress"])
        progress_bar.show()
        file_progress_bar = widget.file_progress_bar
        file_y = mode["file_offset"]
        file_progress_bar.setGeometry(0, file_y, widget.width(), mode["file"])
        file_progress_bar.setFixedHeight(mode["file"])
        file_progress_bar.show()
        if hasattr(self.main_window, 'is_copying') and self.main_window.is_copying(drive_letter):
            progress_bar.setVisible(True)
            file_progress_bar.setVisible(True)
    
    def map_port(self):
        """Maneja el mapeo de puertos físicos del PC delegando al módulo MAPEO_PUERTO"""
        from MAPEO_PUERTO import map_port
        map_port(self)
    
    def _on_port_id_ready(self, port_id, current_name, original_cursor):
        """Función callback cuando se completa la identificación del puerto"""
        from MAPEO_PUERTO import on_port_id_ready
        on_port_id_ready(self, port_id, current_name, original_cursor)

    def _get_port_id_fallback(self, drive_letter):
        """Método para obtener ID único de puerto físico usando información del sistema"""
        from MAPEO_PUERTO import get_port_id_fallback
        return get_port_id_fallback(drive_letter)
    
    def get_usb_port_info(self, drive_letter):
        """Obtiene la información del puerto físico para un disco específico"""
        from MAPEO_PUERTO import get_usb_port_info
        return get_usb_port_info(drive_letter)

    def _get_usb_port_info_fallback(self, drive_letter):
        """Método de respaldo para obtener información del puerto"""
        from MAPEO_PUERTO import get_usb_port_info_fallback
        return get_usb_port_info_fallback(drive_letter)

    def _show_port_rename_dialog(self, port_id, current_name, device_info=None):
        """Muestra el diálogo de renombrado del puerto"""
        from MAPEO_PUERTO import show_port_rename_dialog
        show_port_rename_dialog(self, port_id, current_name, device_info)
    
    def get_port_name(self, port_id):
        """Obtiene el nombre asignado a un puerto específico"""
        from MAPEO_PUERTO import get_port_name
        return get_port_name(port_id)

    def update_changed_items(self, path):
        try:
            if not path or not os.path.exists(path):
                return
            current_items = set()
            for i in range(self.file_tree_widget.topLevelItemCount()):
                item = self.file_tree_widget.topLevelItem(i)
                current_items.add(item.text(0))
            actual_items = set()
            try:
                with os.scandir(path) as scanner:
                    for entry in scanner:
                        try:
                            if (entry.name.startswith('.') or 
                                bool(os.stat(entry.path).st_file_attributes & 0x2) or 
                                not os.access(entry.path, os.R_OK)):
                                continue
                            actual_items.add(entry.name)
                        except Exception:
                            continue
            except Exception as e:
                print(f"Error leyendo directorio {path}: {e}")
                return
            for item_name in actual_items - current_items:
                try:
                    full_path = os.path.join(path, item_name)
                    self.add_item_to_tree(self.file_tree_widget, item_name, os.path.isdir(full_path))
                except Exception as e:
                    if "FileTypeIconManager" not in str(e):
                        print(f"Error agregando item {item_name}: {e}")
            items_to_remove = []
            for i in range(self.file_tree_widget.topLevelItemCount()):
                item = self.file_tree_widget.topLevelItem(i)
                if item.text(0) not in actual_items:
                    items_to_remove.append(item)
            for item in items_to_remove:
                index = self.file_tree_widget.indexOfTopLevelItem(item)
                self.file_tree_widget.takeTopLevelItem(index)
        except Exception as e:
            print(f"Error actualizando items: {e}")

    def add_item_to_tree(self, parent, name, is_dir=False):
        try:
            item = QTreeWidgetItem(parent)
            item.setText(0, name)
            if is_dir:
                icon = self.icon_manager.get_folder_icon()
            else:
                icon = self.icon_manager.get_file_icon(name)
            item.setIcon(0, icon)
            return item
        except Exception as e:
            print(f"Error al agregar item {name}: {str(e)}")
            return None

    def apply_disk_filter(self, filter_type):
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            widget = self.list_widget.itemWidget(item)
            if widget:
                text = widget.volume_label.text().lower()
                drive_letter = text.split('(')[-1].strip(')')
                should_show = False
                drive_type = self.worker.get_drive_type(drive_letter + "\\")
                if filter_type == 'all':
                    should_show = True
                elif filter_type == 'internal':
                    should_show = (drive_type == "Disco duro interno" or 
                                 drive_letter.upper().startswith("C:"))
                elif filter_type == 'external':
                    should_show = (drive_type == "Disco duro externo" and 
                                 not drive_letter.upper().startswith("C:"))
                elif filter_type == 'usb':
                    should_show = drive_type == "Unidad extraíble (USB)"
                item.setHidden(not should_show)
                print(f"Drive {drive_letter}: Type={drive_type}, Filter={filter_type}, Show={should_show}")
        self.list_widget.update()

    def is_file_being_copied(self, file_path):
        """Verifica si un archivo está siendo copiado actualmente"""
        if hasattr(self, 'copy_worker') and self.copy_worker.is_running:
            dest_drive = self.copy_worker.destination_drive # Obtener la ruta de destino de la copia actual
            if file_path.startswith(dest_drive):
                for source, dest in self.copy_worker.pending_files: # Verificar si el archivo está en la cola de copia
                    if dest == file_path:
                        return True
        return False

    def on_license_checked(self, is_valid): # REVISAR DONDE SE LLAMA A ESTE METODO 
        """Maneja el resultado de la verificación de licencia"""
        if is_valid:
            print("Licencia válida y activa")
            self.license_active = True
            self.enable_main_window()
        else:
            print("Licencia no válida o expirada")
            self.license_active = False
            self.disable_main_window()

    def preload_settings_window(self): # Precarga la ventana de ajustes en segundo plano
        try:
            from AJUSTES import TransparentWindow
            self.settings_window = TransparentWindow(self)
            # Añadir referencia explícita a la ventana principal
            self.settings_window.main_window = self
            self.settings_window.cargar_discos_exentos()
            self.settings_window.actualizar_lista_discos()
            print("Ventana de ajustes precargada exitosamente")
        except Exception as e:
            print(f"Error al precargar ventana de ajustes: {e}")

class UsbScanWorker(QThread):
    finished = pyqtSignal(dict)
    def run(self):
        try:
            from Propiedades_Disco import cargar_mappings_usb, guardar_disco_puerto_mapping
            cargar_mappings_usb()
            saved_mappings = {}
            if os.path.exists('USBPorts.txt'):
                try:
                    with open('USBPorts.txt', 'r', encoding='utf-8') as f:
                        for line in f:
                            key, val = line.strip().split(':', 1)
                            saved_mappings[key] = val
                except Exception as e:
                    print(f"Error cargando mapeos: {e}")
                    self._backup_usbports_file()
            ps_command = '''
            Get-Disk | ForEach-Object {
                $disk = $_
                $diskNum = $disk.Number
                $diskInfo = [PSCustomObject]@{
                    Number = $diskNum
                    FriendlyName = $disk.FriendlyName
                    Model = $disk.Model
                    MediaType = $disk.MediaType
                    BusType = $disk.BusType
                    SerialNumber = $disk.SerialNumber
                    Location = $disk.Location
                    Path = $disk.Path
                    PhysicalSectorSize = $disk.PhysicalSectorSize
                    LogicalSectorSize = $disk.LogicalSectorSize
                    Size = $disk.Size
                    Partitions = @()
                }
                Get-Partition -DiskNumber $diskNum | ForEach-Object {
                    $partition = $_
                    $partInfo = [PSCustomObject]@{
                        DriveLetter = if($partition.DriveLetter) { $partition.DriveLetter } else { $null }
                        Size = $partition.Size
                        Type = $partition.Type
                    }
                    $diskInfo.Partitions += $partInfo
                }
                $diskInfo
            } | ConvertTo-Json -Depth 5
            '''
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            process = subprocess.Popen(
                ['powershell', '-Command', ps_command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=startupinfo
            )
            stdout, stderr = process.communicate()
            try:
                disks_data = json.loads(stdout.decode('utf-8', errors='ignore'))
                if not isinstance(disks_data, list):
                    disks_data = [disks_data]
                ports_info = {}
                for disk in disks_data:
                    disk_num = disk.get('Number')
                    if disk_num is None:
                        continue
                    port_id = f"PHYS_{disk_num}"
                    if disk.get('BusType') == 'USB':
                        port_id = f"USB_{disk_num}"
                    elif disk.get('BusType') == 'NVMe':
                        port_id = f"NVME_{disk_num}"
                    if disk.get('Model'):
                        model_safe = ''.join(c if c.isalnum() else '_' for c in disk.get('Model')[:15])
                        port_id = f"{port_id}_{model_safe}"
                    friendly_name = disk.get('FriendlyName') or disk.get('Model') or f"Disco #{disk_num}"
                    drive_letters = []
                    for partition in disk.get('Partitions', []):
                        if partition.get('DriveLetter'):
                            drive_letters.append(partition.get('DriveLetter'))
                    if drive_letters:
                        friendly_name = f"{friendly_name} ({', '.join(drive_letters)}:)"
                    ports_info[port_id] = {
                        'device': friendly_name,
                        'deviceid': f"PhysicalDisk{disk_num}",
                        'diskNumber': disk_num,
                        'busType': disk.get('BusType', 'Unknown'),
                        'mediaType': disk.get('MediaType', 'Unknown'),
                        'model': disk.get('Model', 'Unknown'),
                        'serialNumber': disk.get('SerialNumber', 'Unknown'),
                        'size': disk.get('Size', 0),
                        'driveLetters': drive_letters
                    }
                    if port_id not in saved_mappings:
                        saved_mappings[port_id] = friendly_name
                    for drive_letter in drive_letters:
                        if drive_letter:
                            drive_letter_full = f"{drive_letter}:"
                            try:
                                guardar_disco_puerto_mapping(drive_letter_full, port_id)
                            except Exception as e:
                                print(f"Error guardando mapping disco-puerto: {e}")
                try:
                    with open('USBPorts.txt', 'w', encoding='utf-8') as f:
                        for key, value in saved_mappings.items():
                            f.write(f"{key}:{value}\n")
                except Exception as e:
                    print(f"Error guardando mapeos finales: {e}")
                
                self.finished.emit(ports_info)
            except json.JSONDecodeError as e:
                print(f"Error decodificando JSON: {e}")
                print(f"Salida de PowerShell: {stdout.decode('utf-8', errors='ignore')}")
                self.finished.emit({})
        except Exception as e:
            print(f"Error escaneando discos: {e}")
            import traceback
            traceback.print_exc()
            self.finished.emit({})

class SpeedUpdateWorker(QThread):
    update_signal = pyqtSignal(str, dict)
    def __init__(self, parent=None):
        super().__init__(parent)
        self.running = True
        self._paused = False
        self.last_update = {} 
        self.update_interval = 0.2  
    def run(self):
        while self.running:
            if not self._paused:
                try:
                    current_time = time.time()
                    for drive_letter in self.parent().copy_bytes_copied.keys():
                        if not self.running:
                            break
                        if drive_letter in self.last_update:
                            if current_time - self.last_update[drive_letter] < self.update_interval:
                                continue
                        update_data = self.calculate_speed_data(drive_letter, current_time)
                        if update_data:
                            self.last_update[drive_letter] = current_time
                            self.update_signal.emit(drive_letter, update_data)
                except Exception as e:
                    print(f"Error en SpeedUpdateWorker: {e}")
            time.sleep(self.update_interval)
            
    def calculate_speed_data(self, drive_letter, current_time):
        try:
            main_window = self.parent()
            if drive_letter not in main_window.copy_start_time:
                return None
            elapsed_time = current_time - main_window.copy_start_time[drive_letter]
            if elapsed_time <= 0:
                return None
            bytes_copied = main_window.copy_bytes_copied.get(drive_letter, 0)
            total_size = main_window.total_sizes.get(drive_letter, 0)
            speed = bytes_copied / elapsed_time / (1024 * 1024)
            progress = int((bytes_copied / total_size * 100) if total_size > 0 else 0)
            return {
                'speed': speed,
                'progress': progress,
                'bytes_copied': bytes_copied,
                'total_size': total_size
            }
        except Exception as e:
            print(f"Error calculando datos de velocidad: {e}")
            return None

    def update_speed_ui(self, drive_letter, data):
        """Actualiza la UI con los datos de velocidad calculados"""
        try:
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if drive_letter in item.text():
                    widget = self.list_widget.itemWidget(item)
                    if widget:
                        speed_label = widget.findChildren(QLabel)[2]
                        progress_bar = widget.progress_bar
                        if data['progress'] != progress_bar.value():
                            progress_bar.setValue(data['progress'])
                        speed_str = f"{data['speed']:.2f} MB/s"
                        if data['total_size'] > 0:
                            bytes_remaining = data['total_size'] - data['bytes_copied']
                            time_remaining = bytes_remaining / (data['speed'] * 1024 * 1024) if data['speed'] > 0 else 0
                            hours, remainder = divmod(time_remaining, 3600)
                            minutes, seconds = divmod(remainder, 60)
                            time_str = f"{int(hours):02}:{int(minutes):02}:{int(seconds):02}"
                            speed_label.setText(f"{speed_str} ({time_str}) {data['progress']}%")
                        else:
                            speed_label.setText(speed_str)
                    break
        except Exception as e:
            print(f"Error actualizando UI de velocidad: {e}")

class SpeedDisplayWorker(QThread):
    update_ui = pyqtSignal(str, dict)
    def __init__(self, parent=None):
        super().__init__(parent)
        self.running = True
    def run(self):
        while self.running:
            try:
                current_time = time.time()
                for drive_letter, last_update in self.parent()._last_speed_update.items():
                    if current_time - last_update < 1.0:
                        continue
                    data = self.calculate_speed_data(drive_letter)
                    if data:
                        self.update_ui.emit(drive_letter, data)
            except Exception as e:
                print(f"Error en SpeedDisplayWorker: {e}")
            time.sleep(0.1)
    def calculate_speed_data(self, drive_letter):
        main_window = self.parent()
        if not main_window.is_copying(drive_letter):
            return None
        return {
            'bytes_copied': main_window.copy_bytes_copied.get(drive_letter, 0),
            'total_size': main_window.total_sizes.get(drive_letter, 0),
            'final_space': main_window.final_space_text.get(drive_letter, ''),
            'is_paused': drive_letter in main_window.pause_times
        }

from NOTIFICACION import NotificationBadge, update_badge_positions
from CREAR_TXT import TxtFileCreator
class UpdateWorker(QThread):
    update_ready = pyqtSignal(str, dict)  # drive_letter, datos
    def __init__(self, drive_letter, progress, parent=None):
        super().__init__(parent)
        self.drive_letter = drive_letter
        self.progress = progress
        self.parent = parent
        self._running = True
    def stop(self):
        """Detiene el worker de manera segura"""
        self._running = False
        self.wait()  # Espera a que termine el thread
    def run(self):
        try:
            if not self._running:
                return
            data = self.collect_drive_data()
            if self._running:  # Verificar nuevamente antes de emitir
                self.update_ready.emit(self.drive_letter, data)
        except Exception as e:
            print(f"Error en UpdateWorker para {self.drive_letter}: {e}")
    def collect_drive_data(self):
        """Recopila los datos del disco de manera segura"""
        data = {
            'exists': False,
            'volume_info': '',
            'new_files_size': 0,
            'progress': self.progress,
            'free_space': 0,
            'total_size': 0,
            'bytes_copied': 0
        }
        if not os.path.exists(f"{self.drive_letter}\\") or not self._running:
            return data
        try:
            data['exists'] = True
            data['volume_info'] = win32api.GetVolumeInformation(f"{self.drive_letter}\\")[0]
            if not self._running:
                return data
            if hasattr(self.parent, 'is_copying') and self.parent.is_copying(self.drive_letter):
                try:
                    if (hasattr(self.parent, 'queues') and 
                        self.drive_letter in self.parent.queues and 
                        hasattr(self.parent, 'files_in_queue')):
                        new_files_size = 0
                        pending_files = [f for f in self.parent.files_in_queue 
                                      if f[1].startswith(self.drive_letter)]
                        for src, _ in pending_files:
                            if not self._running:
                                return data
                            if os.path.isfile(src):
                                try:
                                    new_files_size += os.path.getsize(src)
                                except OSError:
                                    continue
                            elif os.path.isdir(src):
                                try:
                                    for root, _, files in os.walk(src):
                                        for file in files:
                                            if not self._running:
                                                return data
                                            try:
                                                new_files_size += os.path.getsize(os.path.join(root, file))
                                            except OSError:
                                                continue
                                except Exception:
                                    continue
                        data['new_files_size'] = new_files_size
                        if hasattr(self.parent, 'total_sizes'):
                            data['total_size'] = self.parent.total_sizes.get(self.drive_letter, 0)
                        if hasattr(self.parent, 'copy_bytes_copied'):
                            data['bytes_copied'] = self.parent.copy_bytes_copied.get(self.drive_letter, 0)
                except Exception as e:
                    print(f"Error calculando tamaños: {e}")
            try:
                free_space, _ = get_drive_space(self.drive_letter)
                data['free_space'] = free_space
            except Exception as e:
                print(f"Error obteniendo espacio libre: {e}")
        except Exception as e:
            print(f"Error recopilando datos: {e}")
        return data

class DriveSpaceWorker(QThread):
    finished = pyqtSignal(dict)
    def __init__(self, drive_letter, parent=None):
        super().__init__(parent)
        self.drive_letter = drive_letter
    def run(self):
        try:
            total, used, free_space = shutil.disk_usage(self.drive_letter)
            self.finished.emit({
                'free_space': free_space,
                'total': total,
                'used': used
            })
        except Exception as e:
            print(f"Error calculando espacio: {e}")

class FileOperationsWorker(QThread):
    progress = pyqtSignal(int)
    finished = pyqtSignal()
    def __init__(self, source_files, dest_path, parent=None):
        super().__init__(parent)
        self.source_files = source_files
        self.dest_path = dest_path
        self.running = True
    def run(self):
        total_files = len(self.source_files)
        processed = 0
        for source, size, dest in self.source_files:
            if not self.running:
                break
            try:
                os.makedirs(os.path.dirname(dest), exist_ok=True)
                self.progress.emit(int((processed / total_files) * 100))
                processed += 1
            except Exception as e:
                print(f"Error procesando archivo {source}: {e}")
        self.finished.emit()
    def stop(self):
        self.running = False

class SingleInstance: # ESTO CUANDO SE EJECUTA LA APP BORRA DEL TEMPORAL RASTROS
    def __init__(self):
        self.mutexname = "ZETACOPY_MUTEX_INSTANCE_"
        self.instance_id = str(os.getpid())  # Usar PID como identificador único
        self.mutexname += self.instance_id
        self.mutex = None
        try:
            self.mutex = win32event.CreateMutex(None, True, self.mutexname)
            self.lasterror = win32api.GetLastError()
        except Exception as e:
            print(f"Error en SingleInstance: {e}")
            self.lasterror = 1
    def already_running(self):
        # Ya no bloqueamos otras instancias, solo verificamos
        return False
    def __del__(self):
        if self.mutex:
            try:
                win32api.CloseHandle(self.mutex)
            except Exception as e:
                print(f"Error closing mutex: {e}")

def exception_handler(exctype, value, tb): # ESTO ES PARA CUANDO OCURRA UN ERROR SE CREE EL ERROR_ZETACOPY.TXT
    """Manejador global de excepciones para ZETACOPY"""
    try:
        if getattr(sys, 'frozen', False):
            app_path = os.path.dirname(sys.executable)
        else:
            app_path = os.path.dirname(os.path.abspath(__file__))
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        error_file = os.path.join(app_path, 'error_zetacopy.txt')
        with open(error_file, 'w', encoding='utf-8') as f:
            f.write(f"ZETACOPY - Registro de Error\n")
            f.write(f"Fecha y hora: {timestamp}\n")
            f.write(f"Tipo de error: {exctype.__name__}\n")
            f.write(f"Mensaje: {str(value)}\n")
            f.write("\nTraceback completo:\n")
            traceback.print_tb(tb, file=f)
        print(f"Se ha producido un error. Detalles guardados en: {error_file}")
    except Exception as e:
        print(f"Error al guardar el registro de error: {str(e)}")
    sys.exit(1)

if __name__ == '__main__':
    sys.excepthook = exception_handler
    app = QApplication(sys.argv)
    single_instance = SingleInstance()
    try:
        from PRESENTACION import mostrar_presentacion
        splash = mostrar_presentacion()
        main_window = None
        def init_app():
            global main_window
            main_window = MainWindow()
            def show_main_window():
                main_window.show()
                splash.finish(main_window)
            QTimer.singleShot(2000, show_main_window)
            return main_window
        QTimer.singleShot(10, init_app)
        result = app.exec()
        sys.exit(result)
    except Exception as e:
        print(f"Error al iniciar la aplicación: {e}")
        sys.exit(1)

def changeEvent(self, event):
    if event.type() == QtCore.QEvent.Type.WindowStateChange:
        if event.oldState() == Qt.WindowState.WindowNoState and \
           self.windowState() == Qt.WindowState.WindowMinimized:
            event.accept()
            return
    super().changeEvent(event)

def event(self, event):
    if event.type() == QtCore.QEvent.Type.WindowActivate and \
       self.windowState() == Qt.WindowState.WindowMinimized:
        return True
    return super().event(event)






















