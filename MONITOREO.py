import base64
import ctypes
import hashlib
import json
import os
import subprocess
import threading
import time
from ctypes import windll, create_unicode_buffer, wintypes, byref, Structure, c_byte as BYTE
from datetime import datetime

import pythoncom
import win32api
import win32file
import wmi
from PyQt6.QtCore import QThread, pyqtSignal

# Importar las nuevas funciones de MAPEO_PUERTO
from MAPEO_PUERTO import (
    get_port_id_fallback,
    get_usb_port_info as get_usb_port_info_new,
    get_port_name
)

class STORAGE_PROPERTY_QUERY(Structure):
    _fields_ = [
        ('PropertyId', wintypes.DWORD),
        ('QueryType', wintypes.DWORD),
        ('AdditionalParameters', BYTE * 1)
    ]

def calculate_hash(data):
    return hashlib.sha256(data.encode()).hexdigest()

class Worker(QThread):
    update_volumes = pyqtSignal(list)  # type: pyqtSignal
    disk_status_changed = pyqtSignal(str, bool)  # type: pyqtSignal
    update_signal = pyqtSignal(dict)  # type: pyqtSignal
    finished_signal = pyqtSignal()  # type: pyqtSignal
    def __init__(self):
        super().__init__()
        self._running = True
        self._properties_cache = {}
        self._cache_timeout = 5  # segundos
        self._last_cache_cleanup = time.time()
    def run(self):
        old_volumes = self.get_connected_volumes()
        self.update_volumes.emit(old_volumes)  # Primera carga completa
        while self._running:
            new_volumes = self.get_connected_volumes()
            if new_volumes != old_volumes:
                old_letters = {vol[2] for vol in old_volumes}
                new_letters = {vol[2] for vol in new_volumes}
                for old_volume in old_volumes:
                    drive_letter = old_volume[2]
                    if drive_letter not in new_letters:
                        self.disk_status_changed.emit(drive_letter, False)
                        if drive_letter in _DISK_MODELS_CACHE:
                            del _DISK_MODELS_CACHE[drive_letter]
                            print(f"Disco {drive_letter} desconectado, eliminado de caché")
                        # Eliminar también de la caché de puertos USB
                        if drive_letter in _USB_PORT_CACHE:
                            del _USB_PORT_CACHE[drive_letter]
                            print(f"Puerto USB para {drive_letter} eliminado de caché")
                for new_volume in new_volumes:
                    drive_letter = new_volume[2]
                    if drive_letter not in old_letters:
                        self.disk_status_changed.emit(drive_letter, True)
                        # Precargar puerto USB antes de emitir la señal
                        self._preload_usb_port_info(drive_letter)
                        # Luego precargar el modelo y emitir la señal
                        threading.Thread(
                            target=self._preload_disk_model,
                            args=(drive_letter,),
                            daemon=True
                        ).start()
                        self.disk_status_changed.emit(drive_letter, True)
                
                # Actualizar la lista de volúmenes
                self.update_volumes.emit(new_volumes)
                old_volumes = new_volumes
            self.sleep(2)

    def get_connected_volumes(self):
        volumes = []
        buflen = ctypes.windll.kernel32.GetLogicalDriveStringsW(0, None)
        buf = create_unicode_buffer(buflen)
        ctypes.windll.kernel32.GetLogicalDriveStringsW(buflen, buf)
        for drive in buf[:buflen].split('\x00'):
            if drive:
                volume_name_buf = create_unicode_buffer(1024)
                file_system_name_buf = create_unicode_buffer(1024)
                serial_number = ctypes.c_uint32()
                max_component_length = ctypes.c_uint32()
                file_system_flags = ctypes.c_uint32()
                ctypes.windll.kernel32.GetVolumeInformationW(
                    ctypes.c_wchar_p(drive),
                    volume_name_buf,
                    ctypes.sizeof(volume_name_buf),
                    ctypes.byref(serial_number),
                    ctypes.byref(max_component_length),
                    ctypes.byref(file_system_flags),
                    file_system_name_buf,
                    ctypes.sizeof(file_system_name_buf)
                )
                volume_name = volume_name_buf.value if volume_name_buf.value else "Sin nombre"
                drive_type = self.get_drive_type(drive)
                volumes.append((volume_name, drive_type, drive.strip('\\')))
        return volumes

    def get_drive_type(self, drive):
        drive_type = ctypes.windll.kernel32.GetDriveTypeW(drive)
        if drive_type == 2:  # DRIVE_REMOVABLE
            return "Unidad extraíble (USB)"
        elif drive_type == 3:  # DRIVE_FIXED
            return self.get_disk_type(drive)
        elif drive_type == 4:  # DRIVE_REMOTE
            return "Unidad de red"  # Cambiado para que use el icono de red
        elif drive_type == 5:
            return "Unidad de CD/DVD"
        else:
            return "Desconocido"

    def get_disk_type(self, drive):
        drive_letter = drive.strip('\\')
        handle = windll.kernel32.CreateFileW(
            f"\\\\.\\{drive_letter}",
            0,
            0,
            None,
            3,
            0,
            None
        )
        if handle == -1:
            return "Disco duro (Desconocido)"
        class STORAGE_DEVICE_DESCRIPTOR(ctypes.Structure):
            _fields_ = [
                ("Version", wintypes.DWORD),
                ("Size", wintypes.DWORD),
                ("DeviceType", wintypes.BYTE),
                ("DeviceTypeModifier", wintypes.BYTE),
                ("RemovableMedia", wintypes.BOOLEAN),
                ("CommandQueueing", wintypes.BOOLEAN),
                ("VendorIdOffset", wintypes.DWORD),
                ("ProductIdOffset", wintypes.DWORD),
                ("ProductRevisionOffset", wintypes.DWORD),
                ("SerialNumberOffset", wintypes.DWORD),
                ("BusType", wintypes.BYTE),
                ("RawPropertiesLength", wintypes.DWORD),
                ("RawDeviceProperties", wintypes.BYTE * 1)
            ]
        IOCTL_STORAGE_QUERY_PROPERTY = 0x2D1400
        property_query = (wintypes.DWORD * 3)(0, 0, 0)
        descriptor = STORAGE_DEVICE_DESCRIPTOR()
        descriptor_size = wintypes.DWORD(ctypes.sizeof(descriptor))
        result = windll.kernel32.DeviceIoControl(
            handle,
            IOCTL_STORAGE_QUERY_PROPERTY,
            byref(property_query),
            ctypes.sizeof(property_query),
            byref(descriptor),
            descriptor_size,
            byref(descriptor_size),
            None
        )
        windll.kernel32.CloseHandle(handle)
        if result:
            if descriptor.BusType == 0x0B:
                return "Disco duro interno"
            else:
                return "Disco duro externo"
        else:
            return "Disco duro (Desconocido)"

    def get_disk_properties(self, drive_letter, force_refresh=False):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.get_disk_properties(drive_letter, force_refresh)

    def get_basic_properties(self, drive_letter):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.get_basic_properties(drive_letter)

    def add_source_disk_to_properties(self, properties_html, source_disk_info):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.add_source_disk_to_properties(properties_html, source_disk_info)

    def get_wmi_disk_info(self, drive_letter):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.get_wmi_disk_info(drive_letter)

    def format_size(self, size):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.format_size(size)

    def get_serial_number_model_and_uid(self, drive_letter):
        """Obtiene el número de serie, modelo e ID del disco usando PowerShell"""
        try:
            drive_letter_clean = drive_letter.replace(":", "")
            cmd = f'powershell -NoProfile -Command "(Get-Partition -DriveLetter {drive_letter_clean} -ErrorAction SilentlyContinue).DiskNumber"'
            disk_num_process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=2
            )
            if disk_num_process.returncode == 0 and disk_num_process.stdout.strip():
                disk_number = disk_num_process.stdout.strip()
                cmd = f'powershell -NoProfile -Command "Get-Disk -Number {disk_number} | Select-Object SerialNumber, Model, Path | ConvertTo-Json"'
                disk_info_process = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    shell=True,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    timeout=3
                )
                if disk_info_process.returncode == 0 and disk_info_process.stdout.strip():
                    try:
                        disk_info = json.loads(disk_info_process.stdout.strip())
                        serial = disk_info.get("SerialNumber", "").strip()
                        model = disk_info.get("Model", "").strip()
                        device_id = disk_info.get("Path", "").strip()
                        if not serial:
                            serial = "Número de serie no encontrado"
                        if not model:
                            model = "Modelo no encontrado"
                        if not device_id:
                            device_id = "UID no encontrado"
                        return serial, model, device_id
                    except json.JSONDecodeError:
                        pass
        except Exception as e:
            print(f"Error obteniendo información del disco {drive_letter}: {e}")
        return "Número de serie no encontrado", "Modelo no encontrado", "UID no encontrado"

    def get_volume_name(self, drive_letter):
        """Obtiene el nombre del volumen usando PowerShell"""
        try:
            cmd = f'powershell -NoProfile -Command "Get-Volume -DriveLetter {drive_letter.replace(":", "")} | Select-Object -ExpandProperty FileSystemLabel"'
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=2
            )
            if process.returncode == 0 and process.stdout.strip():
                return process.stdout.strip()
            cmd = f'powershell -NoProfile -Command "(Get-WmiObject -Class Win32_Volume -Filter \"DriveLetter = \'{drive_letter}\'\" | Select-Object -ExpandProperty Label)"'
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=2
            )
            if process.returncode == 0 and process.stdout.strip():
                return process.stdout.strip()
        except Exception as e:
            print(f"Error obteniendo nombre del volumen: {e}")
        return "Nombre del volumen no encontrado"

    def stop(self):
        self._running = False
    def get_usb_port_info(self, drive_letter):
        """
        Obtiene la información del puerto USB para una letra de unidad específica.
        Ahora usa directamente el método fallback que es más rápido y confiable.
        """
        global _USB_PORT_CACHE
        drive_letter = drive_letter.strip().upper()

        # Verificar primero en la caché usando el semáforo
        with _USB_PORT_CACHE_LOCK:
            if drive_letter in _USB_PORT_CACHE:
                port_id, port_name = _USB_PORT_CACHE[drive_letter]
                # Solo devolver de caché si es un ID válido (no genérico)
                if port_id and not port_id.startswith("Unknown_") and not port_id.startswith("FAIL_"):
                    return port_id, port_name

        try:
            # Usar directamente el método fallback que es más rápido y confiable
            start_time = time.time()
            port_id = get_port_id_fallback(drive_letter)
            
            if port_id:
                # Obtener el nombre del puerto
                port_name = get_port_name(port_id)
                # Si no hay nombre asignado, usar "-" como valor por defecto
                if not port_name:
                    port_name = "-"
                    
                # Actualizar la caché con el semáforo
                with _USB_PORT_CACHE_LOCK:
                    _USB_PORT_CACHE[drive_letter] = (port_id, port_name)
                
                elapsed = time.time() - start_time
                print(f"Puerto USB para {drive_letter} obtenido en {elapsed:.2f}s: {port_id} ({port_name})")
                return port_id, port_name
            
        except Exception as e:
            print(f"Error obteniendo información del puerto USB para {drive_letter}: {e}")
            import traceback
            traceback.print_exc()

        # Si todo falla, devolver valores por defecto
        return "", ""

    def _preload_disk_model(self, drive_letter):
        """Precarga el modelo de un disco recién conectado en la caché global"""
        try:
            if not os.path.exists(f"{drive_letter}\\"):
                return
            drive_type = win32file.GetDriveType(f"{drive_letter}\\")
            if drive_type == win32file.DRIVE_REMOVABLE:
                base_model = "Dispositivo extraíble"
            elif drive_type == win32file.DRIVE_FIXED:
                base_model = "Disco duro"
            elif drive_type == win32file.DRIVE_REMOTE:
                base_model = "Unidad de red"
            elif drive_type == win32file.DRIVE_CDROM:
                base_model = "Unidad óptica"
            else:
                base_model = "Dispositivo de almacenamiento"
            _DISK_MODELS_CACHE[drive_letter] = base_model

            # Cargar información del puerto USB para el nuevo disco
            threading.Thread(
                target=self._preload_usb_port_info,
                args=(drive_letter,),
                daemon=True
            ).start()

            if _mejorar_modelo_individual(drive_letter):
                return
            try:
                import wmi
                w = wmi.WMI()
                for disk in w.Win32_LogicalDisk(DeviceID=drive_letter):
                    for partition in disk.associators("Win32_LogicalDiskToPartition"):
                        for physical_disk in partition.associators("Win32_DiskDriveToDiskPartition"):
                            if physical_disk.Model:
                                model = physical_disk.Model.strip()
                                _DISK_MODELS_CACHE[drive_letter] = model
                                print(f"Modelo básico para {drive_letter} recién conectado: {model}")
                                return
            except Exception as e:
                print(f"Error en respaldo WMI para {drive_letter}: {e}")
        except Exception as e:
            print(f"Error precargando modelo para {drive_letter}: {e}")

    def _preload_usb_port_info(self, drive_letter):
        """Precarga la información del puerto USB para un disco recién conectado"""
        try:
            # Verificar con el semáforo si ya está en caché
            with _USB_PORT_CACHE_LOCK:
                already_cached = drive_letter in _USB_PORT_CACHE and _USB_PORT_CACHE[drive_letter][0]
            
            if already_cached:
                port_id, port_name = _USB_PORT_CACHE[drive_letter]
                print(f"Puerto USB para {drive_letter} recién conectado (ya en caché): {port_id} ({port_name})")
                return
                
            # Si no está en caché, intentar obtener de forma directa
            print(f"Precargando puerto para {drive_letter} recién conectado (modo directo)...")
            start_time = time.time()
            
            # Usar directamente el método fallback que es más rápido y confiable
            port_id = get_port_id_fallback(drive_letter)
            
            if port_id:
                # Obtener el nombre del puerto si existe
                port_name = get_port_name(port_id)
                # Si no hay nombre asignado, usar "-" como valor por defecto
                if not port_name:
                    port_name = "-"
                
                # Actualizar la caché con el semáforo
                with _USB_PORT_CACHE_LOCK:
                    _USB_PORT_CACHE[drive_letter] = (port_id, port_name)
                
                elapsed = time.time() - start_time
                print(f"Puerto USB para {drive_letter} recién conectado, obtenido en {elapsed:.2f}s: {port_id} ({port_name})")
            else:
                # Si todo falla, intentar con el método normal
                port_id, port_name = get_usb_port_info(drive_letter)
                if port_id and not port_name:
                    port_name = "-"
                    with _USB_PORT_CACHE_LOCK:
                        _USB_PORT_CACHE[drive_letter] = (port_id, port_name)
                if port_id:
                    print(f"Puerto USB para {drive_letter} recién conectado (método fallback): {port_id} ({port_name})")
        except Exception as e:
            print(f"Error precargando información de puerto USB para {drive_letter}: {e}")

def get_motherboard_info():
    """Obtiene información de la placa base usando PowerShell"""
    try:
        cmd = 'powershell -NoProfile -Command "Get-CimInstance -ClassName Win32_BaseBoard | Select-Object SerialNumber, Product, Manufacturer | ConvertTo-Json"'
        try:
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=10
            )
            if process.returncode == 0 and process.stdout.strip():
                try:
                    board_info = json.loads(process.stdout.strip())
                    serial_number = board_info.get("SerialNumber", "").strip() or "Desconocido"
                    model = board_info.get("Product", "").strip() or "Desconocido"
                    manufacturer = board_info.get("Manufacturer", "").strip() or "Desconocido"
                    return serial_number, model, manufacturer
                except json.JSONDecodeError as e:
                    print(f"Error al decodificar JSON de placa base: {e}")
            else:
                print(f"Error al ejecutar comando de placa base: {process.stderr}")
        except subprocess.TimeoutExpired:
            print("Tiempo de espera agotado al obtener información de placa base")
        except Exception as e:
            print(f"Error ejecutando comando para placa base: {e}")
        try:
            print("Intentando método alternativo con WMI para placa base...")
            wmi_obj = wmi.WMI()
            for board in wmi_obj.Win32_BaseBoard():
                serial = board.SerialNumber.strip() if board.SerialNumber else "Desconocido"
                model = board.Product.strip() if board.Product else "Desconocido"
                manufacturer = board.Manufacturer.strip() if board.Manufacturer else "Desconocido"
                print("Método alternativo exitoso para placa base")
                return serial, model, manufacturer
        except Exception as e:
            print(f"Error en método alternativo para placa base: {e}")
    except Exception as e:
        print(f"Error general obteniendo información de placa base: {e}")
    return "Desconocido", "Desconocido", "Desconocido"

def get_system_uuid():
    """Obtiene el UUID del sistema usando PowerShell"""
    try:
        cmd = 'powershell -NoProfile -Command "Get-CimInstance -ClassName Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID"'
        try:
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=10  # Aumentar timeout a 10 segundos
            )
            if process.returncode == 0 and process.stdout.strip():
                return process.stdout.strip()
            else:
                print(f"Error al obtener UUID: {process.stderr}")
        except subprocess.TimeoutExpired:
            print("Tiempo de espera agotado al obtener UUID del sistema")
        except Exception as e:
            print(f"Error ejecutando comando para UUID: {e}")
        try:
            print("Intentando método alternativo con WMI para UUID...")
            wmi_obj = wmi.WMI()
            for system in wmi_obj.Win32_ComputerSystemProduct():
                if system.UUID:
                    print("Método alternativo exitoso para UUID")
                    return system.UUID.strip()
        except Exception as e:
            print(f"Error en método alternativo para UUID: {e}")
    except Exception as e:
        print(f"Error general obteniendo UUID del sistema: {e}")
    return None

def get_disk_serial_number(drive_letter):
    """Obtiene el número de serie del disco usando PowerShell"""
    try:
        drive_letter_clean = drive_letter.replace(":", "")
        cmd = f'powershell -NoProfile -Command "(Get-Partition -DriveLetter {drive_letter_clean} -ErrorAction SilentlyContinue).DiskNumber"'
        try:
            disk_num_process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=10  # Aumentar timeout a 10 segundos
            )
            if disk_num_process.returncode == 0 and disk_num_process.stdout.strip():
                disk_number = disk_num_process.stdout.strip()
                cmd = f'powershell -NoProfile -Command "Get-Disk -Number {disk_number} | Select-Object -ExpandProperty SerialNumber"'
                try:
                    serial_process = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        shell=True,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        timeout=10  # Aumentar timeout a 10 segundos
                    )
                    if serial_process.returncode == 0 and serial_process.stdout.strip():
                        return serial_process.stdout.strip()
                    else:
                        print(f"No se pudo obtener el número de serie, código de retorno: {serial_process.returncode}, error: {serial_process.stderr}")
                except subprocess.TimeoutExpired:
                    print(f"Tiempo de espera agotado al obtener número de serie para el disco {disk_number}")
                except Exception as e:
                    print(f"Error obteniendo número de serie del disco {disk_number}: {e}")
            else:
                print(f"No se pudo obtener el número de disco para {drive_letter}, código de retorno: {disk_num_process.returncode}, error: {disk_num_process.stderr}")
        except subprocess.TimeoutExpired:
            print(f"Tiempo de espera agotado al obtener número de disco para {drive_letter}")
        except Exception as e:
            print(f"Error obteniendo número de disco: {e}")
    except Exception as e:
        print(f"Error general obteniendo número de serie: {e}")
    try:
        print(f"Intentando método alternativo con WMI para {drive_letter}...")
        drive_letter_clean = drive_letter.replace(":", "")
        wmi_obj = wmi.WMI()
        for physical_disk in wmi_obj.Win32_DiskDrive():
            for partition in physical_disk.associators("Win32_DiskDriveToDiskPartition"):
                for logical_disk in partition.associators("Win32_LogicalDiskToPartition"):
                    if logical_disk.DeviceID.lower() == f"{drive_letter_clean.lower()}:":
                        serial = physical_disk.SerialNumber
                        if serial:
                            print(f"Método alternativo exitoso para {drive_letter}")
                            return serial.strip()
    except Exception as e:
        print(f"Error en método alternativo: {e}")
    return "Serial number not found"

def generate_unique_code():
    try:
        serial_number, model, manufacturer = get_motherboard_info()
        disk_serial = get_disk_serial_number("C:")
        current_date = datetime.now().strftime("%Y-%m-%d")
        combined_info = f"{model}|{disk_serial}|{current_date}"
        encoded = base64.urlsafe_b64encode(combined_info.encode('utf-8')).decode('utf-8')
        formatted = '-'.join([encoded[i:i+4] for i in range(0, len(encoded), 4)])
        return formatted
    except Exception as e:
        print(f"Error generando código: {str(e)}")
        return None

def get_original_info(formatted_code):
    try:
        code = formatted_code.replace('-', '')
        decoded_bytes = base64.urlsafe_b64decode(code)
        decoded_text = decoded_bytes.decode('utf-8')
        parts = decoded_text.split('|')
        if len(parts) < 3:  # Al menos modelo, fabricante y fecha
            raise ValueError("Formato de información inválido")
        return decoded_text
    except Exception as e:
        print(f"Error desencriptando código: {e}")
        raise ValueError("Código inválido")

def get_disk_model_direct(drive_letter):
    """
    Obtiene el modelo del disco directamente desde Windows sin consultas lentas.
    Esta función es ultrarrápida y se utiliza para mostrar las propiedades del disco.
    """
    try:
        # Ensure drive letter is properly formatted
        if not drive_letter.endswith(":"):
            drive_letter = f"{drive_letter}:"
            
        # Check cache first - this is the fast path
        if drive_letter in _DISK_MODELS_CACHE and _DISK_MODELS_CACHE[drive_letter]:
            return _DISK_MODELS_CACHE[drive_letter]
            
        # If drive doesn't exist, return quickly
        if not os.path.exists(f"{drive_letter}\\"):
            _DISK_MODELS_CACHE[drive_letter] = "Unidad no disponible"
            return _DISK_MODELS_CACHE[drive_letter]
            
        # Get basic info (fast operations)
        drive_type = win32file.GetDriveType(f"{drive_letter}\\")
        try:
            volume_info = win32api.GetVolumeInformation(f"{drive_letter}\\")
            fs_type = volume_info[4] if volume_info and len(volume_info) > 4 else ""
        except:
            fs_type = ""
            
        # Determine base type
        if drive_type == win32file.DRIVE_UNKNOWN:
            base_type = "Unidad desconocida"
        elif drive_type == win32file.DRIVE_NO_ROOT_DIR:
            base_type = "Unidad no disponible"
        elif drive_type == win32file.DRIVE_REMOVABLE:
            base_type = "Dispositivo extraíble"
        elif drive_type == win32file.DRIVE_FIXED:
            if drive_letter.upper() == "C:":
                base_type = "Disco del sistema"
            else:
                base_type = "Disco interno"
        elif drive_type == win32file.DRIVE_REMOTE:
            base_type = "Unidad de red"
        elif drive_type == win32file.DRIVE_CDROM:
            base_type = "Unidad óptica"
        elif drive_type == win32file.DRIVE_RAMDISK:
            base_type = "Disco RAM"
        else:
            base_type = "Dispositivo de almacenamiento"
            
        # Add filesystem info if available
        if fs_type:
            model = f"{base_type} ({fs_type})"
        else:
            model = base_type
            
        # Store in cache
        _DISK_MODELS_CACHE[drive_letter] = model
        
        # Start background improvement if not already in progress
        try:
            if drive_letter not in _DISCOS_EN_MEJORA:
                _DISCOS_EN_MEJORA.append(drive_letter)
                threading.Thread(
                    target=_mejorar_modelo_individual,
                    args=(drive_letter,),
                    daemon=True
                ).start()
        except:
            pass
            
        return model
    except Exception as e:
        print(f"Error obteniendo modelo rápido: {e}")
        return "Dispositivo de almacenamiento"

_DISCOS_EN_MEJORA = []

def _mejorar_modelo_individual(drive_letter):
    """Mejora el modelo de un disco específico en segundo plano"""
    try:
        if not os.path.exists(f"{drive_letter}\\"):
            return
        try:
            cmd = f'''powershell -NoProfile -Command "
            $disk = Get-Partition -DriveLetter {drive_letter[0]} -ErrorAction SilentlyContinue | Get-Disk -ErrorAction SilentlyContinue
            if ($disk) {{
                $diskModel = $disk.Model
                $busType = $disk.BusType
                $mediaType = $disk.MediaType
                $isBoot = $disk.IsBoot
                $isSystem = $disk.IsSystem
                $tipo = ''
                if ($busType -eq 'USB') {{ 
                    $tipo = 'USB' 
                }}
                elseif ($busType -eq 'NVMe') {{ 
                    $tipo = 'NVMe' 
                }}
                elseif ($busType -eq 'SATA') {{ 
                    $tipo = 'SATA' 
                }}
                elseif ($busType -eq 'PCIE') {{ 
                    $tipo = 'PCIe' 
                }}
                if ($tipo -eq '') {{
                    if ($mediaType -eq 'SSD') {{ 
                        $tipo = 'SSD' 
                    }}
                    elseif ($mediaType -eq 'HDD') {{ 
                        $tipo = 'HDD' 
                    }}
                    elseif ($diskModel -match 'SSD|Solid') {{ 
                        $tipo = 'SSD' 
                    }}
                }}
                if (($isSystem -eq $true -or $isBoot -eq $true) -and $tipo -eq '') {{
                    $tipo = 'Interno'
                }}
                try {{
                    $diskInfo = Get-CimInstance -ClassName MSFT_PhysicalDisk | Where-Object DeviceID -eq $disk.Number
                    if ($diskInfo) {{
                        if ($tipo -eq '' -and $diskInfo.MediaType -eq 3) {{ $tipo = 'SSD' }}
                        if ($tipo -eq '' -and $diskInfo.MediaType -eq 4) {{ $tipo = 'SSD' }}
                        if ($tipo -eq '' -and $diskInfo.MediaType -eq 0) {{ $tipo = 'HDD' }}
                    }}
                }} catch {{ }}
                [PSCustomObject]@{{
                    Model = $diskModel
                    Tipo = $tipo
                    BusType = $busType
                    IsRemovable = $disk.IsRemovable
                }} | ConvertTo-Json
            }}"
            '''
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=5
            )
            if result.returncode == 0 and result.stdout.strip():
                try:
                    import json
                    disk_info = json.loads(result.stdout.strip())
                    if "Model" in disk_info and disk_info["Model"]:
                        model = disk_info["Model"].strip()
                        tipo_dispositivo = None
                        if "Tipo" in disk_info and disk_info["Tipo"]:
                            tipo_dispositivo = disk_info["Tipo"]
                        if not tipo_dispositivo:
                            modelo_lower = model.lower()
                            if "nvme" in modelo_lower or "m.2" in modelo_lower:
                                tipo_dispositivo = "NVMe"
                            elif "ssd" in modelo_lower or "solid" in modelo_lower:
                                tipo_dispositivo = "SSD"
                            elif "usb" in modelo_lower:
                                tipo_dispositivo = "USB"
                            if not tipo_dispositivo and "IsRemovable" in disk_info:
                                if disk_info["IsRemovable"] and win32file.GetDriveType(f"{drive_letter}\\") == win32file.DRIVE_REMOVABLE:
                                    tipo_dispositivo = "Externo"
                                elif "BusType" in disk_info and disk_info["BusType"] == "SATA":
                                    tipo_dispositivo = "SATA"
                                else:
                                    tipo_dispositivo = "Interno"
                        if tipo_dispositivo:
                            model = f"{model} ({tipo_dispositivo})"
                        _DISK_MODELS_CACHE[drive_letter] = model
                        return True
                except Exception as e:
                    print(f"Error procesando JSON para {drive_letter}: {e}")
        except Exception as e:
            print(f"Error en PowerShell para {drive_letter}: {e}")
        try:
            import wmi
            w = wmi.WMI()
            for disk in w.Win32_LogicalDisk(DeviceID=drive_letter):
                for partition in disk.associators("Win32_LogicalDiskToPartition"):
                    for physical_disk in partition.associators("Win32_DiskDriveToDiskPartition"):
                        if physical_disk.Model:
                            model = physical_disk.Model.strip()
                            tipo_dispositivo = None
                            try:
                                if hasattr(physical_disk, 'MediaType'):
                                    if physical_disk.MediaType == 'External hard disk media':
                                        tipo_dispositivo = "Externo"
                                    elif physical_disk.MediaType == 'Fixed hard disk media':
                                        tipo_dispositivo = "Interno"
                                if hasattr(physical_disk, 'InterfaceType'):
                                    if physical_disk.InterfaceType == 'USB':
                                        tipo_dispositivo = "USB"
                                    elif physical_disk.InterfaceType == 'SCSI':
                                        tipo_dispositivo = "SCSI"
                                    elif physical_disk.InterfaceType == 'SATA':
                                        tipo_dispositivo = "SATA"
                            except:
                                pass
                            if not tipo_dispositivo:
                                modelo_lower = model.lower()
                                if "nvme" in modelo_lower or "m.2" in modelo_lower:
                                    tipo_dispositivo = "NVMe"
                                elif "ssd" in modelo_lower or "solid" in modelo_lower:
                                    tipo_dispositivo = "SSD"
                                elif "usb" in modelo_lower:
                                    tipo_dispositivo = "USB"
                                elif win32file.GetDriveType(f"{drive_letter}\\") == win32file.DRIVE_REMOVABLE:
                                    tipo_dispositivo = "Externo"
                                elif drive_letter.upper() == "C:":
                                    tipo_dispositivo = "Interno"
                            if tipo_dispositivo:
                                model = f"{model} ({tipo_dispositivo})"
                            _DISK_MODELS_CACHE[drive_letter] = model
                            return True
            return False
        except:
            return False
    except:
        return False
    finally:
        if drive_letter in _DISCOS_EN_MEJORA:
            _DISCOS_EN_MEJORA.remove(drive_letter)

_DISK_MODELS_CACHE = {}

def init_disk_models_cache():
    """Inicializar caché de modelos de disco al inicio de la aplicación"""
    try:
        print("Inicializando caché de modelos de disco...")
        global _DISK_MODELS_CACHE
        
        # Clear the cache first to ensure fresh data
        _DISK_MODELS_CACHE.clear()
        
        # Get all available drives
        drives = []
        bitmask = windll.kernel32.GetLogicalDrives()
        for letter in range(ord('A'), ord('Z')+1):
            if bitmask & (1 << (letter - ord('A'))):
                drives.append(f"{chr(letter)}:")
        
        # First pass: quick initialization with basic info
        for drive in drives:
            try:
                if not os.path.exists(f"{drive}\\"):
                    continue
                    
                # Get basic drive type info (very fast operation)
                drive_type = win32file.GetDriveType(f"{drive}\\")
                
                # Try to get filesystem info (also fast)
                try:
                    volume_info = win32api.GetVolumeInformation(f"{drive}\\")
                    fs_type = volume_info[4] if volume_info and len(volume_info) > 4 else ""
                except:
                    fs_type = ""
                
                # Set basic type based on drive type
                if drive_type == win32file.DRIVE_UNKNOWN:
                    base_type = "Unidad desconocida"
                elif drive_type == win32file.DRIVE_NO_ROOT_DIR:
                    base_type = "Unidad no disponible"
                elif drive_type == win32file.DRIVE_REMOVABLE:
                    base_type = "Dispositivo extraíble"
                elif drive_type == win32file.DRIVE_FIXED:
                    if drive.upper() == "C:":
                        base_type = "Disco del sistema"
                    else:
                        base_type = "Disco interno"
                elif drive_type == win32file.DRIVE_REMOTE:
                    base_type = "Unidad de red"
                elif drive_type == win32file.DRIVE_CDROM:
                    base_type = "Unidad óptica"
                elif drive_type == win32file.DRIVE_RAMDISK:
                    base_type = "Disco RAM"
                else:
                    base_type = "Dispositivo de almacenamiento"
                
                # Add filesystem info if available
                if fs_type:
                    _DISK_MODELS_CACHE[drive] = f"{base_type} ({fs_type})"
                else:
                    _DISK_MODELS_CACHE[drive] = base_type
                    
            except Exception as e:
                print(f"Error inicializando modelo para {drive}: {e}")
                # Ensure we have at least a fallback value
                _DISK_MODELS_CACHE[drive] = "Dispositivo de almacenamiento"
        
        # Start background thread for detailed info
        thread = threading.Thread(
            target=_improve_disk_models_cache,
            args=(drives,),
            daemon=True
        )
        thread.start()
        
        print(f"Modelos de disco inicializados: {len(_DISK_MODELS_CACHE)} discos en caché")
    except Exception as e:
        print(f"Error inicializando caché de modelos: {e}")
        try:
            if os.path.exists("C:\\"):
                _DISK_MODELS_CACHE["C:"] = "Disco del sistema"
        except:
            pass

def _improve_disk_models_cache(drives):
    """Mejora la caché de modelos de disco con información detallada en segundo plano"""
    import pythoncom  # Añadir esta importación
    pythoncom.CoInitialize()  # Inicializar COM al inicio del hilo
    try:
        time.sleep(2)  # Esperar un poco a que la aplicación esté cargada
        for drive in drives:
            try:
                if not os.path.exists(f"{drive}\\"):
                    continue
                if _mejorar_modelo_individual(drive):
                    continue
                try:
                    import wmi
                    w = wmi.WMI()
                    for disk in w.Win32_LogicalDisk(DeviceID=drive):
                        for partition in disk.associators("Win32_LogicalDiskToPartition"):
                            for physical_disk in partition.associators("Win32_DiskDriveToDiskPartition"):
                                if physical_disk.Model:
                                    model = physical_disk.Model.strip()
                                    _DISK_MODELS_CACHE[drive] = model
                                    print(f"Modelo básico para {drive}: {model}")
                                    break
                except Exception as e:
                    print(f"Error en respaldo WMI para {drive}: {e}")
            except Exception as e:
                print(f"Error mejorando modelo para {drive}: {e}")
        print(f"Modelos de disco mejorados: {len(_DISK_MODELS_CACHE)} discos en caché")
    except Exception as e:
        print(f"Error general mejorando caché de modelos: {e}")
threading.Thread(target=init_disk_models_cache, daemon=True).start()

_DISK_MODELS_CACHE = {}
_USB_PORT_CACHE = {}

# Semáforo global para sincronización de acceso a la caché
_USB_PORT_CACHE_LOCK = threading.Semaphore(1)

def init_usb_port_mappings():
    """Verifica si hay nuevos dispositivos conectados después del inicio.
    Complementa la carga síncrona inicial.
    """
    print("Iniciando monitoreo de nuevos dispositivos...")
    try:
        import pythoncom
        pythoncom.CoInitialize()
        
        # Obtener todas las letras de unidad disponibles ahora
        drives = []
        bitmask = windll.kernel32.GetLogicalDrives()
        for letter in range(ord('A'), ord('Z')+1):
            if bitmask & (1 << (letter - ord('A'))):
                drive = f"{chr(letter)}:"
                if os.path.exists(f"{drive}\\"):
                    drives.append(drive)
        
        # Verificar si hay nuevos dispositivos que no estén en caché
        new_devices_found = False
        for drive_letter in drives:
            try:
                # Ignorar el disco C: y otros discos internos
                if drive_letter.upper() == "C:":
                    print(f"Ignorando disco del sistema: {drive_letter}")
                    continue
                    
                # Verificar tipo de disco - solo procesar removibles o externos
                drive_type = win32file.GetDriveType(f"{drive_letter}\\")
                if drive_type == win32file.DRIVE_REMOVABLE:
                    # Las unidades removibles siempre se procesan
                    pass  # Continuar con el procesamiento
                else:
                    # Intentar determinar si es un disco externo
                    try:
                        model = get_disk_model_direct(drive_letter)
                        model_lower = model.lower()
                        # Ampliar los términos de búsqueda para discos externos
                        if not ("externo" in model_lower or 
                               "usb" in model_lower or 
                               "usb-c" in model_lower or
                               "portable" in model_lower or
                               "passport" in model_lower or
                               "elements" in model_lower or
                               "seagate" in model_lower or
                               "toshiba" in model_lower):
                            # Verificar por BusType como último recurso
                            try:
                                cmd = f'powershell -NoProfile -Command "Get-Disk | Where-Object {{ $_.Number -eq (Get-Partition -DriveLetter {drive_letter[0]} -ErrorAction SilentlyContinue).DiskNumber }} | Select-Object -ExpandProperty BusType"'
                                process = subprocess.run(
                                    cmd,
                                    capture_output=True,
                                    text=True,
                                    shell=True,
                                    creationflags=subprocess.CREATE_NO_WINDOW,
                                    timeout=2
                                )
                                if process.returncode == 0 and process.stdout.strip():
                                    bus_type = process.stdout.strip().lower()
                                    if not "usb" in bus_type:
                                        print(f"Ignorando disco interno: {drive_letter}")
                                        continue
                                else:
                                    print(f"Ignorando disco probablemente interno: {drive_letter}")
                                    continue
                            except:
                                print(f"Ignorando disco probablemente interno: {drive_letter}")
                                continue
                    except:
                        # Si no podemos determinar, lo ignoramos
                        print(f"Ignorando disco probablemente interno: {drive_letter}")
                        continue
                
                # Verificar con el semáforo si ya está en caché
                with _USB_PORT_CACHE_LOCK:
                    already_cached = drive_letter in _USB_PORT_CACHE and _USB_PORT_CACHE[drive_letter][0]
                
                if already_cached:
                    continue
                
                # Solo para unidades válidas
                if not os.path.exists(f"{drive_letter}\\"):
                    continue
                
                # Nuevo dispositivo detectado después de la precarga inicial
                new_devices_found = True
                print(f"Nuevo dispositivo detectado después del inicio: {drive_letter}")
                
                # Usar directamente las funciones de MAPEO_PUERTO para máxima velocidad
                print(f"Precargando puerto para {drive_letter} (modo directo)...")
                start_time = time.time()
                
                # Usar directamente el método fallback que es más rápido y confiable
                port_id = get_port_id_fallback(drive_letter)
                
                if port_id:
                    port_name = get_port_name(port_id)
                    if not port_name:
                        port_name = "-"
                    
                    with _USB_PORT_CACHE_LOCK:
                        _USB_PORT_CACHE[drive_letter] = (port_id, port_name)
                    print(f"Puerto para nuevo dispositivo: {drive_letter} -> {port_id} ({port_name})")
            except Exception as e:
                print(f"Error verificando nuevo dispositivo {drive_letter}: {e}")
                
        if new_devices_found:
            with _USB_PORT_CACHE_LOCK:
                print(f"Caché actualizada con nuevos dispositivos. Total: {len(_USB_PORT_CACHE)} puertos.")
        else:
            print("No se detectaron nuevos dispositivos después del inicio.")
                
    except Exception as e:
        print(f"Error en monitoreo de nuevos dispositivos: {e}")
    finally:
        pythoncom.CoUninitialize()

def get_usb_port_info(drive_letter):
    """
    Obtiene la información del puerto USB para una letra de unidad específica.
    Ahora usa directamente el método fallback que es más rápido y confiable.
    """
    global _USB_PORT_CACHE
    drive_letter = drive_letter.strip().upper()

    # Verificar primero en la caché usando el semáforo
    with _USB_PORT_CACHE_LOCK:
        if drive_letter in _USB_PORT_CACHE:
            port_id, port_name = _USB_PORT_CACHE[drive_letter]
            # Solo devolver de caché si es un ID válido (no genérico)
            if port_id and not port_id.startswith("Unknown_") and not port_id.startswith("FAIL_"):
                return port_id, port_name

    try:
        # Usar directamente el método fallback que es más rápido y confiable
        start_time = time.time()
        port_id = get_port_id_fallback(drive_letter)
        
        if port_id:
            # Obtener el nombre del puerto
            port_name = get_port_name(port_id)
            # Si no hay nombre asignado, usar "-" como valor por defecto
            if not port_name:
                port_name = "-"
                
            # Actualizar la caché con el semáforo
            with _USB_PORT_CACHE_LOCK:
                _USB_PORT_CACHE[drive_letter] = (port_id, port_name)
            
            elapsed = time.time() - start_time
            print(f"Puerto USB para {drive_letter} obtenido en {elapsed:.2f}s: {port_id} ({port_name})")
            return port_id, port_name
            
    except Exception as e:
        print(f"Error obteniendo información del puerto USB para {drive_letter}: {e}")
        import traceback
        traceback.print_exc()

    # Si todo falla, devolver valores por defecto
    return "", ""

def preload_all_caches():
    """Precarga todas las cachés necesarias para mejorar el rendimiento"""
    print("Iniciando precarga de cachés en modo híbrido...")
    
    start_time = time.time()
    
    # Iniciar carga de modelos de disco (síncrono)
    init_disk_models_cache()
    
    # Obtener todas las letras de unidad disponibles
    drives = []
    removable_drives = []
    
    bitmask = windll.kernel32.GetLogicalDrives()
    for letter in range(ord('A'), ord('Z')+1):
        if bitmask & (1 << (letter - ord('A'))):
            drive = f"{chr(letter)}:"
            if os.path.exists(f"{drive}\\"):
                drives.append(drive)
                
                # Clasificar las unidades
                try:
                    # Ignorar el disco C: completamente
                    if drive.upper() == "C:":
                        print(f"Ignorando disco del sistema: {drive}")
                        continue
                        
                    drive_type = win32file.GetDriveType(f"{drive}\\")
                    if drive_type == win32file.DRIVE_REMOVABLE:
                        # Las unidades removibles siempre se procesan
                        print(f"Detectado disco removible: {drive}")
                        removable_drives.append(drive)
                    else:
                        # Intentar determinar si es un disco externo
                        try:
                            model = get_disk_model_direct(drive)
                            model_lower = model.lower()
                            # Ampliar los términos de búsqueda para discos externos
                            if ("externo" in model_lower or 
                                "usb" in model_lower or 
                                "usb-c" in model_lower or
                                "portable" in model_lower or
                                "passport" in model_lower or
                                "elements" in model_lower or
                                "seagate" in model_lower or
                                "toshiba" in model_lower):
                                print(f"Detectado disco externo: {drive} ({model})")
                                removable_drives.append(drive)  # Tratar discos externos como removibles
                            else:
                                # No procesar discos internos
                                print(f"Ignorando disco interno: {drive} ({model})")
                        except Exception as e:
                            # Si no podemos determinar, verificamos si es un disco del sistema
                            if drive.upper() != "C:":  # Doble verificación para C:
                                print(f"Verificando si {drive} es externo por otro método...")
                                try:
                                    # Intentar obtener información adicional para determinar si es externo
                                    cmd = f'powershell -NoProfile -Command "Get-Disk | Where-Object {{ $_.Number -eq (Get-Partition -DriveLetter {drive[0]} -ErrorAction SilentlyContinue).DiskNumber }} | Select-Object -ExpandProperty BusType"'
                                    process = subprocess.run(
                                        cmd,
                                        capture_output=True,
                                        text=True,
                                        shell=True,
                                        creationflags=subprocess.CREATE_NO_WINDOW,
                                        timeout=2
                                    )
                                    if process.returncode == 0 and process.stdout.strip():
                                        bus_type = process.stdout.strip().lower()
                                        if "usb" in bus_type:
                                            print(f"Detectado disco externo por BusType: {drive} ({bus_type})")
                                            removable_drives.append(drive)
                                        else:
                                            print(f"Ignorando disco interno por BusType: {drive} ({bus_type})")
                                    else:
                                        print(f"Ignorando disco probablemente interno: {drive}")
                                except:
                                    print(f"Ignorando disco probablemente interno: {drive}")
                            else:
                                print(f"Ignorando disco probablemente interno: {drive}")
                except Exception as e:
                    # Si hay error al determinar tipo, lo ignoramos
                    print(f"Error determinando tipo de disco: {drive}, ignorando: {str(e)}")
    
    # Precargar SOLO los dispositivos USB/removibles de forma síncrona
    if removable_drives:
        print(f"Precargando puertos para {len(removable_drives)} discos prioritarios de forma síncrona...")
        for drive_letter in removable_drives:
            try:
                if not os.path.exists(f"{drive_letter}\\"):
                    continue
                
                # Verificar con el semáforo si ya está en caché
                with _USB_PORT_CACHE_LOCK:
                    already_cached = drive_letter in _USB_PORT_CACHE and _USB_PORT_CACHE[drive_letter][0]
                
                if already_cached:
                    print(f"Puerto ya en caché para {drive_letter}: {_USB_PORT_CACHE[drive_letter]}")
                    continue
                
                # Usar directamente las funciones de MAPEO_PUERTO para máxima velocidad
                print(f"Precargando puerto para {drive_letter} (modo directo)...")
                
                # Para discos extraíbles usamos primero el método fallback que es más rápido
                port_id = get_port_id_fallback(drive_letter)
                
                if port_id:
                    # Obtener el nombre del puerto si existe
                    port_name = get_port_name(port_id)
                    # Si no hay nombre asignado, usar "-" como valor por defecto
                    if not port_name:
                        port_name = "-"
                    
                    # Actualizar la caché con el semáforo
                    with _USB_PORT_CACHE_LOCK:
                        _USB_PORT_CACHE[drive_letter] = (port_id, port_name)
                    
                    print(f"Puerto precargado para {drive_letter}: {port_id} ({port_name})")
            
            except Exception as e:
                print(f"Error precargando puerto para {drive_letter}: {e}")
    
    # Ya no iniciamos la precarga de discos fijos
    
    # Iniciar el monitoreo de nuevos dispositivos en segundo plano con baja prioridad
    threading.Thread(target=init_usb_port_mappings, daemon=True).start()
    
    elapsed = time.time() - start_time
    print(f"Precarga prioritaria completada en {elapsed:.2f}s. {len(removable_drives)} discos prioritarios.")

def preload_all_ports():
    """Precarga la información de todos los puertos de dispositivos conectados de forma síncrona"""
    try:
        import pythoncom
        pythoncom.CoInitialize()
        print("Precargando información de puertos para TODOS los discos (modo síncrono completo)...")
        
        # Obtener todas las letras de unidad disponibles
        drives = []
        bitmask = windll.kernel32.GetLogicalDrives()
        for letter in range(ord('A'), ord('Z')+1):
            if bitmask & (1 << (letter - ord('A'))):
                drives.append(f"{chr(letter)}:")
        
        # Priorizar unidades extraíbles (USB) y discos externos
        removable_drives = []
        external_drives = []
        
        for drive in drives:
            try:
                if not os.path.exists(f"{drive}\\"):
                    continue
                
                # Ignorar el disco C: completamente
                if drive.upper() == "C:":
                    print(f"Ignorando disco del sistema: {drive}")
                    continue
                    
                drive_type = win32file.GetDriveType(f"{drive}\\")
                if drive_type == win32file.DRIVE_REMOVABLE:
                    # Unidades USB tienen máxima prioridad
                    print(f"Detectado disco removible: {drive}")
                    removable_drives.append(drive)
                elif drive_type == win32file.DRIVE_FIXED:
                    # Intentar determinar si es un disco externo
                    try:
                        model = get_disk_model_direct(drive)
                        model_lower = model.lower()
                        # Ampliar los términos de búsqueda para discos externos
                        if ("externo" in model_lower or 
                            "usb" in model_lower or 
                            "usb-c" in model_lower or
                            "portable" in model_lower or
                            "passport" in model_lower or
                            "elements" in model_lower or
                            "seagate" in model_lower or
                            "toshiba" in model_lower):
                            print(f"Detectado disco externo: {drive} ({model})")
                            external_drives.append(drive)
                        else:
                            # Verificar por BusType como último recurso
                            try:
                                cmd = f'powershell -NoProfile -Command "Get-Disk | Where-Object {{ $_.Number -eq (Get-Partition -DriveLetter {drive[0]} -ErrorAction SilentlyContinue).DiskNumber }} | Select-Object -ExpandProperty BusType"'
                                process = subprocess.run(
                                    cmd,
                                    capture_output=True,
                                    text=True,
                                    shell=True,
                                    creationflags=subprocess.CREATE_NO_WINDOW,
                                    timeout=2
                                )
                                if process.returncode == 0 and process.stdout.strip():
                                    bus_type = process.stdout.strip().lower()
                                    if "usb" in bus_type:
                                        print(f"Detectado disco externo por BusType: {drive} ({bus_type})")
                                        external_drives.append(drive)
                                    else:
                                        print(f"Ignorando disco interno por BusType: {drive} ({bus_type})")
                                else:
                                    print(f"Ignorando disco interno: {drive}")
                            except:
                                print(f"Ignorando disco interno: {drive}")
                    except Exception as e:
                        print(f"Error determinando tipo de disco: {drive}, ignorando: {str(e)}")
            except Exception as e:
                print(f"Error procesando disco: {drive}, ignorando: {str(e)}")
        
        # Procesar solo discos removibles y externos
        all_drives = removable_drives + external_drives
        print(f"Discos para precarga: {len(all_drives)} total - {len(removable_drives)} extraíbles, {len(external_drives)} externos")
        
        # Procesar todas las unidades en orden de prioridad
        for drive_letter in all_drives:
            try:
                if not os.path.exists(f"{drive_letter}\\"):
                    continue
                
                # Verificar con el semáforo si ya está en caché
                with _USB_PORT_CACHE_LOCK:
                    already_cached = drive_letter in _USB_PORT_CACHE and _USB_PORT_CACHE[drive_letter][0]
                
                if already_cached:
                    print(f"Puerto ya en caché para {drive_letter}: {_USB_PORT_CACHE[drive_letter]}")
                    continue
                
                # Usar directamente las funciones de MAPEO_PUERTO para máxima velocidad
                print(f"Precargando puerto para {drive_letter} (modo directo)...")
                start_time = time.time()
                
                # Usar directamente el método fallback que es más rápido y confiable
                port_id = get_port_id_fallback(drive_letter)
                
                if port_id:
                    # Obtener el nombre del puerto si existe
                    port_name = get_port_name(port_id)
                    # Si no hay nombre asignado, usar "-" como valor por defecto
                    if not port_name:
                        port_name = "-"
                    
                    # Actualizar la caché con el semáforo
                    with _USB_PORT_CACHE_LOCK:
                        _USB_PORT_CACHE[drive_letter] = (port_id, port_name)
                    
                    elapsed = time.time() - start_time
                    print(f"Puerto precargado para {drive_letter} en {elapsed:.2f}s: {port_id} ({port_name})")
            
            except Exception as e:
                print(f"Error precargando puerto para {drive_letter}: {e}")
        
        # Imprimir resumen de la caché
        with _USB_PORT_CACHE_LOCK:
            print(f"Precarga de puertos completada en modo síncrono. {len(_USB_PORT_CACHE)} puertos en caché.")
    except Exception as e:
        print(f"Error general precargando puertos: {e}")
    finally:
        pythoncom.CoUninitialize()

if __name__ != "__main__":
    threading.Thread(target=preload_all_caches, daemon=True).start()
if __name__ == "__main__":
    unique_code = generate_unique_code()
    print(f"Unique Code: {unique_code}")
    print(f"Original Info: {get_original_info(unique_code)}")





