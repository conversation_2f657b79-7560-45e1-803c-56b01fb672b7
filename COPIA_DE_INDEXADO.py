from PyQt6 import QtCore
from PyQt6.QtWidgets import (
    Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QSpinBox, QToolTip, QFrame, QGraphicsDropShadowEffect
)
from PyQt6.QtCore import (
    Qt, QPropertyAnimation, QPoint, QTimer, 
    QEasingCurve
)
from PyQt6.QtGui import QPainter, QColor, QPainterPath, QShortcut, QKeySequence
from APARIENCIA import (
    ACCENT_POLICY, WINDOWCOMPOSITIONATTRIBDATA, 
    ACCENT_ENABLE_ACRYLICBLURBEHIND, WCA_ACCENT_POLICY,
    apply_acrylic_and_rounded
)
from TOOLTIP_APARIENCIA import showTooltipAtWidget, showTooltipAtCursor, showCustomTooltip
import ctypes
import os

class BaseDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.Popup | 
            Qt.WindowType.NoDropShadowWindowHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.initial_pos = None
        self.setWindowOpacity(0.0)
        
        # Layout principal común
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(15, 15, 15, 15)
        self.layout.setSpacing(10)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        rect = self.rect()
        path = QPainterPath()
        path.addRoundedRect(
            float(rect.x()), 
            float(rect.y()), 
            float(rect.width()), 
            float(rect.height()), 
            8.0, 
            8.0
        )
        painter.fillPath(path, QColor(0, 0, 0, 15))

    def showEvent(self, event):
        super().showEvent(event)
        self.final_pos = self.pos()
        start_pos = self.final_pos + QPoint(0, 50)
        self.move(start_pos)
        
        self.pos_anim = QPropertyAnimation(self, b"pos")
        self.pos_anim.setDuration(400)
        self.pos_anim.setStartValue(start_pos)
        self.pos_anim.setKeyValueAt(0.7, self.final_pos - QPoint(0, 10))
        self.pos_anim.setKeyValueAt(0.85, self.final_pos + QPoint(0, 5))
        self.pos_anim.setEndValue(self.final_pos)
        self.pos_anim.setEasingCurve(QEasingCurve.Type.OutBounce)
        
        self.fade_anim = QPropertyAnimation(self, b"windowOpacity")
        self.fade_anim.setDuration(300)
        self.fade_anim.setStartValue(0.0)
        self.fade_anim.setEndValue(1.0)
        self.fade_anim.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.pos_anim.start()
        self.fade_anim.start()

    def create_button(self, text, is_primary=True):
        """Crea un botón con el estilo común"""
        button = QPushButton(text)
        color = "#0078d7" if is_primary else "#d32f2f"
        hover_color = "#1e90ff" if is_primary else "#ef5350"
        pressed_color = "#005fb3" if is_primary else "#b71c1c"
        
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 16px;
                padding: 5px 15px;
                font-weight: bold;
                min-width: 60px;
                height: 24px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
        """)
        button.setCursor(Qt.CursorShape.PointingHandCursor)
        return button

    def create_button_layout(self):
        """Crea el layout de botones común"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        ok_button = self.create_button("OK", True)
        ok_button.clicked.connect(self.accept)
        
        cancel_button = self.create_button("CANCELAR", False)
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        return button_layout

class IndexCopyDialog(BaseDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(int(self.winId()))

        # Mensaje
        message = QLabel("Cantidad de archivos")
        message.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        message.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(message)

        # Input numérico simplificado con sombra
        self.number_input = QSpinBox()
        self.number_input.setRange(1, 99)
        self.number_input.setValue(10)
        self.number_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.number_input.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)  # Quitamos los botones
        self.number_input.setFixedSize(100, 32)
        self.number_input.setStyleSheet("""
            QSpinBox {
                color: white;
                background-color: rgba(128, 128, 128, 0.2);  /* Gris semitransparente */
                border: none;
                border-radius: 16px;
                padding: 0px 10px;  /* Padding horizontal para el texto */
                font-weight: bold;
                font-size: 14px;
            }
            
            /* Efecto de sombra */
            QSpinBox {
                border: 1px solid rgba(0, 0, 0, 0.2);
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            }
            
            /* Quitar el borde cuando tiene foco */
            QSpinBox:focus {
                border: 1px solid rgba(0, 0, 0, 0.3);
                outline: none;
            }
        """)

        # Agregar efecto de sombra usando QGraphicsDropShadowEffect
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 127))  # Negro semitransparente
        shadow.setOffset(0, 0)  # Sombra en todas direcciones
        self.number_input.setGraphicsEffect(shadow)

        # Centrar el SpinBox
        input_container = QHBoxLayout()
        input_container.addStretch()
        input_container.addWidget(self.number_input)
        input_container.addStretch()
        self.layout.addLayout(input_container)

        # Botones
        button_layout = self.create_button_layout()
        self.layout.addLayout(button_layout)

class ExtensionDiffDialog(BaseDialog):
    def __init__(self, current_file, next_file, current_ext, next_ext, parent=None):
        super().__init__(parent)
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd)

        # Mensaje de advertencia
        message = QLabel("EXTENSIÓN DIFERENTE")
        message.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        message.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(message)

        # Mensaje genérico sin mostrar los nombres de archivo
        info_text = "Las extensiones de los archivos no coinciden.\n¿Desea continuar con la copia?"
        info_label = QLabel(info_text)
        info_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                background: transparent;
            }
        """)
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(info_label)

        # Botones
        button_layout = self.create_button_layout()
        self.layout.addLayout(button_layout)

class IndexCopyHandler:
    def __init__(self, main_window):
        self.main_window = main_window
        self.setup_shortcuts()

    def verify_file_exists(self, source_path, dest_path):
        """Verifica si el archivo ya existe en el destino con el mismo tamaño"""
        try:
            # Si el archivo fuente termina en .txt, verificar si es un archivo con extensión + .txt
            if source_path.lower().endswith('.txt'):
                base_name = os.path.basename(source_path)
                # Verificar si hay otra extensión antes del .txt
                name_parts = base_name.rsplit('.txt', 1)[0]
                if '.' in name_parts:  # Si hay un punto, probablemente hay otra extensión
                    # Construir la ruta sin el .txt
                    real_dest_path = os.path.join(os.path.dirname(dest_path), name_parts)
                    if os.path.exists(real_dest_path):
                        source_size = os.path.getsize(source_path)
                        dest_size = os.path.getsize(real_dest_path)
                        return source_size == dest_size
                
            # Verificación normal si no es un caso especial
            if not os.path.exists(dest_path):
                return False
                
            source_size = os.path.getsize(source_path)
            dest_size = os.path.getsize(dest_path)
            return source_size == dest_size
                
        except Exception as e:
            print(f"Error verificando archivo: {e}")
            return False

    def setup_shortcuts(self):
        """Configura los atajos de teclado para la copia indexada"""
        # Atajo para tecla '+' del teclado numérico
        plus_shortcut = QShortcut(QKeySequence(Qt.Key.Key_Plus), self.main_window)
        plus_shortcut.activated.connect(lambda: self.show_copy_amount_dialog(False))
        
        # Atajo para Ctrl+'+'
        ctrl_plus_shortcut = QShortcut(QKeySequence("Ctrl++"), self.main_window)
        ctrl_plus_shortcut.activated.connect(lambda: self.show_copy_amount_dialog(True))

    def show_copy_amount_dialog(self, ctrl_pressed):
        """Muestra el diálogo para seleccionar la cantidad de archivos a copiar"""
        # Verificar si estamos en la vista de explorador
        if not hasattr(self.main_window, 'file_tree_widget') or not hasattr(self.main_window, 'current_explorer_drive'):
            tooltip_text = "Esta función solo está disponible en la vista de explorador"
            showTooltipAtWidget(tooltip_text, self.main_window, 3000)
            return

        # Verificar si hay archivos seleccionados
        if hasattr(self.main_window, 'file_tree_widget'):
            if not self.main_window.file_tree_widget.selectedItems():
                tooltip_text = "Seleccione un archivo primero"
                showTooltipAtWidget(tooltip_text, self.main_window, 3000)
                return

        dialog = IndexCopyDialog(self.main_window)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                # Obtener la cantidad seleccionada
                amount = dialog.number_input.value()
                
                # Verificar que la cantidad sea válida
                if amount <= 0:
                    self.show_tooltip("La cantidad debe ser mayor que cero")
                    return
                    
                # Desactivar actualizaciones visuales antes de la copia
                if hasattr(self.main_window, 'file_tree_widget'):
                    self.main_window.file_tree_widget.setUpdatesEnabled(False)
                
                # Llamar a queue_next_n_files con la cantidad seleccionada
                self.main_window.queue_next_n_files(amount, ctrl_pressed)
                
            except Exception as e:
                print(f"Error en la copia indexada: {e}")
                import traceback
                traceback.print_exc()
                self.show_tooltip(f"Error al procesar la copia: {str(e)}")
                # Asegurar que las actualizaciones visuales se reactiven en caso de error
                if hasattr(self.main_window, 'file_tree_widget'):
                    self.main_window.file_tree_widget.setUpdatesEnabled(True)

    def show_tooltip(self, message):
        """Muestra un tooltip con el mensaje especificado"""
        showTooltipAtWidget(message, self.main_window, 3000)

    def get_real_filename(self, source_path):
        """
        Determina el nombre real del archivo, eliminando .txt si ya tiene otra extensión
        """
        filename = os.path.basename(source_path)
        print(f"Procesando archivo: {filename}")
        
        # Si no termina en .txt, devolver el nombre original
        if not filename.lower().endswith('.txt'):
            print(f"No termina en .txt, devolviendo: {filename}")
            return filename
        
        # Quitar el .txt
        name_without_txt = filename[:-4]
        print(f"Nombre sin .txt: {name_without_txt}")
        
        # Buscar la última ocurrencia de un punto (posible extensión)
        last_dot_pos = name_without_txt.rfind('.')
        
        # Si no hay punto, no hay extensión adicional
        if last_dot_pos == -1:
            print(f"No se encontró otra extensión, devolviendo: {filename}")
            return filename  # Devolver el nombre original con .txt
        
        # Extraer la posible extensión
        possible_ext = name_without_txt[last_dot_pos:].lower()
        print(f"Posible extensión encontrada: {possible_ext}")
        
        # Lista de extensiones comunes
        common_extensions = [
            # Video
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.mpg', '.mpeg',
            # Audio
            '.mp3', '.wav', '.ogg', '.flac', '.aac', '.wma',
            # Imagen
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp',
            # Documentos
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            # Otros
            '.zip', '.rar', '.7z', '.tar', '.gz'
        ]
        
        # Si la extensión es reconocida, usar el nombre sin .txt
        if any(possible_ext == ext for ext in common_extensions):
            print(f"Extensión reconocida, devolviendo: {name_without_txt}")
            return name_without_txt
        
        # Si llegamos aquí, mantener el nombre original
        print(f"Extensión no reconocida, devolviendo: {filename}")
        return filename

    def get_file_extension(self, filename):
        """Obtiene la extensión de un archivo"""
        last_dot_pos = filename.rfind('.')
        if last_dot_pos != -1:
            return filename[last_dot_pos:].lower()
        return ""
        
