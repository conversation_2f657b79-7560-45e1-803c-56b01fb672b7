import os
import json
import queue
import traceback
import win32api
from PyQt6.QtWidgets import QFileDialog
from TOOLTIP_APARIENCIA import showTooltipAtCursor

def format_size(size_bytes):
    """Formatea un tamaño en bytes a una representación legible."""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.2f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.2f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"

def salvar_copia(main_window):
    """Guarda la cola de archivos en un archivo JSON o de texto."""
    selected_items = main_window.list_widget.selectedItems()
    if selected_items:
        selected_volume = selected_items[0].text()
        drive_letter = selected_volume.split(' ')[-1].strip('()')
        file_path, _ = QFileDialog.getSaveFileName(main_window, "Guardar Cola de Archivos", "", "JSON Files (*.json);;Text Files (*.txt);;All Files (*)")
        if file_path:
            # Usar formato JSON compatible con pending_copies
            if file_path.lower().endswith('.json'):
                queue_data = {
                    "drive_letter": drive_letter,
                    "queue": []
                }
                
                # Obtener los archivos en el orden exacto de la cola
                ordered_files = []
                
                # 1. Primero agregar el archivo que se está copiando actualmente (si existe)
                if drive_letter in main_window.current_copying_file and main_window.current_copying_file[drive_letter]:
                    current_file = main_window.current_copying_file[drive_letter]
                    if current_file and isinstance(current_file, tuple) and len(current_file) == 2:
                        ordered_files.append(current_file)
                        print(f"Archivo en copia actual agregado primero: {current_file[0]}")
                
                # 2. Luego agregar el resto de archivos en la cola
                if drive_letter in main_window.queues:
                    # Crear una copia temporal de la cola para no alterarla
                    temp_queue = queue.Queue()
                    temp_list = []
                    
                    # Extraer todos los elementos de la cola original
                    while not main_window.queues[drive_letter].empty():
                        item = main_window.queues[drive_letter].get()
                        temp_list.append(item)
                    
                    # Restaurar la cola original y construir la lista ordenada
                    for item in temp_list:
                        main_window.queues[drive_letter].put(item)
                        # Solo agregar si no es el archivo actual (para evitar duplicados)
                        if not (drive_letter in main_window.current_copying_file and 
                                main_window.current_copying_file[drive_letter] == item):
                            ordered_files.append(item)
                
                # 3. Agregar archivos que están en files_in_queue pero no en la cola actual
                # (pueden haber sido procesados ya)
                for source_path, destination_path in main_window.files_in_queue:
                    if destination_path.startswith(drive_letter) and (source_path, destination_path) not in ordered_files:
                        ordered_files.append((source_path, destination_path))
                
                # Crear la lista de archivos en el formato correcto
                for source_path, destination_path in ordered_files:
                    if destination_path.startswith(drive_letter):
                        queue_data["queue"].append({
                            "source": source_path,
                            "destination": destination_path
                        })
                
                with open(file_path, 'w', encoding='utf-8') as file:
                    json.dump({f"{selected_volume}": queue_data}, file, indent=2, ensure_ascii=False)
            # Formato de texto legible (antiguo)
            else:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(f"Disco: {drive_letter}\n")
                    
                    # Obtener archivos ordenados con el mismo método mejorado
                    ordered_files = []
                    
                    # 1. Primero agregar el archivo que se está copiando actualmente
                    if drive_letter in main_window.current_copying_file and main_window.current_copying_file[drive_letter]:
                        current_file = main_window.current_copying_file[drive_letter]
                        if current_file and isinstance(current_file, tuple) and len(current_file) == 2:
                            ordered_files.append(current_file)
                            print(f"Archivo en copia actual agregado primero: {current_file[0]}")
                    
                    # 2. Luego agregar el resto de archivos en la cola
                    if drive_letter in main_window.queues:
                        temp_queue = queue.Queue()
                        temp_list = []
                        
                        while not main_window.queues[drive_letter].empty():
                            item = main_window.queues[drive_letter].get()
                            temp_list.append(item)
                        
                        for item in temp_list:
                            main_window.queues[drive_letter].put(item)
                            # Solo agregar si no es el archivo actual (para evitar duplicados)
                            if not (drive_letter in main_window.current_copying_file and 
                                    main_window.current_copying_file[drive_letter] == item):
                                ordered_files.append(item)
                    
                    # 3. Agregar archivos que están en files_in_queue pero no en la cola actual
                    for source_path, destination_path in main_window.files_in_queue:
                        if destination_path.startswith(drive_letter) and (source_path, destination_path) not in ordered_files:
                            ordered_files.append((source_path, destination_path))
                    
                    # Agrupar por directorio para el formato de texto
                    current_dir = None
                    for source_path, destination_path in ordered_files:
                        if destination_path.startswith(drive_letter):
                            dir_name = os.path.dirname(source_path)
                            if dir_name != current_dir:
                                file.write(f"\nDirectorio: {dir_name}\n")
                                current_dir = dir_name
                            file.write(f"{source_path} -> {destination_path}\n")
            
            print(f"Cola de archivos guardada en {file_path}")
            # Mostrar tooltip de confirmación
            showTooltipAtCursor(f"Cola guardada en:\n{os.path.basename(file_path)}", duration=3000, parent=main_window)

def cargar_copia(main_window):
    """Carga la cola de archivos desde un archivo JSON o de texto."""
    selected_items = main_window.list_widget.selectedItems()
    if selected_items:
        selected_volume = selected_items[0].text()
        drive_letter = selected_volume.split(' ')[-1].strip('()')
        file_path, _ = QFileDialog.getOpenFileName(main_window, "Cargar Cola de Archivos", "", "JSON Files (*.json);;Text Files (*.txt);;All Files (*)")
        if file_path:
            # Preparar lista de archivos para procesar como si fueran arrastrados
            processed_files = []
            total_size = 0
            
            # Formato JSON (compatible con pending_copies)
            if file_path.lower().endswith('.json'):
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        data = json.load(file)
                        
                    # Procesar cada disco en el archivo
                    for drive_key, drive_data in data.items():
                        saved_drive_letter = drive_data.get('drive_letter')
                        
                        # Verificar que coincida con el disco seleccionado
                        if saved_drive_letter != drive_letter:
                            continue
                            
                        queue_items = drive_data.get('queue', [])
                        for item in queue_items:
                            src = item.get('source')
                            dst = item.get('destination')
                            
                            if not src or not dst or not os.path.exists(src):
                                continue
                                
                            # Verificar si el archivo ya existe con el mismo tamaño
                            if os.path.exists(dst) and os.path.getsize(src) == os.path.getsize(dst):
                                continue
                                
                            try:
                                file_size = os.path.getsize(src)
                                total_size += file_size
                                processed_files.append((src, file_size, dst))
                            except OSError as e:
                                print(f"Error obteniendo tamaño del archivo {src}: {e}")
                
                except Exception as e:
                    print(f"Error cargando archivo JSON: {e}")
                    traceback.print_exc()
            
            # Formato de texto (antiguo)
            else:
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        lines = file.readlines()
                    
                    current_dir = None
                    for line in lines[1:]:
                        line = line.strip()
                        if not line:
                            continue
                            
                        if line.startswith("Directorio: "):
                            current_dir = line.split("Directorio: ")[1]
                        elif " -> " in line:
                            src_path, dst_path = line.split(' -> ')
                            if not os.path.isabs(src_path) and current_dir:
                                src_path = os.path.join(current_dir, src_path)
                                
                            if not os.path.exists(src_path):
                                continue
                                
                            # Verificar si el archivo ya existe con el mismo tamaño
                            if os.path.exists(dst_path) and os.path.getsize(src_path) == os.path.getsize(dst_path):
                                continue
                                
                            try:
                                file_size = os.path.getsize(src_path)
                                total_size += file_size
                                processed_files.append((src_path, file_size, dst_path))
                            except OSError as e:
                                print(f"Error obteniendo tamaño del archivo {src_path}: {e}")
                
                except Exception as e:
                    print(f"Error cargando archivo de texto: {e}")
                    traceback.print_exc()
            
            # Procesar los archivos usando el mismo flujo que el arrastre
            if processed_files:
                print(f"Procesando {len(processed_files)} archivos cargados desde {file_path}")
                
                # Obtener el nombre del volumen
                volume_name = ""
                try:
                    volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                except:
                    pass
                
                # Mostrar tooltip con información de los archivos cargados (como al arrastrar)
                formatted_size = format_size(total_size)
                mensaje = f"Cargando: {len(processed_files)} archivos\nTamaño: {formatted_size}"
                showTooltipAtCursor(mensaje, duration=5000, parent=main_window)
                
                # Usar el mismo método que se usa para el arrastre de archivos
                if hasattr(main_window.list_widget, 'process_dropped_files'):
                    main_window.list_widget.process_dropped_files(
                        processed_files=processed_files,
                        drive_letter=drive_letter,
                        shift_pressed=False,
                        current_path=f"{drive_letter}\\",
                        volume_name=volume_name,
                        dragging_folder=False
                    )
                    print(f"Archivos cargados procesados como arrastrados")
                    
                    # Reproducir sonido como cuando se arrastran archivos
                    try:
                        from ZETACOPY import play_drag_sound
                        play_drag_sound(len(processed_files))
                    except Exception as e:
                        print(f"Error reproduciendo sonido: {e}")
                else:
                    print(f"No se encontró el método process_dropped_files")
                    # Usar showTooltipAtCursor en lugar de QMessageBox
                    showTooltipAtCursor(f"SE HAN AGREGADO {len(processed_files)} ARCHIVOS A LA COLA DE COPIA", duration=3000, parent=main_window)
            else:
                print("No se agregaron archivos nuevos a la cola")
                # Usar showTooltipAtCursor en lugar de QMessageBox
                showTooltipAtCursor("NO SE HAN AGREGADO ARCHIVOS NUEVOS A LA COLA", duration=3000, parent=main_window)