from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout
from PyQt6.QtCore import Qt, QTimer, QSize
from PyQt6.QtGui import QPixmap
import os
from APARIENCIA import apply_acrylic_and_rounded

def show_license_activation_dialog(parent_window, main_window=None):
    """
    Muestra el diálogo de activación de licencia.
    
    Args:
        parent_window: Ventana padre donde se mostrará el diálogo
        main_window: Referencia a la ventana principal (opcional)
    
    Returns:
        bool: True si se aceptó la activación, False si se canceló
    """
    class AcrylicDialog(QDialog):
        def showEvent(self, event):
            super().showEvent(event)
            apply_acrylic_and_rounded(int(self.winId()), mode='dark')
    
    dialog = AcrylicDialog(parent_window)
    dialog.setWindowTitle("Licencia Requerida")
    dialog.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
    dialog.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
    dialog.setFixedSize(320, 320)
    dialog.setStyleSheet("""
        QDialog {
            border-radius: 12px;
        }
        QLabel {
            color: white;
            font-size: 11px;
            font-weight: bold;
        }
        QPushButton {
            background-color: #00a2ff;
            color: white;
            border-radius: 8px;
            padding: 5px 10px;
            font-weight: bold;
            font-size: 10px;
        }
        QPushButton:hover {
            background-color: #0088d1;
        }
        QPushButton:pressed {
            background-color: #006699;
        }
    """)
    
    # Layout principal
    main_layout = QVBoxLayout(dialog)
    main_layout.setContentsMargins(15, 5, 15, 15)
    main_layout.setSpacing(3)
    
    # Título
    title_label = QLabel("Se requiere licencia")
    title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #00a2ff;")
    title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    title_label.setContentsMargins(0, 0, 0, 0)
    title_label.setFixedHeight(20)
    main_layout.addWidget(title_label)
    
    # Icono
    icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'ZETACOPY.ico')
    icon_label = QLabel()
    icon_pixmap = QPixmap(icon_path)
    icon_pixmap = icon_pixmap.scaled(90, 90, Qt.AspectRatioMode.KeepAspectRatio, 
                                   Qt.TransformationMode.SmoothTransformation)
    icon_label.setPixmap(icon_pixmap)
    icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    icon_label.setContentsMargins(0, 0, 0, 0)
    main_layout.addWidget(icon_label)
    
    # Mensaje
    message_label = QLabel(
        "Para acceder a esta función, necesita activar ZETACOPY Premium.\n\n"
        "La licencia no se encuentra o ha sido modificada.\n"
        "Por favor, active su licencia para continuar."
    )
    message_label.setStyleSheet("font-size: 11px; font-weight: bold;")
    message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    message_label.setWordWrap(True)
    message_label.setContentsMargins(0, 0, 0, 0)
    main_layout.addWidget(message_label)
    
    # Botones
    button_layout = QHBoxLayout()
    button_layout.setSpacing(8)
    button_layout.setContentsMargins(0, 5, 0, 0)
    
    cancel_button = QPushButton("Cancelar")
    cancel_button.setStyleSheet("""
        QPushButton {
            background-color: rgba(80, 80, 80, 0.7);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 4px 8px;
            font-weight: bold;
            font-size: 11px;
            min-height: 22px;
        }
        QPushButton:hover {
            background-color: rgba(100, 100, 100, 0.8);
        }
        QPushButton:pressed {
            background-color: rgba(60, 60, 60, 0.9);
        }
    """)
    cancel_button.setMinimumWidth(70)
    cancel_button.setCursor(Qt.CursorShape.PointingHandCursor)
    cancel_button.clicked.connect(dialog.reject)
    
    activate_button = QPushButton("ACTIVAR AHORA")
    activate_button.setStyleSheet("""
        QPushButton {
            background-color: #00a2ff;
            color: white;
            border: none;
            border-radius: 15px;
            padding: 4px 8px;
            font-weight: bold;
            font-size: 11px;
            min-height: 22px;
        }
        QPushButton:hover {
            background-color: #0088d1;
        }
        QPushButton:pressed {
            background-color: #006699;
        }
    """)
    activate_button.setMinimumWidth(90)
    activate_button.setCursor(Qt.CursorShape.PointingHandCursor)
    
    # Asegurarse de que los botones mantengan su forma
    cancel_button.setFixedHeight(22)
    activate_button.setFixedHeight(22)
    
    def recreate_settings_window():
        dialog.accept()
        if main_window and hasattr(main_window, 'settings_window'):
            # Cerrar la ventana actual
            main_window.settings_window.close()
            # Eliminar la referencia para forzar la creación de una nueva
            main_window.settings_window = None
            # Reabrir la ventana de ajustes después de un breve retraso
            from BOTONES import Botones
            QTimer.singleShot(300, lambda: Botones.open_ajustes(main_window.botones))
    
    activate_button.clicked.connect(recreate_settings_window)
    
    button_layout.addWidget(cancel_button)
    button_layout.addWidget(activate_button)
    main_layout.addLayout(button_layout)
    
    # Centrar en la ventana padre
    center_pos = parent_window.rect().center() - dialog.rect().center()
    dialog.move(parent_window.mapToGlobal(center_pos))
    
    return dialog.exec() == QDialog.DialogCode.Accepted
