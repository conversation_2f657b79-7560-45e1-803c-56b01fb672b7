import sys
import base64
import os
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTextEdit, QPushButton, QLabel, QMessageBox, QDialog, 
    QDateEdit, QFrame, QSizePolicy, QScrollArea, QListWidget,
    QListWidgetItem, QStackedWidget
)
from PyQt6.QtCore import Qt, QDate, QSize, QTimer, pyqtSignal, QEvent
from PyQt6.QtGui import QColor, QPalette, QFont, QWheelEvent
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.fernet import Fernet
import ctypes
from ctypes import windll, byref, sizeof, c_int, POINTER, c_bool, Structure, addressof
import hashlib

# Definiciones para el efecto acrílico
class ACCENT_POLICY(Structure):
    _fields_ = [
        ('AccentState', c_int),
        ('AccentFlags', c_int),
        ('GradientColor', c_int),
        ('AnimationId', c_int),
    ]

class WINDOWCOMPOSITIONATTRIBDATA(Structure):
    _fields_ = [
        ('Attrib', c_int),
        ('Data', POINTER(c_int)),
        ('SizeOfData', c_int)
    ]

def apply_acrylic(hwnd):
    accent = ACCENT_POLICY()
    accent.AccentState = 3  # ACCENT_ENABLE_BLURBEHIND
    accent.GradientColor = 0x990000 & 0xFFFFFFFF  # 60% transparencia

    data = WINDOWCOMPOSITIONATTRIBDATA()
    data.Attrib = 19  # WCA_ACCENT_POLICY
    data.SizeOfData = sizeof(accent)
    data.Data = POINTER(c_int)(c_int.from_address(addressof(accent)))

    SetWindowCompositionAttribute = windll.user32.SetWindowCompositionAttribute
    SetWindowCompositionAttribute.argtypes = [c_int, POINTER(WINDOWCOMPOSITIONATTRIBDATA)]
    SetWindowCompositionAttribute.restype = c_bool
    SetWindowCompositionAttribute(hwnd, byref(data))

def set_rounded_corners(hwnd):
    DWMWA_WINDOW_CORNER_PREFERENCE = 33
    DWMWCP_ROUND = 2
    windll.dwmapi.DwmSetWindowAttribute(hwnd, DWMWA_WINDOW_CORNER_PREFERENCE, byref(c_int(DWMWCP_ROUND)), sizeof(c_int))

def get_original_info(code):
    # Eliminar guiones si los hay
    code = code.replace('-', '')
    
    try:
        # Decodificar el código base64
        decoded_bytes = base64.urlsafe_b64decode(code)
        
        # Convertir a texto
        decoded_text = decoded_bytes.decode('utf-8')
        
        # Verificar formato
        parts = decoded_text.split('|')
        if len(parts) != 3:  # Ahora son 3 partes: modelo, fabricante y fecha
            print(f"Advertencia: Formato diferente al esperado. Se encontraron {len(parts)} partes.")
            # Intentar manejar formatos antiguos o diferentes
            if len(parts) == 5:  # Formato antiguo
                # Extraer solo modelo, fabricante y fecha
                model = parts[0]
                manufacturer = parts[1]
                date = parts[4]  # La fecha estaba en la posición 4
                return f"{model}|{manufacturer}|{date}"
        
        return decoded_text
        
    except Exception as e:
        print(f"Error desencriptando código: {e}")
        raise ValueError(f"Código inválido: {str(e)}")

class CustomTextEdit(QFrame):
    def __init__(self, readonly=False, placeholder=""):
        super().__init__()
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # TextEdit
        self.text_edit = QTextEdit()
        self.text_edit.setReadOnly(readonly)
        self.text_edit.setPlaceholderText(placeholder)
        self.text_edit.setStyleSheet("""
            QTextEdit {
                background-color: rgba(255, 255, 255, 30);
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 5px;
                color: white;
                padding: 5px;
                font-family: Consolas, monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.text_edit)
    
    def setText(self, text):
        self.text_edit.setText(text)
    
    def toPlainText(self):
        return self.text_edit.toPlainText()

class DesencriptadorWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Desencriptador de Licencias ZETACOPY")
        self.setFixedSize(900, 700)  # Aumentado de 800x600 a 900x700
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        
        # Aplicar efecto acrílico
        hwnd = int(self.winId())
        apply_acrylic(hwnd)
        set_rounded_corners(hwnd)
        
        # Crear el widget de fondo principal
        self.background = QWidget(self)
        self.background.setObjectName("background")
        self.background.setStyleSheet("""
            QWidget#background {
                background-color: rgba(0, 0, 0, 0.01);
                border-radius: 10px;
            }
        """)
        self.background.setGeometry(0, 0, self.width(), self.height())
        
        # Variables para el arrastre de ventana
        self.dragging = False
        self.offset = None
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # Crear layout superior para el título y botón de cierre
        top_layout = QHBoxLayout()
        
        # Título
        title_label = QLabel("DESENCRIPTADOR DE LICENCIAS ZETACOPY")
        title_label.setStyleSheet("color: white; font-size: 18px; font-weight: bold;")
        top_layout.addWidget(title_label)
        
        top_layout.addStretch()
        
        # Crear botón de cierre
        close_button = QPushButton("CERRAR")
        close_button.setFixedSize(80, 24)
        close_button.setCursor(Qt.CursorShape.PointingHandCursor)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 5px 15px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #ef5350;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        close_button.clicked.connect(self.close)
        top_layout.addWidget(close_button)
        main_layout.addLayout(top_layout)
        
        # Contenido principal
        content_layout = QVBoxLayout()
        content_layout.setSpacing(15)
        
        # Sección de entrada AES
        aes_label = QLabel("CÓDIGO ENCRIPTADO (AES):")
        aes_label.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")
        content_layout.addWidget(aes_label)
        
        self.aes_input = CustomTextEdit(placeholder="Introduce el código cifrado AES aquí...")
        self.aes_input.setMinimumHeight(80)
        content_layout.addWidget(self.aes_input)
        
        # Botón de desencriptar
        decrypt_button = QPushButton("Desencriptar AES")
        decrypt_button.setCursor(Qt.CursorShape.PointingHandCursor)
        decrypt_button.setStyleSheet("""
            QPushButton {
                background-color: #FF8C00;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FFA500;
            }
            QPushButton:pressed {
                background-color: #FF7F00;
            }
        """)
        decrypt_button.clicked.connect(self.decrypt_aes)
        content_layout.addWidget(decrypt_button)
        
        # Sección de salida Base64
        base64_label = QLabel("CÓDIGO DESENCRIPTADO (BASE64):")
        base64_label.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")
        content_layout.addWidget(base64_label)
        
        self.base64_output = CustomTextEdit(readonly=True)
        self.base64_output.setMinimumHeight(80)
        content_layout.addWidget(self.base64_output)
        
        # Sección de información desencriptada
        info_label = QLabel("INFORMACIÓN DESENCRIPTADA:")
        info_label.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")
        content_layout.addWidget(info_label)
        
        self.info_output = CustomTextEdit(readonly=True)
        self.info_output.setMinimumHeight(120)
        content_layout.addWidget(self.info_output)
        
        # Sección de fecha y generación de código
        date_layout = QHBoxLayout()
        date_layout.setSpacing(10)  # Reducido el espaciado
        
        date_label = QLabel("Fecha de vencimiento:")
        date_label.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")
        
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate().addDays(30))  # Por defecto 30 días
        self.date_edit.setMinimumDate(QDate.currentDate())  # No permitir fechas pasadas
        self.date_edit.setDisplayFormat("dd/MM/yyyy")
        self.date_edit.setFixedWidth(120)  # Ancho fijo para el selector de fecha
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background-color: rgba(255, 255, 255, 30);
                color: white;
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 5px;
                padding: 5px;
                font-family: Arial;
                font-size: 12px;
            }
            QDateEdit::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left-width: 1px;
                border-left-color: rgba(255, 255, 255, 30);
                border-left-style: solid;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }
        """)
        
        generate_button = QPushButton("Generar Nuevo Código")
        generate_button.setCursor(Qt.CursorShape.PointingHandCursor)
        generate_button.setStyleSheet("""
            QPushButton {
                background-color: #0078D7;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1E90FF;
            }
            QPushButton:pressed {
                background-color: #0066CC;
            }
        """)
        generate_button.clicked.connect(self.generate_new_code)
        
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.date_edit)
        date_layout.addWidget(generate_button)
        date_layout.addStretch(1)  # Añadir espacio flexible al final
        
        content_layout.addLayout(date_layout)
        
        # Botones de copia
        button_layout = QHBoxLayout()
        
        copy_aes_button = QPushButton("Copiar AES")
        copy_aes_button.setCursor(Qt.CursorShape.PointingHandCursor)
        copy_aes_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #66BB6A;
            }
            QPushButton:pressed {
                background-color: #43A047;
            }
        """)
        copy_aes_button.clicked.connect(self.copy_aes)
        
        copy_base64_button = QPushButton("Copiar Base64")
        copy_base64_button.setCursor(Qt.CursorShape.PointingHandCursor)
        copy_base64_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #BA68C8;
            }
            QPushButton:pressed {
                background-color: #7B1FA2;
            }
        """)
        copy_base64_button.clicked.connect(self.copy_base64)
        
        copy_info_button = QPushButton("Copiar Información")
        copy_info_button.setCursor(Qt.CursorShape.PointingHandCursor)
        copy_info_button.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FF7043;
            }
            QPushButton:pressed {
                background-color: #E64A19;
            }
        """)
        copy_info_button.clicked.connect(self.copy_info)
        
        button_layout.addWidget(copy_aes_button)
        button_layout.addWidget(copy_base64_button)
        button_layout.addWidget(copy_info_button)
        
        content_layout.addLayout(button_layout)
        main_layout.addLayout(content_layout)
    
    def showEvent(self, event):
        super().showEvent(event)
        # Aplicar efecto acrílico cuando la ventana se muestra
        hwnd = self.winId()
        apply_acrylic(int(hwnd))
    
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = True
            self.offset = event.position().toPoint()
    
    def mouseMoveEvent(self, event):
        if self.dragging and self.offset:
            self.move(self.mapToGlobal(event.position().toPoint() - self.offset))
    
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
    
    def decrypt_aes(self):
        try:
            # Obtener el código AES
            aes_code = self.aes_input.toPlainText().strip()
            if not aes_code:
                QMessageBox.warning(self, "Error", "Por favor, ingrese un código AES")
                return
            
            # Desencriptar AES a Base64
            try:
                # Eliminar guiones
                code_raw = aes_code.replace('-', '')
                
                # Decodificar de base64
                encrypted_code = base64.urlsafe_b64decode(code_raw)
                
                # Definir la contraseña y la sal
                password = "Prado12@@Delgado12@@"
                salt = b"ZetaSalt2024"
                
                # Derivar la clave
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=480000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
                
                # Desencriptar
                fernet = Fernet(key)
                decrypted_code = fernet.decrypt(encrypted_code).decode('utf-8')
                
                # Mostrar el código base64
                self.base64_output.setText(decrypted_code)
                
                # Desencriptar a información original
                original_info = get_original_info(decrypted_code)
                
                # Formatear la información
                parts = original_info.split('|')
                if len(parts) >= 3:  # Formato nuevo: modelo, serial disco, fecha
                    formatted_info = (
                        f"Modelo: {parts[0]}\n"
                        f"Serial Disco: {parts[1]}\n"
                        f"Fecha: {parts[2]}"
                    )
                    self.info_output.setText(formatted_info)
                    
                    # Establecer la fecha en el selector
                    try:
                        date_str = parts[2]  # La fecha está en el índice 2
                        date = QDate.fromString(date_str, "yyyy-MM-dd")
                        if date.isValid():
                            self.date_edit.setDate(date)
                    except Exception as date_error:
                        print(f"Error estableciendo fecha: {date_error}")
                else:
                    self.info_output.setText("Formato de información no reconocido")
            
            except Exception as e:
                print(f"Error al desencriptar: {e}")
                import traceback
                traceback.print_exc()
                QMessageBox.critical(self, "Error", f"Error al desencriptar: {str(e)}")
        
        except Exception as e:
            print(f"Error general: {e}")
            QMessageBox.critical(self, "Error", f"Error general: {str(e)}")
    
    def generate_new_code(self):
        try:
            # Verificar que haya información desencriptada
            info_text = self.info_output.toPlainText()
            if not info_text:
                QMessageBox.warning(self, "Error", "No hay información para reencriptar")
                return
            
            # Extraer la información actual
            lines = info_text.split('\n')
            values = []
            for line in lines:
                if ':' in line:
                    value = line.split(':', 1)[1].strip()
                    values.append(value)
            
            # Verificar que tengamos al menos 3 valores
            if len(values) < 3:
                raise ValueError("Formato de información incompleto")
            
            # Obtener la nueva fecha del selector
            new_date = self.date_edit.date().toString("yyyy-MM-dd")
            
            # Actualizar la fecha en los valores
            values[2] = new_date  # La fecha siempre está en el índice 2
            
            # Reconstruir la información
            combined_info = '|'.join(values)
            
            # Actualizar la información mostrada
            formatted_info = (
                f"Modelo: {values[0]}\n"
                f"Serial Disco: {values[1]}\n"
                f"Fecha: {values[2]}"
            )
            
            # Codificar en base64
            base64_code = base64.urlsafe_b64encode(combined_info.encode('utf-8')).decode('utf-8')
            
            # Formatear el Base64 con guiones cada 4 caracteres
            formatted_base64 = '-'.join([base64_code[i:i+4] for i in range(0, len(base64_code), 4)])
            
            # Actualizar el campo base64
            self.base64_output.setText(formatted_base64)
            
            # Encriptar con AES
            password = "Prado12@@Delgado12@@"
            salt = b"ZetaSalt2024"
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=480000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            
            fernet = Fernet(key)
            encrypted_code = fernet.encrypt(base64_code.encode())
            
            # Codificar en base64 y formatear
            encoded_code = base64.urlsafe_b64encode(encrypted_code).decode('utf-8')
            formatted_code = '-'.join([encoded_code[i:i+4] for i in range(0, len(encoded_code), 4)])
            
            # Mostrar el nuevo código AES
            self.aes_input.setText(formatted_code)
            
            # Actualizar la información
            self.info_output.setText(formatted_info)
            
            QMessageBox.information(self, "Éxito", 
                f"Nueva licencia generada válida hasta: {new_date}\n"
                f"El código AES se ha actualizado en el campo superior."
            )
            
        except Exception as e:
            print(f"Error al generar nueva licencia: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"Error al generar nueva licencia: {str(e)}")
    
    def copy_aes(self):
        text = self.aes_input.toPlainText()
        if text:
            QApplication.clipboard().setText(text)
            QMessageBox.information(self, "Copiado", "Código AES copiado al portapapeles")
    
    def copy_base64(self):
        text = self.base64_output.toPlainText()
        if text:
            QApplication.clipboard().setText(text)
            QMessageBox.information(self, "Copiado", "Código Base64 copiado al portapapeles")
    
    def copy_info(self):
        text = self.info_output.toPlainText()
        if text:
            QApplication.clipboard().setText(text)
            QMessageBox.information(self, "Copiado", "Información copiada al portapapeles")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DesencriptadorWindow()
    window.show()
    sys.exit(app.exec())