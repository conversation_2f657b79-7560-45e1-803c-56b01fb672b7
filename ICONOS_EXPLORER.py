import os
import mimetypes
from pathlib import Path
from PyQt6.QtGui import QIcon
from CREAR import (registro, create_video_icon, create_audio_icon, create_word_icon, 
                  create_exe_icon, create_excel_icon, create_image_icon, create_compressed_icon,
                  create_ios_app_icon, create_pdf_icon, create_android_app_icon, icono_carpeta,
                  icono_OTROS)  # Añadir icono_OTROS a las importaciones

class FileTypeIconManager:
    def __init__(self):
        self._icon_cache = {}
        # Inicializar mimetypes
        mimetypes.init()
        
        # Definir categorías principales de archivos
        self.type_patterns = {
            'executable': {
                'mime_pattern': 'application/x-executable',
                'extensions': {'.exe', '.msi', '.bat', '.com', '.reg'},
                'icon': 'create_exe_icon'
            },
            'text': {
                'mime_pattern': 'text/*',
                'extensions': {'.txt', '.log', '.ini', '.cfg', '.dat', '.cmd', '.ps1'},
                'icon': 'registro'
            },
            'document': {
                'mime_pattern': 'application/*/document',
                'extensions': {'.doc', '.docx', '.odt', '.rtf'},
                'icon': 'create_word_icon'
            },
            'spreadsheet': {
                'mime_pattern': 'application/*/sheet',
                'extensions': {'.xls', '.xlsx', '.ods', '.csv'},
                'icon': 'create_excel_icon'
            },
            'image': {
                'mime_pattern': 'image/*',
                'extensions': {'.jpg', '.jpeg', '.png', '.gif', '.bmp'},
                'icon': 'create_image_icon'
            },
            'video': {
                'mime_pattern': 'video/*',
                'extensions': {'.mp4', '.avi', '.mkv', '.mov'},
                'icon': 'create_video_icon'
            },
            'audio': {
                'mime_pattern': 'audio/*',
                'extensions': {'.mp3', '.wav', '.ogg', '.m4a'},
                'icon': 'create_audio_icon'
            },
            'compressed': {
                'mime_pattern': 'application/*compressed',
                'extensions': {'.zip', '.rar', '.7z', '.tar.gz', '.iso', '.rdr'},
                'icon': 'create_compressed_icon'
            },
            'ios_app': {
                'mime_pattern': 'application/octet-stream',
                'extensions': {'.ipa'},
                'icon': 'create_ios_app_icon'
            },
            'android_app': {
                'mime_pattern': 'application/vnd.android.package-archive',
                'extensions': {'.apk'},
                'icon': 'create_android_app_icon'
            },
            'pdf': {
                'mime_pattern': 'application/pdf',
                'extensions': {'.pdf'},
                'icon': 'create_pdf_icon'
            }
        }

    def get_folder_icon(self):
        """Retorna el ícono para carpetas"""
        return icono_carpeta(size=24)

    def get_icon_for_file(self, file_path):
        """Obtiene el ícono apropiado para un archivo basado en su tipo."""
        if isinstance(file_path, str):
            file_path = Path(file_path)

        # Si es un directorio, retornar ícono de carpeta
        if file_path.is_dir():
            return icono_carpeta(size=24)

        # Obtener el mimetype del archivo
        mime_type, _ = mimetypes.guess_type(str(file_path))
        extension = file_path.suffix.lower()

        # Buscar en las categorías definidas
        for category in self.type_patterns.values():
            # Verificar por mimetype
            if mime_type and category['mime_pattern'].replace('*', '') in mime_type:
                return self._get_cached_icon(category['icon'])
            
            # Verificar por extensión
            if extension in category['extensions']:
                return self._get_cached_icon(category['icon'])

        # Si no se encuentra una categoría específica, usar ícono por defecto
        return self._get_cached_icon('icono_otros')

    def _get_cached_icon(self, icon_name):
        """Obtiene un ícono del caché o lo carga si no existe."""
        if icon_name not in self._icon_cache:
            if icon_name == 'registro':
                self._icon_cache[icon_name] = registro(size=24)
            elif icon_name == 'create_video_icon':
                self._icon_cache[icon_name] = create_video_icon(size=24)
            elif icon_name == 'create_audio_icon':
                self._icon_cache[icon_name] = create_audio_icon(size=24)
            elif icon_name == 'create_word_icon':
                self._icon_cache[icon_name] = create_word_icon(size=24)
            elif icon_name == 'create_exe_icon':
                self._icon_cache[icon_name] = create_exe_icon(size=24)
            elif icon_name == 'create_excel_icon':
                self._icon_cache[icon_name] = create_excel_icon(size=24)
            elif icon_name == 'create_image_icon':
                self._icon_cache[icon_name] = create_image_icon(size=24)
            elif icon_name == 'create_compressed_icon':
                self._icon_cache[icon_name] = create_compressed_icon(size=24)
            elif icon_name == 'create_ios_app_icon':
                self._icon_cache[icon_name] = create_ios_app_icon(size=24)
            elif icon_name == 'create_android_app_icon':
                self._icon_cache[icon_name] = create_android_app_icon(size=24)
            elif icon_name == 'create_pdf_icon':
                self._icon_cache[icon_name] = create_pdf_icon(size=24)
            elif icon_name == 'icono_otros':  # Añadir caso para icono_OTROS
                self._icon_cache[icon_name] = icono_OTROS(size=24)
            else:
                # Cargar desde archivo para los demás iconos
                icon_path = os.path.join(os.path.dirname(__file__), 'iconos', icon_name)
                self._icon_cache[icon_name] = QIcon(icon_path)
        return self._icon_cache[icon_name]
